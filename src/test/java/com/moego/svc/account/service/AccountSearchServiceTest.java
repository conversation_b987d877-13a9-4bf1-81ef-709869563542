package com.moego.svc.account.service;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.models.account.v1.AccountStatus;
import com.moego.idl.service.account.v1.SearchAccountRequest;
import com.moego.idl.utils.v2.NumberWrapper;
import com.moego.idl.utils.v2.Operator;
import com.moego.idl.utils.v2.OrderBy;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.idl.utils.v2.Predicate;
import com.moego.idl.utils.v2.StringWrapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

@SpringBootTest
@Transactional
@ActiveProfiles("unit-test")
public class AccountSearchServiceTest {

    @Autowired
    private AccountSearchService accountSearchService;

    @Test
    public void testSearch() {
        var sourcePredicateBuilder = Predicate.newBuilder()
                .setFieldName("source")
                .setOperator(Operator.OPERATOR_EQ)
                .setString(StringWrapper.newBuilder().setValue("business").build());

        var statusPredicateBuilder = Predicate.newBuilder()
                .setFieldName("status")
                .setOperator(Operator.OPERATOR_EQ)
                .setNumber(NumberWrapper.newBuilder()
                        .setValue(AccountStatus.ACCOUNT_STATUS_ACTIVE_VALUE)
                        .build());

        var emailPredicateBuilder = Predicate.newBuilder()
                .setFieldName("email")
                .setOperator(Operator.OPERATOR_LIKE)
                .setString(StringWrapper.newBuilder().setValue("well").build());

        var namePredicateBuilder = Predicate.newBuilder()
                .setFieldName("name")
                .setOperator(Operator.OPERATOR_LIKE)
                .setString(StringWrapper.newBuilder().setValue("well").build());

        // (email ilike 'well' or name ilike 'well') and status = 1 and source = 'business'
        // => ((email ilike 'well' or name ilike 'well') and status = 1) and source = 'business'
        emailPredicateBuilder.setOr(namePredicateBuilder.build());
        statusPredicateBuilder.setAnd(emailPredicateBuilder.build());
        sourcePredicateBuilder.setAnd(statusPredicateBuilder.build());
        var predicate = sourcePredicateBuilder.build();

        var orderBy = OrderBy.newBuilder().setFieldName("email").setAsc(true).build();

        var pagination =
                PaginationRequest.newBuilder().setPageNum(1).setPageSize(10).build();

        var request = SearchAccountRequest.newBuilder()
                .setPredicate(predicate)
                .addOrderBys(orderBy)
                .setPagination(pagination)
                .build();

        var result = accountSearchService.searchV2(request);

        result.getAccountDTOList().forEach(accountDTO -> {
            boolean emailContains = accountDTO.getEmail().toLowerCase().contains("well");
            boolean nameContains = accountDTO.getFirstName().toLowerCase().contains("well")
                    || accountDTO.getLastName().toLowerCase().contains("well");
            assertThat(emailContains || nameContains).isTrue();
            assertThat(accountDTO.getStatus().getValue()).isEqualTo(AccountStatus.ACCOUNT_STATUS_ACTIVE_VALUE);
        });
    }
}

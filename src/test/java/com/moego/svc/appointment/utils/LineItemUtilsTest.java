package com.moego.svc.appointment.utils;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.fulfillment.v1.LineItem;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v2.PricingRule;
import com.moego.idl.models.offering.v2.RuleType;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.lib.common.proto.MoneyUtils;
import com.moego.svc.appointment.domain.BoardingSplitLodging;
import com.moego.svc.appointment.domain.EvaluationServiceDetail;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.domain.PricingRuleRecordApplyLog;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

class LineItemUtilsTest {

    @Nested
    @DisplayName("buildLineItems for MoeGroomingPetDetail")
    class BuildLineItemsForMoeGroomingPetDetailTest {

        @Test
        @DisplayName("should build single line item when no pricing rules and no split lodgings")
        void shouldBuildSingleLineItemWhenNoPricingRulesAndNoSplitLodgings() {
            // Arrange
            MoeGroomingPetDetail petDetail = createMoeGroomingPetDetail(
                    BigDecimal.valueOf(100.00),
                    2,
                    BigDecimal.valueOf(200.00),
                    null,
                    "2025-05-20",
                    "2025-05-22",
                    PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE);
            StaffModel staff = createStaffModel("John");
            Map<Long, LodgingUnitModel> idToLodging = new HashMap<>();
            Map<Long, LodgingTypeModel> idToLodgingType = new HashMap<>();
            List<BoardingSplitLodging> splitLodgings = new ArrayList<>();
            String timeZoneName = "America/New_York";
            List<PricingRuleRecordApplyLog> pricingRuleApplyLogs = new ArrayList<>();

            // Act
            List<LineItem> result = LineItemUtils.buildLineItems(
                    petDetail, staff, idToLodging, idToLodgingType, splitLodgings, timeZoneName, pricingRuleApplyLogs);

            // Assert
            assertThat(result).hasSize(1);
            LineItem lineItem = result.get(0);
            assertThat(lineItem.getItemName()).isEqualTo("Service - John");
            assertThat(lineItem.getUnitPrice()).isEqualTo(MoneyUtils.toGoogleMoney(BigDecimal.valueOf(100.00)));
            assertThat(lineItem.getQuantity()).isEqualTo(2);
            assertThat(lineItem.getLineTotal()).isEqualTo(MoneyUtils.toGoogleMoney(BigDecimal.valueOf(200.00)));
            assertThat(lineItem.getItemType()).isEqualTo(LineItem.LineItemType.SERVICE_PRICE);
        }

        @Test
        @DisplayName("should build line items with lodging information when lodging exists")
        void shouldBuildLineItemsWithLodgingWhenLodgingExists() {
            // Arrange
            Long lodgingId = 123L;
            Long lodgingTypeId = 456L;
            MoeGroomingPetDetail petDetail = createMoeGroomingPetDetail(
                    BigDecimal.valueOf(150.00),
                    1,
                    BigDecimal.valueOf(150.00),
                    lodgingId,
                    "2025-05-20",
                    "2025-05-21",
                    PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE);

            Map<Long, LodgingUnitModel> idToLodging = createLodgingUnitMap(lodgingId, lodgingTypeId, "Premium Suite");
            Map<Long, LodgingTypeModel> idToLodgingType = createLodgingTypeMap(lodgingTypeId, "Luxury");

            // Act
            List<LineItem> result = LineItemUtils.buildLineItems(
                    petDetail, null, idToLodging, idToLodgingType, new ArrayList<>(), "UTC", new ArrayList<>());

            // Assert
            assertThat(result).hasSize(1);
            LineItem lineItem = result.get(0);
            assertThat(lineItem.getItemName()).isEqualTo("Service - Premium Suite(Luxury)");
            assertThat(lineItem.getUnitPrice()).isEqualTo(MoneyUtils.toGoogleMoney(BigDecimal.valueOf(150.00)));
            assertThat(lineItem.getQuantity()).isEqualTo(1);
            assertThat(lineItem.getLineTotal()).isEqualTo(MoneyUtils.toGoogleMoney(BigDecimal.valueOf(150.00)));
        }

        @Test
        @DisplayName("should build multiple line items when split lodgings exist without pricing rules")
        void shouldBuildMultipleLineItemsWhenSplitLodgingsExistWithoutPricingRules() {
            // Arrange
            MoeGroomingPetDetail petDetail = createMoeGroomingPetDetail(
                    BigDecimal.valueOf(100.00),
                    3,
                    BigDecimal.valueOf(300.00),
                    null,
                    "2025-05-20",
                    "2025-05-23",
                    PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE);

            List<BoardingSplitLodging> splitLodgings = createSplitLodgings();
            String timeZoneName = "UTC";

            // Act
            List<LineItem> result = LineItemUtils.buildLineItems(
                    petDetail, null, new HashMap<>(), new HashMap<>(), splitLodgings, timeZoneName, new ArrayList<>());

            // Assert
            assertThat(result).hasSize(2);

            LineItem firstItem = result.get(0);
            assertThat(firstItem.getItemName()).isEqualTo("Service");
            assertThat(firstItem.getUnitPrice()).isEqualTo(MoneyUtils.toGoogleMoney(BigDecimal.valueOf(80.00)));
            assertThat(firstItem.getQuantity()).isEqualTo(2);
            assertThat(firstItem.getLineTotal()).isEqualTo(MoneyUtils.toGoogleMoney(BigDecimal.valueOf(160.00)));

            LineItem secondItem = result.get(1);
            assertThat(secondItem.getItemName()).isEqualTo("Service");
            assertThat(secondItem.getUnitPrice()).isEqualTo(MoneyUtils.toGoogleMoney(BigDecimal.valueOf(120.00)));
            assertThat(secondItem.getQuantity()).isEqualTo(1);
            assertThat(secondItem.getLineTotal()).isEqualTo(MoneyUtils.toGoogleMoney(BigDecimal.valueOf(120.00)));
        }

        @Test
        @DisplayName("should build service and pricing rule line items when pricing rules exist")
        void shouldBuildServiceAndPricingRuleLineItemsWhenPricingRulesExist() {
            // Arrange
            MoeGroomingPetDetail petDetail = createMoeGroomingPetDetail(
                    BigDecimal.valueOf(100.00),
                    2,
                    BigDecimal.valueOf(200.00),
                    null,
                    "2025-05-20",
                    "2025-05-22",
                    PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE);

            List<PricingRuleRecordApplyLog> pricingRuleApplyLogs = createPricingRuleApplyLogs();

            // Act
            List<LineItem> result = LineItemUtils.buildLineItems(
                    petDetail, null, new HashMap<>(), new HashMap<>(), new ArrayList<>(), "UTC", pricingRuleApplyLogs);

            // Assert
            assertThat(result).hasSize(2); // 2 service items + 1 pricing rule adjustment

            // Check service items
            List<LineItem> serviceItems = result.stream()
                    .filter(item -> item.getItemType() == LineItem.LineItemType.SERVICE_PRICE)
                    .toList();
            assertThat(serviceItems).hasSize(1);

            // Check pricing rule adjustment
            List<LineItem> pricingRuleItems = result.stream()
                    .filter(item -> item.getItemType() == LineItem.LineItemType.PRICING_RULE_ADJUSTMENT)
                    .toList();
            assertThat(pricingRuleItems).hasSize(1);

            LineItem pricingRuleItem = pricingRuleItems.get(0);
            assertThat(pricingRuleItem.getItemName()).isEqualTo("Multiple pets");
            assertThat(pricingRuleItem.getUnitPrice()).isEqualTo(MoneyUtils.toGoogleMoney(BigDecimal.valueOf(-10.00)));
            assertThat(pricingRuleItem.getQuantity()).isEqualTo(2);
        }

        @Test
        @DisplayName("should prioritize split lodgings over pricing rules when both exist")
        void shouldPrioritizeSplitLodgingsOverPricingRulesWhenBothExist() {
            // Arrange
            MoeGroomingPetDetail petDetail = createMoeGroomingPetDetail(
                    BigDecimal.valueOf(100.00),
                    3,
                    BigDecimal.valueOf(300.00),
                    null,
                    "2025-05-20",
                    "2025-05-23",
                    PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE);

            List<BoardingSplitLodging> splitLodgings = createSplitLodgings();
            List<PricingRuleRecordApplyLog> pricingRuleApplyLogs = createPricingRuleApplyLogs();

            // Act
            List<LineItem> result = LineItemUtils.buildLineItems(
                    petDetail, null, new HashMap<>(), new HashMap<>(), splitLodgings, "UTC", pricingRuleApplyLogs);

            // Assert
            // Should use split lodgings for service items + pricing rules
            List<LineItem> serviceItems = result.stream()
                    .filter(item -> item.getItemType() == LineItem.LineItemType.SERVICE_PRICE)
                    .toList();
            assertThat(serviceItems).hasSize(2); // From split lodgings

            List<LineItem> pricingRuleItems = result.stream()
                    .filter(item -> item.getItemType() == LineItem.LineItemType.PRICING_RULE_ADJUSTMENT)
                    .toList();
            assertThat(pricingRuleItems).hasSize(1); // From pricing rules
        }

        @Test
        @DisplayName("should handle checkout day inclusion correctly")
        void shouldHandleCheckoutDayInclusionCorrectly() {
            // Arrange
            MoeGroomingPetDetail petDetail = createMoeGroomingPetDetail(
                    BigDecimal.valueOf(100.00),
                    3,
                    BigDecimal.valueOf(300.00),
                    null,
                    "2025-05-20",
                    "2025-05-22", // End date matches split lodging end date
                    PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY_VALUE);

            List<BoardingSplitLodging> splitLodgings = List.of(createBoardingSplitLodging(
                    LocalDateTime.of(2025, 5, 20, 0, 0),
                    LocalDateTime.of(2025, 5, 22, 0, 0), // Same as pet detail end date
                    BigDecimal.valueOf(100.00)));

            // Act
            List<LineItem> result = LineItemUtils.buildLineItems(
                    petDetail, null, new HashMap<>(), new HashMap<>(), splitLodgings, "UTC", new ArrayList<>());

            // Assert
            assertThat(result).hasSize(1);
            LineItem lineItem = result.get(0);
            assertThat(lineItem.getQuantity()).isEqualTo(3); // 2 days + 1 checkout day = 3
        }
    }

    // Helper methods for creating test data
    private MoeGroomingPetDetail createMoeGroomingPetDetail(
            BigDecimal servicePrice,
            Integer quantity,
            BigDecimal totalPrice,
            Long lodgingId,
            String startDate,
            String endDate,
            int dateType) {
        MoeGroomingPetDetail detail = new MoeGroomingPetDetail();
        detail.setServicePrice(servicePrice);
        detail.setQuantity(quantity);
        detail.setTotalPrice(totalPrice);
        detail.setLodgingId(lodgingId);
        detail.setStartDate(startDate);
        detail.setEndDate(endDate);
        detail.setDateType(dateType);
        return detail;
    }

    private EvaluationServiceDetail createEvaluationServiceDetail(BigDecimal servicePrice) {
        EvaluationServiceDetail detail = new EvaluationServiceDetail();
        detail.setServicePrice(servicePrice);
        return detail;
    }

    private StaffModel createStaffModel(String firstName) {
        return StaffModel.newBuilder().setFirstName(firstName).build();
    }

    private Map<Long, LodgingUnitModel> createLodgingUnitMap(Long lodgingId, Long lodgingTypeId, String name) {
        Map<Long, LodgingUnitModel> map = new HashMap<>();
        LodgingUnitModel lodging = LodgingUnitModel.newBuilder()
                .setLodgingTypeId(lodgingTypeId)
                .setName(name)
                .build();
        map.put(lodgingId, lodging);
        return map;
    }

    private Map<Long, LodgingTypeModel> createLodgingTypeMap(Long lodgingTypeId, String name) {
        Map<Long, LodgingTypeModel> map = new HashMap<>();
        LodgingTypeModel lodgingType =
                LodgingTypeModel.newBuilder().setName(name).build();
        map.put(lodgingTypeId, lodgingType);
        return map;
    }

    private LodgingUnitModel createLodgingUnitModel(Long lodgingTypeId, String name) {
        return LodgingUnitModel.newBuilder()
                .setLodgingTypeId(lodgingTypeId)
                .setName(name)
                .build();
    }

    private List<BoardingSplitLodging> createSplitLodgings() {
        return List.of(
                createBoardingSplitLodging(
                        LocalDateTime.of(2025, 5, 20, 0, 0),
                        LocalDateTime.of(2025, 5, 22, 0, 0),
                        BigDecimal.valueOf(80.00)),
                createBoardingSplitLodging(
                        LocalDateTime.of(2025, 5, 22, 0, 0),
                        LocalDateTime.of(2025, 5, 23, 0, 0),
                        BigDecimal.valueOf(120.00)));
    }

    private BoardingSplitLodging createBoardingSplitLodging(
            LocalDateTime startDateTime, LocalDateTime endDateTime, BigDecimal price) {
        BoardingSplitLodging splitLodging = new BoardingSplitLodging();
        splitLodging.setStartDateTime(startDateTime);
        splitLodging.setEndDateTime(endDateTime);
        splitLodging.setPrice(price);
        return splitLodging;
    }

    private List<PricingRuleRecordApplyLog> createPricingRuleApplyLogs() {
        PricingRule multiplePetRule =
                PricingRule.newBuilder().setType(RuleType.MULTIPLE_PET).build();

        return List.of(
                createPricingRuleRecordApplyLog(
                        "2025-05-20", BigDecimal.valueOf(100.00), BigDecimal.valueOf(90.00), true, multiplePetRule),
                createPricingRuleRecordApplyLog(
                        "2025-05-21", BigDecimal.valueOf(100.00), BigDecimal.valueOf(90.00), true, multiplePetRule));
    }

    private PricingRuleRecordApplyLog createPricingRuleRecordApplyLog(
            String serviceDate,
            BigDecimal originalPrice,
            BigDecimal adjustedPrice,
            Boolean isUsingRule,
            PricingRule pricingRule) {
        PricingRuleRecordApplyLog log = new PricingRuleRecordApplyLog();
        log.setServiceDate(serviceDate);
        log.setOriginalPrice(originalPrice);
        log.setAdjustedPrice(adjustedPrice);
        log.setIsUsingRule(isUsingRule);
        log.setPricingRule(pricingRule);
        return log;
    }

    @Nested
    @DisplayName("buildLineItems for EvaluationServiceDetail")
    class BuildLineItemsForEvaluationServiceDetailTest {

        @Test
        @DisplayName("should build single line item for evaluation service without staff or lodging")
        void shouldBuildSingleLineItemForEvaluationServiceWithoutStaffOrLodging() {
            // Arrange
            EvaluationServiceDetail evaluationDetail = createEvaluationServiceDetail(BigDecimal.valueOf(50.00));

            // Act
            List<LineItem> result = LineItemUtils.buildLineItems(evaluationDetail, null, null, new HashMap<>());

            // Assert
            assertThat(result).hasSize(1);
            LineItem lineItem = result.get(0);
            assertThat(lineItem.getItemName()).isEqualTo("Service");
            assertThat(lineItem.getUnitPrice()).isEqualTo(MoneyUtils.toGoogleMoney(BigDecimal.valueOf(50.00)));
            assertThat(lineItem.getQuantity()).isEqualTo(1);
            assertThat(lineItem.getLineTotal()).isEqualTo(MoneyUtils.toGoogleMoney(BigDecimal.valueOf(50.00)));
            assertThat(lineItem.getItemType()).isEqualTo(LineItem.LineItemType.SERVICE_PRICE);
        }

        @Test
        @DisplayName("should build line item with staff name when staff is provided")
        void shouldBuildLineItemWithStaffNameWhenStaffIsProvided() {
            // Arrange
            EvaluationServiceDetail evaluationDetail = createEvaluationServiceDetail(BigDecimal.valueOf(75.00));
            StaffModel staff = createStaffModel("Alice");

            // Act
            List<LineItem> result = LineItemUtils.buildLineItems(evaluationDetail, staff, null, new HashMap<>());

            // Assert
            assertThat(result).hasSize(1);
            LineItem lineItem = result.get(0);
            assertThat(lineItem.getItemName()).isEqualTo("Service - Alice");
            assertThat(lineItem.getUnitPrice()).isEqualTo(MoneyUtils.toGoogleMoney(BigDecimal.valueOf(75.00)));
        }

        @Test
        @DisplayName("should build line item with lodging information when lodging is provided")
        void shouldBuildLineItemWithLodgingWhenLodgingIsProvided() {
            // Arrange
            EvaluationServiceDetail evaluationDetail = createEvaluationServiceDetail(BigDecimal.valueOf(100.00));
            Long lodgingTypeId = 789L;
            LodgingUnitModel lodging = createLodgingUnitModel(lodgingTypeId, "Deluxe Room");
            Map<Long, LodgingTypeModel> idToLodgingType = createLodgingTypeMap(lodgingTypeId, "Premium");

            // Act
            List<LineItem> result = LineItemUtils.buildLineItems(evaluationDetail, null, lodging, idToLodgingType);

            // Assert
            assertThat(result).hasSize(1);
            LineItem lineItem = result.get(0);
            assertThat(lineItem.getItemName()).isEqualTo("Service - Deluxe Room(Premium)");
            assertThat(lineItem.getUnitPrice()).isEqualTo(MoneyUtils.toGoogleMoney(BigDecimal.valueOf(100.00)));
        }

        @Test
        @DisplayName("should prioritize lodging over staff when both are provided")
        void shouldPrioritizeLodgingOverStaffWhenBothAreProvided() {
            // Arrange
            EvaluationServiceDetail evaluationDetail = createEvaluationServiceDetail(BigDecimal.valueOf(125.00));
            StaffModel staff = createStaffModel("Bob");
            Long lodgingTypeId = 999L;
            LodgingUnitModel lodging = createLodgingUnitModel(lodgingTypeId, "VIP Suite");
            Map<Long, LodgingTypeModel> idToLodgingType = createLodgingTypeMap(lodgingTypeId, "Executive");

            // Act
            List<LineItem> result = LineItemUtils.buildLineItems(evaluationDetail, staff, lodging, idToLodgingType);

            // Assert
            assertThat(result).hasSize(1);
            LineItem lineItem = result.get(0);
            assertThat(lineItem.getItemName()).isEqualTo("Service - VIP Suite(Executive)");
            // Should use lodging name, not staff name
        }
    }

    @Nested
    @DisplayName("getLineItemName with MoeGroomingPetDetail")
    class GetLineItemNameWithMoeGroomingPetDetailTest {

        @Test
        @DisplayName("should return lodging service name when lodging exists")
        void shouldReturnLodgingServiceNameWhenLodgingExists() {
            // Arrange
            Long lodgingId = 123L;
            Long lodgingTypeId = 456L;
            MoeGroomingPetDetail detail = createMoeGroomingPetDetail(lodgingId);
            StaffModel staff = createStaffModel("John");
            Map<Long, LodgingUnitModel> idToLodging = createLodgingUnitMap(lodgingId, lodgingTypeId, "Premium Suite");
            Map<Long, LodgingTypeModel> idToLodgingType = createLodgingTypeMap(lodgingTypeId, "Luxury");

            // Act
            String result = LineItemUtils.getLineItemName(detail, staff, idToLodging, idToLodgingType);

            // Assert
            assertThat(result).isEqualTo("Service - Premium Suite(Luxury)");
        }

        @Test
        @DisplayName("should return staff service name when no lodging but staff exists")
        void shouldReturnStaffServiceNameWhenNoLodgingButStaffExists() {
            // Arrange
            MoeGroomingPetDetail detail = createMoeGroomingPetDetail(null);
            StaffModel staff = createStaffModel("Alice");
            Map<Long, LodgingUnitModel> idToLodging = new HashMap<>();
            Map<Long, LodgingTypeModel> idToLodgingType = new HashMap<>();

            // Act
            String result = LineItemUtils.getLineItemName(detail, staff, idToLodging, idToLodgingType);

            // Assert
            assertThat(result).isEqualTo("Service - Alice");
        }

        @Test
        @DisplayName("should return normal service name when no lodging and no staff")
        void shouldReturnNormalServiceNameWhenNoLodgingAndNoStaff() {
            // Arrange
            MoeGroomingPetDetail detail = createMoeGroomingPetDetail(null);
            Map<Long, LodgingUnitModel> idToLodging = new HashMap<>();
            Map<Long, LodgingTypeModel> idToLodgingType = new HashMap<>();

            // Act
            String result = LineItemUtils.getLineItemName(detail, null, idToLodging, idToLodgingType);

            // Assert
            assertThat(result).isEqualTo("Service");
        }

        @Test
        @DisplayName("should return staff service name when lodging ID not found in map")
        void shouldReturnStaffServiceNameWhenLodgingIdNotFoundInMap() {
            // Arrange
            Long nonExistentLodgingId = 999L;
            MoeGroomingPetDetail detail = createMoeGroomingPetDetail(nonExistentLodgingId);
            StaffModel staff = createStaffModel("Bob");
            Map<Long, LodgingUnitModel> idToLodging = new HashMap<>(); // Empty map
            Map<Long, LodgingTypeModel> idToLodgingType = new HashMap<>();

            // Act
            String result = LineItemUtils.getLineItemName(detail, staff, idToLodging, idToLodgingType);

            // Assert
            assertThat(result).isEqualTo("Service - Bob");
        }

        @Test
        @DisplayName("should handle lodging with missing lodging type gracefully")
        void shouldHandleLodgingWithMissingLodgingTypeGracefully() {
            // Arrange
            Long lodgingId = 123L;
            Long nonExistentLodgingTypeId = 999L;
            MoeGroomingPetDetail detail = createMoeGroomingPetDetail(lodgingId);
            Map<Long, LodgingUnitModel> idToLodging =
                    createLodgingUnitMap(lodgingId, nonExistentLodgingTypeId, "Basic Room");
            Map<Long, LodgingTypeModel> idToLodgingType = new HashMap<>(); // Empty map

            // Act
            String result = LineItemUtils.getLineItemName(detail, null, idToLodging, idToLodgingType);

            // Assert
            // Should use default instance which has empty name
            assertThat(result).isEqualTo("Service - Basic Room()");
        }

        @Test
        @DisplayName("should prioritize lodging over staff when both exist")
        void shouldPrioritizeLodgingOverStaffWhenBothExist() {
            // Arrange
            Long lodgingId = 123L;
            Long lodgingTypeId = 456L;
            MoeGroomingPetDetail detail = createMoeGroomingPetDetail(lodgingId);
            StaffModel staff = createStaffModel("Charlie");
            Map<Long, LodgingUnitModel> idToLodging = createLodgingUnitMap(lodgingId, lodgingTypeId, "Executive Suite");
            Map<Long, LodgingTypeModel> idToLodgingType = createLodgingTypeMap(lodgingTypeId, "Premium");

            // Act
            String result = LineItemUtils.getLineItemName(detail, staff, idToLodging, idToLodgingType);

            // Assert
            assertThat(result).isEqualTo("Service - Executive Suite(Premium)");
            // Should not contain staff name
            assertThat(result).doesNotContain("Charlie");
        }
    }

    @Nested
    @DisplayName("getLineItemName with direct parameters")
    class GetLineItemNameWithDirectParametersTest {

        @Test
        @DisplayName("should return lodging service name when lodging is provided")
        void shouldReturnLodgingServiceNameWhenLodgingIsProvided() {
            // Arrange
            Long lodgingTypeId = 789L;
            StaffModel staff = createStaffModel("Diana");
            LodgingUnitModel lodging = createLodgingUnitModel(lodgingTypeId, "Deluxe Room");
            Map<Long, LodgingTypeModel> idToLodgingType = createLodgingTypeMap(lodgingTypeId, "Standard");

            // Act
            String result = LineItemUtils.getLineItemName(staff, lodging, idToLodgingType);

            // Assert
            assertThat(result).isEqualTo("Service - Deluxe Room(Standard)");
        }

        @Test
        @DisplayName("should return staff service name when no lodging but staff exists")
        void shouldReturnStaffServiceNameWhenNoLodgingButStaffExists() {
            // Arrange
            StaffModel staff = createStaffModel("Eve");
            Map<Long, LodgingTypeModel> idToLodgingType = new HashMap<>();

            // Act
            String result = LineItemUtils.getLineItemName(staff, null, idToLodgingType);

            // Assert
            assertThat(result).isEqualTo("Service - Eve");
        }

        @Test
        @DisplayName("should return normal service name when no lodging and no staff")
        void shouldReturnNormalServiceNameWhenNoLodgingAndNoStaff() {
            // Arrange
            Map<Long, LodgingTypeModel> idToLodgingType = new HashMap<>();

            // Act
            String result = LineItemUtils.getLineItemName(null, null, idToLodgingType);

            // Assert
            assertThat(result).isEqualTo("Service");
        }

        @Test
        @DisplayName("should handle lodging with missing lodging type gracefully")
        void shouldHandleLodgingWithMissingLodgingTypeGracefully() {
            // Arrange
            Long nonExistentLodgingTypeId = 999L;
            LodgingUnitModel lodging = createLodgingUnitModel(nonExistentLodgingTypeId, "Economy Room");
            Map<Long, LodgingTypeModel> idToLodgingType = new HashMap<>(); // Empty map

            // Act
            String result = LineItemUtils.getLineItemName(null, lodging, idToLodgingType);

            // Assert
            // Should use default instance which has empty name
            assertThat(result).isEqualTo("Service - Economy Room()");
        }

        @Test
        @DisplayName("should prioritize lodging over staff when both exist")
        void shouldPrioritizeLodgingOverStaffWhenBothExist() {
            // Arrange
            Long lodgingTypeId = 555L;
            StaffModel staff = createStaffModel("Frank");
            LodgingUnitModel lodging = createLodgingUnitModel(lodgingTypeId, "VIP Suite");
            Map<Long, LodgingTypeModel> idToLodgingType = createLodgingTypeMap(lodgingTypeId, "Executive");

            // Act
            String result = LineItemUtils.getLineItemName(staff, lodging, idToLodgingType);

            // Assert
            assertThat(result).isEqualTo("Service - VIP Suite(Executive)");
            // Should not contain staff name
            assertThat(result).doesNotContain("Frank");
        }

        @Test
        @DisplayName("should handle lodging with empty name")
        void shouldHandleLodgingWithEmptyName() {
            // Arrange
            Long lodgingTypeId = 666L;
            LodgingUnitModel lodging = createLodgingUnitModel(lodgingTypeId, "");
            Map<Long, LodgingTypeModel> idToLodgingType = createLodgingTypeMap(lodgingTypeId, "Budget");

            // Act
            String result = LineItemUtils.getLineItemName(null, lodging, idToLodgingType);

            // Assert
            assertThat(result).isEqualTo("Service - (Budget)");
        }

        @Test
        @DisplayName("should handle both lodging and lodging type with empty names")
        void shouldHandleBothLodgingAndLodgingTypeWithEmptyNames() {
            // Arrange
            Long lodgingTypeId = 777L;
            LodgingUnitModel lodging = createLodgingUnitModel(lodgingTypeId, "");
            Map<Long, LodgingTypeModel> idToLodgingType = createLodgingTypeMap(lodgingTypeId, "");

            // Act
            String result = LineItemUtils.getLineItemName(null, lodging, idToLodgingType);

            // Assert
            assertThat(result).isEqualTo("Service - ()");
        }
    }

    // Helper methods for creating test data
    private MoeGroomingPetDetail createMoeGroomingPetDetail(Long lodgingId) {
        MoeGroomingPetDetail detail = new MoeGroomingPetDetail();
        detail.setLodgingId(lodgingId);
        return detail;
    }
}

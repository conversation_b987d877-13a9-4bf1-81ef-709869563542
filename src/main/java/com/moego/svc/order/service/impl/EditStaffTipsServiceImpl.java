package com.moego.svc.order.service.impl;

import com.moego.common.distributed.LockManager;
import com.moego.common.enums.order.OrderItemType;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.order.v1.EditStaffCommissionItem;
import com.moego.idl.models.order.v1.EditStaffCommissionOperationItem;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.models.order.v1.SplitTipsMethod;
import com.moego.idl.models.order.v1.StaffChangeLogAmountType;
import com.moego.idl.models.order.v1.StaffChangeLogRelateType;
import com.moego.idl.service.order.v1.EditStaffCommissionParams;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.utils.model.Pair;
import com.moego.server.business.client.IPayrollSettingClient;
import com.moego.server.business.dto.BusinessPayrollSettingDTO;
import com.moego.server.grooming.client.IGroomingPetDetailClient;
import com.moego.server.grooming.dto.GroomingPetDetailDTO;
import com.moego.server.grooming.dto.GroomingServiceOperationDTO;
import com.moego.svc.order.model.params.SaveSplitTipsRecordParams;
import com.moego.svc.order.repository.entity.Order;
import com.moego.svc.order.repository.entity.OrderGroomingDetailRel;
import com.moego.svc.order.repository.entity.OrderLineItem;
import com.moego.svc.order.repository.entity.OrderStaffSplitDetail;
import com.moego.svc.order.repository.entity.OrderTipsSplitDetail;
import com.moego.svc.order.repository.entity.StaffPayrollChangeLog;
import com.moego.svc.order.repository.mapper.OrderLineItemMapper;
import com.moego.svc.order.repository.mapper.OrderMapper;
import com.moego.svc.order.repository.mapper.OrderStaffSplitDetailMapper;
import com.moego.svc.order.repository.mapper.OrderTipSplitRecordMapper;
import com.moego.svc.order.repository.mapper.OrderTipsSplitDetailMapper;
import com.moego.svc.order.repository.mapper.StaffPayrollChangeLogMapper;
import com.moego.svc.order.service.EditStaffTipsService;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionOperations;
import org.springframework.util.CollectionUtils;

@Service
public class EditStaffTipsServiceImpl implements EditStaffTipsService {

    @Autowired
    private TransactionOperations transactionOperations;

    @Autowired
    private IGroomingPetDetailClient groomingPetDetailClient;

    @Autowired
    private IPayrollSettingClient iPayrollSettingClient;

    @Autowired
    private LockManager lockManager;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private OrderLineItemMapper orderLineItemMapper;

    @Autowired
    private OrderTipSplitRecordMapper orderTipSplitRecordMapper;

    @Autowired
    private OrderTipsSplitDetailMapper orderTipsSplitDetailMapper;

    @Autowired
    private OrderStaffSplitDetailMapper orderStaffSplitDetailMapper;

    @Autowired
    private StaffPayrollChangeLogMapper staffPayrollChangeLogMapper;

    @Autowired
    private OrderGroomingDetailRelService orderGroomingDetailRelService;

    /**
     * @see EditStaffTipsService#saveEditStaff(EditStaffCommissionParams)
     */
    @Override
    public void saveEditStaff(EditStaffCommissionParams request) {
        // 这里考虑换人和换split rate，其余不变
        long orderId = request.getOrderId();
        // 0. 先判断订单是否存在
        Order order = orderMapper.selectByPrimaryKey(orderId);
        if (null == order) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "order not found, orderId=" + orderId);
        }
        List<EditStaffCommissionItem> editStaffCommissionItemsList = request.getEditStaffCommissionItemsList();
        if (CollectionUtils.isEmpty(editStaffCommissionItemsList)) {
            return;
        }

        BigDecimal paidAmount = order.getPaidAmount();
        BigDecimal tipsAmount = order.getTipsAmount();

        BigDecimal refundedAmount = order.getRefundedAmount();

        BigDecimal tipsRefundedAmount = BigDecimal.ZERO;
        // 只有paid amount大于0的时候才需要计算这些
        if (BigDecimal.ZERO.compareTo(paidAmount) < 0) {
            tipsRefundedAmount = refundedAmount.multiply(tipsAmount).divide(paidAmount, 2, RoundingMode.DOWN);
        }
        BigDecimal orderItemAmount = paidAmount.subtract(tipsAmount);
        BigDecimal orderItemRefundedAmount = refundedAmount.subtract(tipsRefundedAmount);

        List<OrderLineItem> orderLineItems = orderLineItemMapper.selectByOrderId(orderId);
        BigDecimal serviceItemTotalAmount = BigDecimal.ZERO;
        BigDecimal productItemTotalAmount = BigDecimal.ZERO;
        BigDecimal itemTotalAmount = BigDecimal.ZERO;
        for (OrderLineItem orderLineItem : orderLineItems) {
            if (OrderItemType.ITEM_TYPE_PRODUCT.getType().equalsIgnoreCase(orderLineItem.getType())) {
                productItemTotalAmount = productItemTotalAmount.add(orderLineItem.getSubTotalAmount());
            }
            if (OrderItemType.ITEM_TYPE_SERVICE.getType().equalsIgnoreCase(orderLineItem.getType())) {
                serviceItemTotalAmount = serviceItemTotalAmount.add(orderLineItem.getSubTotalAmount());
            }
            itemTotalAmount = itemTotalAmount.add(orderLineItem.getSubTotalAmount());
        }

        BigDecimal serviceItemPaidAmount = BigDecimal.ZERO;
        BigDecimal serviceItemRefundedAmount = BigDecimal.ZERO;
        BigDecimal productItemPaidAmount = BigDecimal.ZERO;
        BigDecimal productItemRefundedAmount = BigDecimal.ZERO;
        if (BigDecimal.ZERO.compareTo(itemTotalAmount) < 0) {
            serviceItemPaidAmount =
                    orderItemAmount.multiply(serviceItemTotalAmount).divide(itemTotalAmount, 2, RoundingMode.HALF_UP);
            serviceItemRefundedAmount = orderItemRefundedAmount
                    .multiply(serviceItemTotalAmount)
                    .divide(itemTotalAmount, 2, RoundingMode.HALF_UP);

            productItemPaidAmount =
                    orderItemAmount.multiply(productItemTotalAmount).divide(itemTotalAmount, 2, RoundingMode.HALF_UP);
            productItemRefundedAmount = orderItemRefundedAmount
                    .multiply(productItemTotalAmount)
                    .divide(itemTotalAmount, 2, RoundingMode.HALF_UP);
        }

        // service的和product的处理方式不一样
        saveProductEditStaff(order, editStaffCommissionItemsList, productItemPaidAmount, productItemRefundedAmount);
        saveServiceEditStaff(order, editStaffCommissionItemsList, serviceItemPaidAmount, serviceItemRefundedAmount);
    }

    /**
     * 处理product的edit staff
     */
    private void saveProductEditStaff(
            Order order,
            List<EditStaffCommissionItem> editStaffCommissionItemsList,
            BigDecimal productItemPaidAmount,
            BigDecimal productItemRefundedAmount) {

        List<EditStaffCommissionItem> pdEditStaffCommissionItemList = editStaffCommissionItemsList.stream()
                .filter(item -> OrderItemType.ITEM_TYPE_PRODUCT.getType().equalsIgnoreCase(item.getOrderItemType()))
                .toList();
        if (CollectionUtils.isEmpty(pdEditStaffCommissionItemList)) {
            return;
        }

        List<OrderLineItem> orderLineItems = orderLineItemMapper.selectByOrderIdsAndType(
                List.of(order.getId()), OrderItemType.ITEM_TYPE_PRODUCT.getType());
        if (CollectionUtils.isEmpty(orderLineItems)) {
            return;
        }

        Map<Long, BigDecimal> perOrderItemPaidAmountMap =
                buildPerOrderItemAmountByLineItem(productItemPaidAmount, orderLineItems);
        Map<Long, BigDecimal> perOrderItemRefundedAmountMap =
                buildPerOrderItemAmountByLineItem(productItemRefundedAmount, orderLineItems);

        // 金额是order line item的金额
        for (EditStaffCommissionItem pdEditStaffCommissionItem : pdEditStaffCommissionItemList) {
            long orderItemId = pdEditStaffCommissionItem.getOrderItemId();
            // 如果本来不存在这个line item，就不需要处理了
            OrderLineItem orderLineItemFromDB = orderLineItemMapper.selectByPrimaryKey(orderItemId);
            if (null == orderLineItemFromDB) {
                continue;
            }
            // 更新line里面的staff
            OrderLineItem orderLineItem = new OrderLineItem();
            orderLineItem.setId(orderItemId);
            orderLineItem.setStaffId(pdEditStaffCommissionItem.getStaffId());
            orderLineItemMapper.updateByPrimaryKeySelective(orderLineItem);

            executePerCommissionItem(
                    pdEditStaffCommissionItem,
                    order.getId(),
                    orderItemId,
                    OrderItemType.ITEM_TYPE_PRODUCT.getType(),
                    perOrderItemPaidAmountMap,
                    perOrderItemRefundedAmountMap,
                    order.getBusinessId());
        }
    }

    /**
     * 处理service的edit staff
     */
    private void saveServiceEditStaff(
            Order order,
            List<EditStaffCommissionItem> editStaffCommissionItemsList,
            BigDecimal serviceItemPaidAmount,
            BigDecimal serviceItemRefundedAmount) {

        List<EditStaffCommissionItem> serviceEditStaffCommissionItemList = editStaffCommissionItemsList.stream()
                .filter(item -> OrderItemType.ITEM_TYPE_SERVICE.getType().equalsIgnoreCase(item.getOrderItemType()))
                .toList();
        if (CollectionUtils.isEmpty(serviceEditStaffCommissionItemList)) {
            return;
        }
        List<GroomingPetDetailDTO> groomingPetDetailDTOList = getGroomingPetDetailDTOS(order);
        // 没有的本来就没有，不需要执行了
        if (CollectionUtils.isEmpty(groomingPetDetailDTOList)) {
            return;
        }

        // 计算每个item的支付金额
        Map<Long, BigDecimal> perOrderItemPaidAmountMap =
                buildPerOrderItemAmountByPetDetail(serviceItemPaidAmount, groomingPetDetailDTOList);
        // 计算每个item的退款金额
        Map<Long, BigDecimal> perOrderItemRefundedAmountMap =
                buildPerOrderItemAmountByPetDetail(serviceItemRefundedAmount, groomingPetDetailDTOList);

        // 这里一个pet detail，对应原来的一个edit staff detail 数据
        for (EditStaffCommissionItem editStaffCommissionItem : serviceEditStaffCommissionItemList) {
            long petDetailId = editStaffCommissionItem.getPetDetailId();
            executePerCommissionItem(
                    editStaffCommissionItem,
                    order.getId(),
                    petDetailId,
                    OrderItemType.ITEM_TYPE_SERVICE.getType(),
                    perOrderItemPaidAmountMap,
                    perOrderItemRefundedAmountMap,
                    order.getBusinessId());
        }
    }

    /**
     * 处理单个commission item
     */
    private void executePerCommissionItem(
            EditStaffCommissionItem editStaffCommissionItem,
            long orderId,
            long orderItemId,
            String orderItemType,
            Map<Long, BigDecimal> perOrderItemPaidAmountMap,
            Map<Long, BigDecimal> perOrderItemRefundedAmountMap,
            Long businessId) {
        // 原来已经存在的
        List<OrderStaffSplitDetail> orderStaffSplitDetailList =
                orderStaffSplitDetailMapper.selectByOrderIdAndItemId(orderId, orderItemId, orderItemType);
        Set<Long> oldStaffIdSet = new HashSet<>();
        Map<Long, OrderStaffSplitDetail> staffOrderSplitDetailMap = new HashMap<>();
        orderStaffSplitDetailList.forEach(orderStaffSplitDetail -> {
            oldStaffIdSet.add(orderStaffSplitDetail.getStaffId());
            staffOrderSplitDetailMap.put(orderStaffSplitDetail.getStaffId(), orderStaffSplitDetail);
        });
        BigDecimal curTotalAmount = perOrderItemPaidAmountMap.get(orderItemId);
        BigDecimal curRefundedAmount = perOrderItemRefundedAmountMap.get(orderItemId);
        // 新传入的
        Map<Long, BigDecimal> staffSplitRateMap = buildSplitRate(editStaffCommissionItem);
        Set<Long> newStaffIdSet = staffSplitRateMap.keySet();

        Set<Long> needUpdateStaffIdSet =
                newStaffIdSet.stream().filter(oldStaffIdSet::contains).collect(Collectors.toSet());
        Set<Long> needAddStaffIdSet = newStaffIdSet.stream()
                .filter(staffId -> !oldStaffIdSet.contains(staffId))
                .collect(Collectors.toSet());
        Set<Long> needDeleteStaffIdSet = oldStaffIdSet.stream()
                .filter(staffId -> !newStaffIdSet.contains(staffId))
                .collect(Collectors.toSet());
        needUpdateStaffIdSet.addAll(needDeleteStaffIdSet);

        //  type为OrderItemType.ITEM_TYPE_PRODUCT 时，根据 orderItemId 查询 productId
        Long objectId = editStaffCommissionItem.getServiceId();
        if (OrderItemType.ITEM_TYPE_PRODUCT.getType().equals(orderItemType)) {
            OrderLineItem orderLineItem = orderLineItemMapper.selectByPrimaryKey(orderItemId);
            if (orderLineItem != null) {
                objectId = orderLineItem.getObjectId();
            }
        }
        updateOrderStaffSplitDetail(
                needUpdateStaffIdSet,
                staffOrderSplitDetailMap,
                staffSplitRateMap,
                curTotalAmount,
                curRefundedAmount,
                BigDecimal.ZERO);
        addOrderStaffSplitDetail(
                orderId,
                orderItemId,
                orderItemType,
                businessId,
                needAddStaffIdSet,
                staffSplitRateMap,
                curTotalAmount,
                curRefundedAmount,
                BigDecimal.ZERO,
                editStaffCommissionItem.getPetId(),
                objectId);
    }

    /**
     * @see EditStaffTipsService#saveEditTips(SaveSplitTipsRecordParams)
     *
     * Yunxiang: 2025-03-13
     * 当前方法主要用于更新 order_tips_split_detail 表。
     * 在 BD War Room 的需求，支持自定义 Business Tips 的时候，这张表的数据 **应该** 要遵循分配规则来重算，
     * 但是：
     * 1. 这张表并没有地方在读取（Report 是通过 order tip split record 表计算）
     * 2. 这张表本身数据就有错误（Staff 信息不准确，Tips 分配规则也与 Report 不一样）
     * 3. 这张表在 按预约分配Tips 需求中已经废弃，并且先于 Business Tip 需求上线
     *
     * 基于上述几条，这个方法就不调整了，并且在后续迭代中直接干掉。
     */
    @Override
    public void saveEditTips(SaveSplitTipsRecordParams splitTipsRecordParams) {
        Long orderId = splitTipsRecordParams.getOrderId();
        // 0. 先判断订单是否存在
        Order order = orderMapper.selectByPrimaryKey(orderId);
        if (null == order) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "order not found, orderId=" + orderId);
        }

        BigDecimal paidAmount = order.getPaidAmount();
        BigDecimal tipsAmount = order.getTipsAmount();
        BigDecimal refundedAmount = order.getRefundedAmount();

        BigDecimal tipsRefundedAmount = BigDecimal.ZERO;
        if (BigDecimal.ZERO.compareTo(paidAmount) < 0) {
            tipsRefundedAmount = refundedAmount.multiply(tipsAmount).divide(paidAmount, 2, RoundingMode.DOWN);
        }

        // 这里直接分配人和钱
        SplitTipsMethod splitTipsMethod = SplitTipsMethod.forNumber(
                Optional.ofNullable(splitTipsRecordParams.getSplitMethod()).orElse(0));
        if (null == splitTipsMethod) {
            splitTipsMethod = SplitTipsMethod.SPLIT_TIPS_METHOD_UNSPECIFIED;
        }

        // 查出来关联的pet detail数据
        List<GroomingPetDetailDTO> groomingPetDetailDTOList = getGroomingPetDetailDTOS(order);
        // 没有的本来就没有，不需要执行了
        if (CollectionUtils.isEmpty(groomingPetDetailDTOList)) {
            return;
        }
        Set<Long> staffIdSet = getStaffIdSetByPetDetailList(groomingPetDetailDTOList);
        Map<Long, Pair<BigDecimal, BigDecimal>> tipsSplitRateAndAmountMap = new HashMap<>();

        switch (splitTipsMethod) {
            case SPLIT_TIPS_METHOD_BY_EQUALLY -> tipsSplitRateAndAmountMap =
                    buildSplitRateAndAmount(staffIdSet, tipsAmount.subtract(tipsRefundedAmount));
            case SPLIT_TIPS_METHOD_CUSTOMIZED -> {
                // 这里直接有钱和分配的比例了
                for (SaveSplitTipsRecordParams.CustomizedTipConfigParams param :
                        splitTipsRecordParams.getCustomizedConfigList()) {
                    tipsSplitRateAndAmountMap.put(
                            param.getStaffId(),
                            new Pair<>(
                                    new BigDecimal(param.getPercentage())
                                            .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP),
                                    param.getAmount()));
                }
            }
                // default 按照by service 处理
            default -> {
                tipsSplitRateAndAmountMap =
                        buildSplitRateAndAmount(tipsAmount.subtract(tipsRefundedAmount), groomingPetDetailDTOList);
            }
        }

        saveEditTipsDetail(
                orderId, order.getBusinessId(), tipsAmount, tipsRefundedAmount, staffIdSet, tipsSplitRateAndAmountMap);
    }

    /**
     * @see EditStaffTipsService#saveEditStaffAndEditTips(Long)
     */
    @Override
    public void saveEditStaffAndEditTips(Long orderId) {
        // 这里更新人和钱都有可能， 这个方法是pet detail更改会调用到，支付金额变动会调用到，只有关单前会调用到
        // 获取有哪些staff，以及每一个staff的commission rate

        transactionOperations.executeWithoutResult(transactionStatus -> {

            // 加锁
            String resourceKey = lockManager.getResourceKey(LockManager.EDIT_STAFF_TIPS_COMMISSION, orderId);
            String resourceValue = CommonUtil.getUuid();

            try {
                if (!lockManager.lockWithRetry(resourceKey, resourceValue)) {
                    throw ExceptionUtil.bizException(
                            Code.CODE_PARALLEL_ERROR, "get lock with retry failed for " + resourceKey);
                }
                // 0. 先判断订单是否存在
                Order order = orderMapper.selectByPrimaryKey(orderId);
                if (null == order) {
                    throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "order not found, orderId=" + orderId);
                }

                List<GroomingPetDetailDTO> groomingPetDetailDTOList = getGroomingPetDetailDTOS(order);
                deleteNotInGroomingStaffDetail(groomingPetDetailDTOList, orderId);

                BigDecimal paidAmount = order.getPaidAmount();
                BigDecimal tipsAmount = order.getTipsAmount();
                BigDecimal refundedAmount = order.getRefundedAmount();

                BigDecimal tipsRefundedAmount = BigDecimal.ZERO;
                // 只有paid amount大于0的时候才需要计算这些
                if (BigDecimal.ZERO.compareTo(paidAmount) < 0) {
                    tipsRefundedAmount = refundedAmount.multiply(tipsAmount).divide(paidAmount, 2, RoundingMode.DOWN);
                }

                // 1. 根据订单数据、支付数据构造payroll数据
                // 对于edit staff，需要感知到每一个pet detail的staff，以及对应的金额
                EditStaffCommissionParams params = buildEditStaffCommissionParams(orderId, groomingPetDetailDTOList);
                saveEditStaff(params);

                // 2.保存tips detail数据
                // 对于edit tips，只需要感知到一个order下所涉及的所有staff以及总金额
                // business payroll 设置 default split tips method
                Set<Long> staffIdSet = getStaffIdSetByPetDetailList(groomingPetDetailDTOList);
                BusinessPayrollSettingDTO businessPayrollSetting =
                        iPayrollSettingClient.getBusinessPayrollSetting(Math.toIntExact(order.getBusinessId()));
                Map<Long, Pair<BigDecimal, BigDecimal>> tipsSplitMap;
                if (businessPayrollSetting == null
                        || SplitTipsMethod.SPLIT_TIPS_METHOD_BY_SERVICE_VALUE
                                == businessPayrollSetting.getSplitTipsMethod()) {
                    // by service price
                    tipsSplitMap =
                            buildSplitRateAndAmount(tipsAmount.subtract(tipsRefundedAmount), groomingPetDetailDTOList);
                } else {
                    // by staff equally
                    tipsSplitMap = buildSplitRateAndAmount(staffIdSet, tipsAmount.subtract(tipsRefundedAmount));
                }

                saveEditTipsDetail(
                        orderId, order.getBusinessId(), tipsAmount, tipsRefundedAmount, staffIdSet, tipsSplitMap);
            } finally {
                lockManager.unlock(resourceKey, resourceValue);
            }
        });
    }

    /**
     * 构建edit staff commission 请求参数
     */
    private EditStaffCommissionParams buildEditStaffCommissionParams(
            Long orderId, List<GroomingPetDetailDTO> groomingPetDetailDTOList) {

        List<EditStaffCommissionItem> commissionItems = new ArrayList<>();
        List<OrderLineItem> pdOrderLineItems = orderLineItemMapper.selectByOrderIdsAndType(
                List.of(orderId), OrderItemType.ITEM_TYPE_PRODUCT.getType());
        for (GroomingPetDetailDTO groomingPetDetailDTO : groomingPetDetailDTOList) {
            EditStaffCommissionItem commissionItem = EditStaffCommissionItem.newBuilder()
                    .setOrderItemType(OrderItemType.ITEM_TYPE_SERVICE.getType())
                    .setPetDetailId(groomingPetDetailDTO.getId())
                    .setServiceId(groomingPetDetailDTO.getServiceId())
                    .setPetId(groomingPetDetailDTO.getPetId())
                    .setStaffId(groomingPetDetailDTO.getStaffId())
                    .addAllOperation(
                            Optional.ofNullable(groomingPetDetailDTO.getOperationList()).orElse(List.of()).stream()
                                    .map(op -> EditStaffCommissionOperationItem.newBuilder()
                                            .setStaffId(op.getStaffId())
                                            .setRatio(op.getPriceRatio().doubleValue())
                                            .setDuration(op.getDuration())
                                            .setOperationName(op.getOperationName())
                                            .build())
                                    .toList())
                    .build();
            commissionItems.add(commissionItem);
        }
        for (OrderLineItem pdOrderLineItem : pdOrderLineItems) {
            EditStaffCommissionItem commissionItem = EditStaffCommissionItem.newBuilder()
                    .setOrderItemType(OrderItemType.ITEM_TYPE_PRODUCT.getType())
                    .setStaffId(pdOrderLineItem.getStaffId())
                    .setOrderItemId(pdOrderLineItem.getId())
                    .build();
            commissionItems.add(commissionItem);
        }
        return EditStaffCommissionParams.newBuilder()
                .setOrderId(orderId)
                .addAllEditStaffCommissionItems(commissionItems)
                .build();
    }

    /**
     * 删除/更新 已经不存在grooming中的信息
     *
     * @param groomingPetDetailDTOList grooming当前有的pet detail信息
     * @param orderId                  订单id
     */
    private void deleteNotInGroomingStaffDetail(List<GroomingPetDetailDTO> groomingPetDetailDTOList, Long orderId) {

        Set<Long> petDetailIdSet = groomingPetDetailDTOList.stream()
                .map(dto -> Long.valueOf(dto.getId()))
                .collect(Collectors.toSet());
        // 查询当前有的staff split detail
        List<OrderStaffSplitDetail> alreadyExistStaffSplitDetailList =
                orderStaffSplitDetailMapper.selectByOrderId(orderId);
        if (CollectionUtils.isEmpty(alreadyExistStaffSplitDetailList)) {
            return;
        }

        for (OrderStaffSplitDetail orderStaffSplitDetail : alreadyExistStaffSplitDetailList) {
            if (!petDetailIdSet.contains(orderStaffSplitDetail.getOrderItemId())) {
                // 原来的detail 更新成0
                updateSingleStaffDetail(
                        orderStaffSplitDetail.getStaffId(),
                        orderStaffSplitDetail,
                        BigDecimal.ZERO,
                        orderStaffSplitDetail.getTotalAmount(),
                        orderStaffSplitDetail.getRefundedAmount(),
                        orderStaffSplitDetail.getCommissionRate());
            }
        }
    }

    /**
     * 构建split rate
     *
     * @param editStaffCommissionItem EditStaffCommissionItem
     * @return Map<staffId, splitRate>
     */
    private Map<Long, BigDecimal> buildSplitRate(EditStaffCommissionItem editStaffCommissionItem) {

        Map<Long, BigDecimal> map = new HashMap<>();
        List<EditStaffCommissionOperationItem> operationList = editStaffCommissionItem.getOperationList();
        if (CollectionUtils.isEmpty(operationList)) {
            map.put(editStaffCommissionItem.getStaffId(), BigDecimal.ONE);
            return map;
        }
        for (EditStaffCommissionOperationItem operationItem : operationList) {
            map.put(operationItem.getStaffId(), BigDecimal.valueOf(operationItem.getRatio()));
        }
        return map;
    }

    /**
     * 构造split rate和split amount 映射, by service amount
     *
     * @param amount                   金额
     * @param groomingPetDetailDTOList List<GroomingPetDetailDTO>
     * @return Map<Long, Pair < BigDecimal, BigDecimal>>
     */
    private Map<Long, Pair<BigDecimal, BigDecimal>> buildSplitRateAndAmount(
            BigDecimal amount, List<GroomingPetDetailDTO> groomingPetDetailDTOList) {

        BigDecimal totalServiceAmount = groomingPetDetailDTOList.stream()
                .map(GroomingPetDetailDTO::getServicePrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal allocatedTips = BigDecimal.ZERO;

        // 计算每个staff分配到的比例和金额
        Map<Long, Pair<BigDecimal, BigDecimal>> splitRateAndAmountMap = new HashMap<>();
        for (GroomingPetDetailDTO groomingPetDetailDTO : groomingPetDetailDTOList) {
            BigDecimal servicePrice = groomingPetDetailDTO.getServicePrice();
            Map<Long, BigDecimal> staffIdSplitRateMap = getStaffIdSplitRateByPetDetail(groomingPetDetailDTO);

            for (Map.Entry<Long, BigDecimal> entry : staffIdSplitRateMap.entrySet()) {
                Long staffId = entry.getKey();
                Pair<BigDecimal, BigDecimal> currentPair =
                        splitRateAndAmountMap.getOrDefault(staffId, new Pair<>(BigDecimal.ZERO, BigDecimal.ZERO));

                BigDecimal splitRate = BigDecimal.ZERO;
                BigDecimal allocatedAmount = BigDecimal.ZERO;
                if (BigDecimal.ZERO.compareTo(totalServiceAmount) < 0) {
                    // split rate = servicePrice * priceRatio / totalAmount
                    splitRate =
                            servicePrice.multiply(entry.getValue()).divide(totalServiceAmount, 4, RoundingMode.HALF_UP);

                    // split amount = amount * servicePrice * priceRatio / totalAmount
                    allocatedAmount = servicePrice
                            .multiply(entry.getValue())
                            .multiply(amount)
                            .divide(totalServiceAmount, 2, RoundingMode.HALF_UP);
                }
                splitRateAndAmountMap.put(
                        staffId,
                        Pair.of(
                                currentPair.key().add(splitRate),
                                currentPair.value().add(allocatedAmount)));
                allocatedTips = allocatedTips.add(allocatedAmount);
            }
        }

        // 处理轧差
        BigDecimal difference = amount.subtract(allocatedTips);
        if (difference.compareTo(BigDecimal.ZERO) != 0) {
            // 将差额加到某个staff的分配金额上
            Optional<Map.Entry<Long, Pair<BigDecimal, BigDecimal>>> entryWithMaxAmount =
                    splitRateAndAmountMap.entrySet().stream()
                            .max(Comparator.comparing(entry -> entry.getValue().value()));

            entryWithMaxAmount.ifPresent(entry -> {
                BigDecimal adjustedAmount = entry.getValue().value().add(difference);
                splitRateAndAmountMap.put(
                        entry.getKey(), Pair.of(entry.getValue().key(), adjustedAmount));
            });
        }

        return splitRateAndAmountMap;
    }

    /**
     * 根据pet detail 获取staff id集合
     *
     * @param groomingPetDetailDTOList List<GroomingPetDetailDTO>
     * @return staff id 集合
     */
    private static Set<Long> getStaffIdSetByPetDetailList(List<GroomingPetDetailDTO> groomingPetDetailDTOList) {

        return groomingPetDetailDTOList.stream()
                .flatMap(groomingPetDetailDTO -> {
                    List<GroomingServiceOperationDTO> operationList = groomingPetDetailDTO.getOperationList();
                    if (CollectionUtils.isEmpty(operationList)) {
                        return Stream.of(Long.valueOf(groomingPetDetailDTO.getStaffId()));
                    }
                    return operationList.stream().map(operation -> Long.valueOf(operation.getStaffId()));
                })
                .collect(Collectors.toSet());
    }

    /**
     * 根据order 获取pet detail 列表
     *
     * @param order Order
     * @return List<GroomingPetDetailDTO>
     */
    private List<GroomingPetDetailDTO> getGroomingPetDetailDTOS(Order order) {

        boolean isOriginOrder = OrderModel.OrderType.ORIGIN.name().equals(order.getOrderType());

        // 查出来关联的pet detail数据
        List<OrderGroomingDetailRel> orderGroomingDetailRels =
                orderGroomingDetailRelService.getByOrderId(isOriginOrder, order.getId());
        Set<Long> petDetailIdSet = orderGroomingDetailRels.stream()
                .map(OrderGroomingDetailRel::getPetDetailId)
                .collect(Collectors.toSet());
        if (!isOriginOrder) {
            // 对于子单，直接查询关联关系 相关的pet detail就行
            return groomingPetDetailClient.listGroomingPetDetail(
                    order.getBusinessId(), order.getSourceId(), new ArrayList<>(petDetailIdSet));
        }

        // 对于主单，查询所有的groom pet detail，过滤掉关联关系的
        List<GroomingPetDetailDTO> groomingPetDetailDTOS =
                groomingPetDetailClient.queryAllPetDetailByGroomingId(order.getBusinessId(), order.getSourceId());
        return groomingPetDetailDTOS.stream()
                .filter(item -> !petDetailIdSet.contains(Long.valueOf(item.getId())))
                .toList();
    }

    /**
     * 按照每个金额加权算已支付金额
     *
     * @param amount                   总的已支付金额
     * @param groomingPetDetailDTOList pet detail
     * @return Map<Integer, BigDecimal>
     */
    private static Map<Long, BigDecimal> buildPerOrderItemAmountByPetDetail(
            BigDecimal amount, List<GroomingPetDetailDTO> groomingPetDetailDTOList) {

        Map<Long, BigDecimal> allocation = new HashMap<>();
        if (BigDecimal.ZERO.compareTo(amount) >= 0) {
            for (GroomingPetDetailDTO groomingPetDetailDTO : groomingPetDetailDTOList) {
                allocation.put(Long.valueOf(groomingPetDetailDTO.getId()), BigDecimal.ZERO);
            }
            return allocation;
        }

        // 计算 OrderItem 的总价格
        BigDecimal totalItemPrice = groomingPetDetailDTOList.stream()
                .map(GroomingPetDetailDTO::getServicePrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 分配支付金额
        BigDecimal remainingAmount = amount;
        for (int i = 0; i < groomingPetDetailDTOList.size(); i++) {
            GroomingPetDetailDTO item = groomingPetDetailDTOList.get(i);

            // 计算每个 OrderItem 应该分配的金额
            BigDecimal allocatedAmount = BigDecimal.ZERO;
            if (i == groomingPetDetailDTOList.size() - 1) {
                // 最后一个项分配剩余的所有金额，避免浮点误差
                allocatedAmount = remainingAmount;
            } else {
                if (BigDecimal.ZERO.compareTo(totalItemPrice) < 0) {
                    allocatedAmount =
                            amount.multiply(item.getServicePrice()).divide(totalItemPrice, 2, RoundingMode.HALF_UP);
                }
                remainingAmount = remainingAmount.subtract(allocatedAmount);
            }

            allocation.put(Long.valueOf(item.getId()), allocatedAmount);
        }

        return allocation;
    }

    /**
     * 按照每个金额加权算已支付金额
     *
     * @param amount         总的已支付金额
     * @param orderLineItems order item 列表
     * @return Map<Integer, BigDecimal>
     */
    private static Map<Long, BigDecimal> buildPerOrderItemAmountByLineItem(
            BigDecimal amount, List<OrderLineItem> orderLineItems) {

        Map<Long, BigDecimal> allocation = new HashMap<>();
        if (BigDecimal.ZERO.compareTo(amount) >= 0) {
            for (OrderLineItem orderLineItem : orderLineItems) {
                allocation.put(orderLineItem.getId(), BigDecimal.ZERO);
            }
            return allocation;
        }

        // 计算 OrderItem 的总价格
        BigDecimal totalItemPrice =
                orderLineItems.stream().map(OrderLineItem::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        // 分配支付金额
        BigDecimal remainingAmount = amount;
        for (int i = 0; i < orderLineItems.size(); i++) {
            OrderLineItem item = orderLineItems.get(i);

            // 计算每个 OrderItem 应该分配的金额
            BigDecimal allocatedAmount = BigDecimal.ZERO;
            if (i == orderLineItems.size() - 1) {
                // 最后一个项分配剩余的所有金额，避免浮点误差
                allocatedAmount = remainingAmount;
            } else {
                if (BigDecimal.ZERO.compareTo(totalItemPrice) < 0) {
                    allocatedAmount =
                            amount.multiply(item.getTotalAmount()).divide(totalItemPrice, 2, RoundingMode.HALF_UP);
                }
                remainingAmount = remainingAmount.subtract(allocatedAmount);
            }

            allocation.put(item.getId(), allocatedAmount);
        }

        return allocation;
    }

    /**
     * 更新order staff split detail
     *
     * @param needUpdateStaffIdSet     需要更新的staff id 集合
     * @param staffOrderSplitDetailMap 已有staff order split detail映射
     * @param splitRateMap             split rate 映射
     * @param totalAmount              总支付金额
     * @param refundedAmount           总已退款金额
     * @param commissionRate           commission rate
     */
    private void updateOrderStaffSplitDetail(
            Set<Long> needUpdateStaffIdSet,
            Map<Long, OrderStaffSplitDetail> staffOrderSplitDetailMap,
            Map<Long, BigDecimal> splitRateMap,
            BigDecimal totalAmount,
            BigDecimal refundedAmount,
            BigDecimal commissionRate) {
        if (CollectionUtils.isEmpty(needUpdateStaffIdSet)) {
            return;
        }
        for (Long staffId : needUpdateStaffIdSet) {

            OrderStaffSplitDetail orderStaffSplitDetail = staffOrderSplitDetailMap.get(staffId);
            // 兼容删除的逻辑
            BigDecimal splitRate = splitRateMap.getOrDefault(staffId, BigDecimal.ZERO);

            updateSingleStaffDetail(
                    staffId, orderStaffSplitDetail, splitRate, totalAmount, refundedAmount, commissionRate);
        }
    }

    /**
     * 更新单个staff detail
     *
     * @param staffId               staff id
     * @param orderStaffSplitDetail 原来的staff detail
     * @param splitRate             split rate
     * @param totalAmount           总金额
     * @param refundedAmount        退款金额
     * @param commissionRate        commission rate
     */
    private void updateSingleStaffDetail(
            Long staffId,
            OrderStaffSplitDetail orderStaffSplitDetail,
            BigDecimal splitRate,
            BigDecimal totalAmount,
            BigDecimal refundedAmount,
            BigDecimal commissionRate) {
        OrderStaffSplitDetail needUpdateStaffSplitDetail = new OrderStaffSplitDetail();
        needUpdateStaffSplitDetail.setId(orderStaffSplitDetail.getId());
        needUpdateStaffSplitDetail.setTotalAmount(totalAmount);
        needUpdateStaffSplitDetail.setRefundedAmount(refundedAmount);
        needUpdateStaffSplitDetail.setSplitRate(splitRate);
        BigDecimal splitAmount = totalAmount.multiply(splitRate);
        needUpdateStaffSplitDetail.setSplitAmount(splitAmount.setScale(2, RoundingMode.HALF_UP));

        commissionRate = null == commissionRate ? orderStaffSplitDetail.getCommissionRate() : commissionRate;
        needUpdateStaffSplitDetail.setCommissionRate(commissionRate);
        BigDecimal commissionAmount = splitAmount.multiply(commissionRate).setScale(2, RoundingMode.HALF_UP);
        needUpdateStaffSplitDetail.setCommissionAmount(commissionAmount);
        int cnt = orderStaffSplitDetailMapper.updateByPrimaryKeySelective(needUpdateStaffSplitDetail);
        // 本来就没有记录的，就不需要管change log了
        if (cnt <= 0) {
            return;
        }

        // 同时需要记录一条change log
        //        StaffPayrollChangeLog staffPayrollChangeLog = buildDefaultStaffChangeLog(
        //                orderStaffSplitDetail.getId(),
        //                orderStaffSplitDetail.getOrderId(),
        //                staffId,
        //                orderStaffSplitDetail.getBusinessId(),
        //                orderStaffSplitDetail.getTotalAmount(),
        //                totalAmount,
        //                orderStaffSplitDetail.getRefundedAmount(),
        //                refundedAmount,
        //                orderStaffSplitDetail.getSplitRate(),
        //                splitRate,
        //                orderStaffSplitDetail.getSplitAmount(),
        //                splitAmount,
        //                orderStaffSplitDetail.getCommissionRate(),
        //                commissionRate,
        //                orderStaffSplitDetail.getCommissionAmount(),
        //                commissionAmount);
        //        staffPayrollChangeLogMapper.insertSelective(staffPayrollChangeLog);
    }

    /**
     * 新增order staff split detail
     *
     * @param orderId           order id
     * @param orderItemId       type是service时是pet detail id，type是product时是order item id
     * @param type              type, product/service
     * @param businessId        business id
     * @param needAddStaffIdSet 需要新增的staff id集合
     * @param splitRateMap      split rate 映射
     * @param totalAmount       总支付金额
     * @param refundedAmount    总退款金额
     * @param commissionRate    commission rate
     *                          petId
     *                          objectId type是service时是serviceId，type是product时是productId
     */
    private void addOrderStaffSplitDetail(
            Long orderId,
            Long orderItemId,
            String type,
            Long businessId,
            Set<Long> needAddStaffIdSet,
            Map<Long, BigDecimal> splitRateMap,
            BigDecimal totalAmount,
            BigDecimal refundedAmount,
            BigDecimal commissionRate,
            Long petId,
            Long objectId) {

        if (CollectionUtils.isEmpty(needAddStaffIdSet)) {
            return;
        }

        for (Long staffId : needAddStaffIdSet) {
            BigDecimal splitRate = splitRateMap.get(staffId);

            // 不存在的就需要新增
            OrderStaffSplitDetail needAddOrderStaffSplitDetail = new OrderStaffSplitDetail();
            needAddOrderStaffSplitDetail.setOrderId(orderId);
            needAddOrderStaffSplitDetail.setOrderItemId(orderItemId);
            needAddOrderStaffSplitDetail.setType(type);
            needAddOrderStaffSplitDetail.setPetId(petId);
            needAddOrderStaffSplitDetail.setObjectId(objectId);
            needAddOrderStaffSplitDetail.setStaffId(staffId);
            needAddOrderStaffSplitDetail.setBusinessId(businessId);
            needAddOrderStaffSplitDetail.setTotalAmount(totalAmount);
            needAddOrderStaffSplitDetail.setRefundedAmount(refundedAmount);
            needAddOrderStaffSplitDetail.setSplitRate(splitRate);
            BigDecimal splitAmount = totalAmount.subtract(refundedAmount).multiply(splitRate);
            needAddOrderStaffSplitDetail.setSplitAmount(splitAmount.setScale(2, RoundingMode.HALF_UP));

            needAddOrderStaffSplitDetail.setCommissionRate(commissionRate);
            BigDecimal commissionAmount = splitAmount.multiply(commissionRate).setScale(2, RoundingMode.HALF_UP);
            needAddOrderStaffSplitDetail.setCommissionAmount(commissionAmount);

            orderStaffSplitDetailMapper.insertSelective(needAddOrderStaffSplitDetail);

            // 同时需要记录一条change log
            //            StaffPayrollChangeLog staffPayrollChangeLog = buildDefaultStaffChangeLog(
            //                    needAddOrderStaffSplitDetail.getId(),
            //                    orderId,
            //                    staffId,
            //                    businessId,
            //                    BigDecimal.ZERO,
            //                    totalAmount,
            //                    BigDecimal.ZERO,
            //                    refundedAmount,
            //                    BigDecimal.ZERO,
            //                    splitRate,
            //                    BigDecimal.ZERO,
            //                    splitAmount,
            //                    BigDecimal.ZERO,
            //                    commissionRate,
            //                    BigDecimal.ZERO,
            //                    commissionAmount);
            //            staffPayrollChangeLogMapper.insertSelective(staffPayrollChangeLog);
        }
    }

    /**
     * 获取pet detail涉及的staff集合
     *
     * @param groomingPetDetailDTO pet detail
     * @return staff id, split rate 映射
     */
    private static Map<Long, BigDecimal> getStaffIdSplitRateByPetDetail(GroomingPetDetailDTO groomingPetDetailDTO) {
        Map<Long, BigDecimal> staffIdSplitRateMap = new HashMap<>();
        if (CollectionUtils.isEmpty(groomingPetDetailDTO.getOperationList())) {
            staffIdSplitRateMap.put(Long.valueOf(groomingPetDetailDTO.getStaffId()), BigDecimal.ONE);
        } else {
            groomingPetDetailDTO
                    .getOperationList()
                    .forEach(operationDTO -> staffIdSplitRateMap.put(
                            Long.valueOf(operationDTO.getStaffId()), operationDTO.getPriceRatio()));
        }
        return staffIdSplitRateMap;
    }

    public static Map<Long, Pair<BigDecimal, BigDecimal>> buildSplitRateAndAmount(
            Set<Long> staffIdSet, BigDecimal amount) {
        Map<Long, Pair<BigDecimal, BigDecimal>> resultMap = new HashMap<>();
        int size = staffIdSet.size();
        if (size == 0) {
            return resultMap;
        }

        // 平均分配比例，保留四位小数
        BigDecimal averageRate = BigDecimal.ONE.divide(BigDecimal.valueOf(size), 4, RoundingMode.HALF_UP);
        BigDecimal totalAllocatedAmount = BigDecimal.ZERO;

        // 分配金额并处理每个 staffId 的比例和金额
        for (Long staffId : staffIdSet) {
            BigDecimal allocatedAmount = amount.divide(BigDecimal.valueOf(size), 2, RoundingMode.HALF_UP);
            totalAllocatedAmount = totalAllocatedAmount.add(allocatedAmount);
            resultMap.put(staffId, Pair.of(averageRate, allocatedAmount));
        }

        // 处理因为四舍五入带来的误差
        BigDecimal difference = amount.subtract(totalAllocatedAmount);
        if (difference.compareTo(BigDecimal.ZERO) != 0) {
            // 将误差加到第一个 staffId 的金额中
            Long firstStaffId = staffIdSet.iterator().next();
            Pair<BigDecimal, BigDecimal> originalPair = resultMap.get(firstStaffId);
            BigDecimal adjustedAmount = originalPair.value().add(difference);
            resultMap.put(firstStaffId, Pair.of(originalPair.key(), adjustedAmount));
        }
        return resultMap;
    }

    /**
     * 保存 edit tips detail amount
     */
    private void saveEditTipsDetail(
            Long orderId,
            Long businessId,
            BigDecimal tipsAmount,
            BigDecimal tipsRefundedAmount,
            Set<Long> staffIdSet,
            Map<Long, Pair<BigDecimal, BigDecimal>> tipsSplitRateAndAmountMap) {

        // 对于edit tips，只需要感知到一个order下所涉及的所有staff以及总金额

        List<OrderTipsSplitDetail> orderTipsSplitDetailList = orderTipsSplitDetailMapper.selectByOrderId(orderId);
        Set<Long> oldStaffIdSet = new HashSet<>();
        Map<Long, OrderTipsSplitDetail> staffOrderTipsSplitDetailMap = new HashMap<>();
        orderTipsSplitDetailList.forEach(orderTipsSplitDetail -> {
            oldStaffIdSet.add(orderTipsSplitDetail.getStaffId());
            staffOrderTipsSplitDetailMap.put(orderTipsSplitDetail.getStaffId(), orderTipsSplitDetail);
        });

        // 获取需要修改/新增/删除的staff
        Set<Long> needUpdateStaffIdSet =
                staffIdSet.stream().filter(oldStaffIdSet::contains).collect(Collectors.toSet());
        Set<Long> needAddStaffIdSet = staffIdSet.stream()
                .filter(staffId -> !oldStaffIdSet.contains(staffId))
                .collect(Collectors.toSet());
        Set<Long> needDeleteStaffIdSet = oldStaffIdSet.stream()
                .filter(staffId -> !staffIdSet.contains(staffId))
                .collect(Collectors.toSet());
        // 删除也就是修改为0
        needUpdateStaffIdSet.addAll(needDeleteStaffIdSet);

        // 修改order tips split detail
        updateOrderTipsSplitDetail(
                needUpdateStaffIdSet,
                tipsAmount,
                tipsRefundedAmount,
                staffOrderTipsSplitDetailMap,
                tipsSplitRateAndAmountMap);
        // 新增order tips split detail
        addOrderTipsSplitDetail(
                orderId, businessId, needAddStaffIdSet, tipsAmount, tipsRefundedAmount, tipsSplitRateAndAmountMap);
    }

    /**
     * 修改order tips split detail
     *
     * @param needUpdateStaffIdSet         需要修改的staff id 集合
     * @param tipsAmount                   tips总金额
     * @param tipsRefundedAmount           tips已退款金额
     * @param staffOrderTipsSplitDetailMap staff已有的split detail 映射
     * @param tipsSplitRateAndAmountMap    tips split的映射
     */
    private void updateOrderTipsSplitDetail(
            Set<Long> needUpdateStaffIdSet,
            BigDecimal tipsAmount,
            BigDecimal tipsRefundedAmount,
            Map<Long, OrderTipsSplitDetail> staffOrderTipsSplitDetailMap,
            Map<Long, Pair<BigDecimal, BigDecimal>> tipsSplitRateAndAmountMap) {

        if (CollectionUtils.isEmpty(needUpdateStaffIdSet)) {
            return;
        }
        for (Long staffId : needUpdateStaffIdSet) {

            OrderTipsSplitDetail tipsSplitDetail = staffOrderTipsSplitDetailMap.get(staffId);
            Pair<BigDecimal, BigDecimal> tipsSplitRateAndAmount =
                    tipsSplitRateAndAmountMap.getOrDefault(staffId, new Pair<>(BigDecimal.ZERO, BigDecimal.ZERO));
            BigDecimal splitRate = tipsSplitRateAndAmount.key();
            BigDecimal splitAmount = tipsSplitRateAndAmount.value();

            // 原来有的，需要更新一下
            OrderTipsSplitDetail needUpdateTipsSplitDetail = new OrderTipsSplitDetail();
            needUpdateTipsSplitDetail.setId(tipsSplitDetail.getId());
            needUpdateTipsSplitDetail.setTotalAmount(tipsAmount);
            needUpdateTipsSplitDetail.setRefundedAmount(tipsRefundedAmount);
            needUpdateTipsSplitDetail.setSplitRate(splitRate);
            needUpdateTipsSplitDetail.setSplitAmount(splitAmount);
            needUpdateTipsSplitDetail.setCommissionRate(BigDecimal.ONE);
            needUpdateTipsSplitDetail.setCommissionAmount(splitAmount);
            orderTipsSplitDetailMapper.updateByPrimaryKeySelective(needUpdateTipsSplitDetail);

            //            StaffPayrollChangeLog staffPayrollChangeLog = buildDefaultTipsChangeLog(
            //                    tipsSplitDetail.getId(),
            //                    tipsSplitDetail.getOrderId(),
            //                    staffId,
            //                    tipsSplitDetail.getBusinessId(),
            //                    tipsSplitDetail.getTotalAmount(),
            //                    tipsAmount,
            //                    tipsSplitDetail.getRefundedAmount(),
            //                    tipsRefundedAmount,
            //                    tipsSplitDetail.getSplitRate(),
            //                    splitRate,
            //                    tipsSplitDetail.getSplitAmount(),
            //                    splitAmount);
            //            staffPayrollChangeLogMapper.insertSelective(staffPayrollChangeLog);
        }
    }

    /**
     * 新增order tips split detail
     *
     * @param orderId                   order id
     * @param businessId                business id
     * @param needAddStaffIdSet         需要新增的staff id集合
     * @param tipsAmount                tips总金额
     * @param tipsRefundedAmount        tips已退款金额
     * @param tipsSplitRateAndAmountMap tips split映射
     */
    private void addOrderTipsSplitDetail(
            Long orderId,
            Long businessId,
            Set<Long> needAddStaffIdSet,
            BigDecimal tipsAmount,
            BigDecimal tipsRefundedAmount,
            Map<Long, Pair<BigDecimal, BigDecimal>> tipsSplitRateAndAmountMap) {

        if (CollectionUtils.isEmpty(needAddStaffIdSet)) {
            return;
        }
        for (Long staffId : needAddStaffIdSet) {
            Pair<BigDecimal, BigDecimal> tipsSplitRateAndAmount = tipsSplitRateAndAmountMap.get(staffId);
            BigDecimal splitRate = tipsSplitRateAndAmount.key();
            BigDecimal splitAmount = tipsSplitRateAndAmount.value();

            // 对于tips来说，commission rate 是100%
            OrderTipsSplitDetail needAddTipsSplitDetail = buildDefaultTipsSplitDetail(
                    orderId, businessId, staffId, tipsAmount, tipsRefundedAmount, splitRate, splitAmount);
            orderTipsSplitDetailMapper.insertSelective(needAddTipsSplitDetail);

            //            StaffPayrollChangeLog staffPayrollChangeLog = buildDefaultTipsChangeLog(
            //                    needAddTipsSplitDetail.getId(),
            //                    orderId,
            //                    staffId,
            //                    businessId,
            //                    tipsAmount,
            //                    tipsAmount,
            //                    tipsRefundedAmount,
            //                    tipsRefundedAmount,
            //                    BigDecimal.ZERO,
            //                    splitRate,
            //                    BigDecimal.ZERO,
            //                    splitAmount);
            //
            //            staffPayrollChangeLogMapper.insertSelective(staffPayrollChangeLog);
        }
    }

    /**
     * 构建默认的tips change log
     *
     * @return StaffPayrollChangeLog
     */
    private static StaffPayrollChangeLog buildDefaultTipsChangeLog(
            Long refId,
            Long orderId,
            Long staffId,
            Long businessId,
            BigDecimal beforeTotalAmount,
            BigDecimal totalAmount,
            BigDecimal beforeRefundedAmount,
            BigDecimal refundedAmount,
            BigDecimal beforeSplitRate,
            BigDecimal splitRate,
            BigDecimal beforeSplitAmount,
            BigDecimal splitAmount) {

        StaffPayrollChangeLog staffPayrollChangeLog = new StaffPayrollChangeLog();
        staffPayrollChangeLog.setRefType(StaffChangeLogRelateType.STAFF_CHANGE_LOG_RELATE_TYPE_EDIT_TIPS.name());
        staffPayrollChangeLog.setRefId(refId);
        staffPayrollChangeLog.setOrderId(orderId);
        staffPayrollChangeLog.setStaffId(staffId);
        staffPayrollChangeLog.setBusinessId(businessId);
        staffPayrollChangeLog.setAmountType(StaffChangeLogAmountType.STAFF_CHANGE_LOG_AMOUNT_TYPE_TIPS.name());
        staffPayrollChangeLog.setBeforeCommissionAmount(beforeSplitAmount);
        staffPayrollChangeLog.setCommissionAmount(splitAmount);
        staffPayrollChangeLog.setBeforeCommissionRate(BigDecimal.ONE);
        staffPayrollChangeLog.setCommissionRate(BigDecimal.ONE);
        staffPayrollChangeLog.setBeforeTotalAmount(beforeTotalAmount);
        staffPayrollChangeLog.setTotalAmount(totalAmount);
        staffPayrollChangeLog.setBeforeSplitAmount(beforeSplitAmount);
        staffPayrollChangeLog.setSplitAmount(splitAmount);
        staffPayrollChangeLog.setBeforeSplitRate(beforeSplitRate);
        staffPayrollChangeLog.setSplitRate(splitRate);
        staffPayrollChangeLog.setBeforeRefundedAmount(beforeRefundedAmount);
        staffPayrollChangeLog.setRefundedAmount(refundedAmount);
        return staffPayrollChangeLog;
    }

    /**
     * 构建默认的tips split detail
     *
     * @return OrderTipsSplitDetail
     */
    private static OrderTipsSplitDetail buildDefaultTipsSplitDetail(
            Long orderId,
            Long businessId,
            Long staffId,
            BigDecimal totalAmount,
            BigDecimal refundedAmount,
            BigDecimal splitRate,
            BigDecimal splitAmount) {

        OrderTipsSplitDetail tipsSplitDetail = new OrderTipsSplitDetail();
        tipsSplitDetail.setOrderId(orderId);
        tipsSplitDetail.setBusinessId(businessId);
        tipsSplitDetail.setStaffId(staffId);
        tipsSplitDetail.setCommissionAmount(splitAmount);
        tipsSplitDetail.setCommissionRate(BigDecimal.ONE);
        tipsSplitDetail.setTotalAmount(totalAmount);
        tipsSplitDetail.setRefundedAmount(refundedAmount);
        tipsSplitDetail.setSplitAmount(splitAmount);
        tipsSplitDetail.setSplitRate(splitRate);
        return tipsSplitDetail;
    }

    /**
     * 构建默认的staff change log
     *
     * @return StaffPayrollChangeLog
     */
    private static StaffPayrollChangeLog buildDefaultStaffChangeLog(
            Long refId,
            Long orderId,
            Long staffId,
            Long businessId,
            BigDecimal beforeTotalAmount,
            BigDecimal totalAmount,
            BigDecimal beforeRefundedAmount,
            BigDecimal refundedAmount,
            BigDecimal beforeSplitRate,
            BigDecimal splitRate,
            BigDecimal beforeSplitAmount,
            BigDecimal splitAmount,
            BigDecimal beforeCommissionRate,
            BigDecimal commissionRate,
            BigDecimal beforeCommissionAmount,
            BigDecimal commissionAmount) {

        StaffPayrollChangeLog staffPayrollChangeLog = new StaffPayrollChangeLog();
        staffPayrollChangeLog.setRefType(StaffChangeLogRelateType.STAFF_CHANGE_LOG_RELATE_TYPE_EDIT_STAFF.name());
        staffPayrollChangeLog.setRefId(refId);
        staffPayrollChangeLog.setOrderId(orderId);
        staffPayrollChangeLog.setStaffId(staffId);
        staffPayrollChangeLog.setBusinessId(businessId);
        staffPayrollChangeLog.setAmountType(StaffChangeLogAmountType.STAFF_CHANGE_LOG_AMOUNT_TYPE_ORDER_ITEM.name());
        staffPayrollChangeLog.setBeforeCommissionAmount(beforeCommissionAmount);
        staffPayrollChangeLog.setCommissionAmount(commissionAmount);
        staffPayrollChangeLog.setBeforeCommissionRate(beforeCommissionRate);
        staffPayrollChangeLog.setCommissionRate(commissionRate);
        staffPayrollChangeLog.setBeforeTotalAmount(beforeTotalAmount);
        staffPayrollChangeLog.setTotalAmount(totalAmount);
        staffPayrollChangeLog.setBeforeSplitAmount(beforeSplitAmount);
        staffPayrollChangeLog.setSplitAmount(splitAmount);
        staffPayrollChangeLog.setBeforeSplitRate(beforeSplitRate);
        staffPayrollChangeLog.setSplitRate(splitRate);
        staffPayrollChangeLog.setBeforeRefundedAmount(beforeRefundedAmount);
        staffPayrollChangeLog.setRefundedAmount(refundedAmount);
        return staffPayrollChangeLog;
    }
}

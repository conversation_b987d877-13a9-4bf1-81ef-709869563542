package com.moego.svc.business.customer.enums;

import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel.Source;
import lombok.Getter;

public enum CustomerSource {
    SOURCE_0("0", Source.SOURCE_ZERO),
    SOURCE_1("1", Source.SOURCE_ONE),
    SOURCE_2("2", Source.SOURCE_TWO),
    SOURCE_3("3", Source.SOURCE_THREE),
    SOURCE_4("4", Source.SOURCE_FOUR),
    SOURCE_5("5", Source.SOURCE_FIVE),
    MANUAL_CREATE("manual", Source.MANUAL_CREATE),
    SELF_IMPORT("contacts", Source.SELF_IMPORT),
    DATA_IMPORT("dm", Source.DATA_IMPORT),
    ONLINE_BOOKING("ob", Source.ONLINE_BOOKING),
    INTAKE_FORM("if", Source.INTAKE_FORM),
    CALL_IN("call", Source.CALL_IN),
    TEXT_IN("text", Source.TEXT_IN),
    BRANDED_APP("branded", Source.BRANDED_APP),
    UNKNOWN("unknown", Source.UNKNOWN);

    @Getter
    private final String value;

    @Getter
    private final Source protoEnum;

    CustomerSource(String value, Source protoEnum) {
        this.value = value;
        this.protoEnum = protoEnum;
    }

    public static CustomerSource fromSource(Source protoEnum) {
        for (CustomerSource source : CustomerSource.values()) {
            if (source.protoEnum == protoEnum) {
                return source;
            }
        }
        return UNKNOWN;
    }

    public static CustomerSource fromValue(String value) {
        for (CustomerSource source : CustomerSource.values()) {
            if (source.value.equals(value)) {
                return source;
            }
        }
        return UNKNOWN;
    }
}

package com.moego.svc.business.customer.converter;

import ch.qos.logback.classic.pattern.DateConverter;
import com.moego.idl.models.business_customer.v1.BusinessPetSizeModel;
import com.moego.idl.models.business_customer.v1.BusinessPetSizeUpsertDef;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.svc.business.customer.enums.StatusEnum;
import com.moego.svc.business.customer.repository.jooq.tables.records.MoePetSizeRecord;
import com.moego.svc.business.customer.utils.StatusUtils;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import org.mapstruct.AfterMapping;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE,
        imports = {Objects.class},
        uses = {DateConverter.class, TimeConverter.class, RecordFactory.class})
public interface PetSizeConverter {

    MoePetSizeRecord createRecord(BusinessPetSizeUpsertDef request, Tenant tenant);

    // use conditionExpression to avoid update unchanged fields
    @Mapping(
            target = "name",
            source = "request.name",
            conditionExpression = "java(!Objects.equals(record.getName(), request.getName()))")
    @Mapping(
            target = "weightLow",
            source = "request.weightLow",
            conditionExpression = "java(!Objects.equals(record.getWeightLow(), request.getWeightLow()))")
    @Mapping(
            target = "weightHigh",
            source = "request.weightHigh",
            conditionExpression = "java(!Objects.equals(record.getWeightHigh(), request.getWeightHigh()))")
    void updateRecord(BusinessPetSizeUpsertDef request, @MappingTarget MoePetSizeRecord record);

    default void deleteRecord(@MappingTarget MoePetSizeRecord record) {
        if (StatusUtils.isDeleted(record.getStatus())) {
            return;
        }
        record.setStatus(StatusEnum.DELETED.getValue());
        setCreatedAndUpdatedAt(record);
    }

    BusinessPetSizeModel toModel(MoePetSizeRecord record);

    List<BusinessPetSizeModel> toModels(List<MoePetSizeRecord> records);

    @AfterMapping
    default void setCreatedAndUpdatedAt(@MappingTarget MoePetSizeRecord record) {
        var now = LocalDateTime.now();
        if (record.getCreatedAt() == null) {
            record.setCreatedAt(now);
        }
        if (record.changed()) {
            record.setUpdatedAt(now);
        }
    }
}

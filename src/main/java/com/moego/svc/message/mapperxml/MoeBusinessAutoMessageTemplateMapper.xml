<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.message.mapper.MoeBusinessAutoMessageTemplateMapper">
  <resultMap id="BaseResultMap" type="com.moego.svc.message.mapperbean.MoeBusinessAutoMessageTemplate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="body" jdbcType="VARCHAR" property="body" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="crate_time" jdbcType="INTEGER" property="crateTime" />
    <result column="update_time" jdbcType="INTEGER" property="updateTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, type, body, status, crate_time, update_time, company_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from moe_business_auto_message_template
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_business_auto_message_template
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.svc.message.mapperbean.MoeBusinessAutoMessageTemplate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_business_auto_message_template (business_id, type, body, 
      status, crate_time, update_time, 
      company_id)
    values (#{businessId,jdbcType=INTEGER}, #{type,jdbcType=INTEGER}, #{body,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{crateTime,jdbcType=INTEGER}, #{updateTime,jdbcType=INTEGER}, 
      #{companyId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.svc.message.mapperbean.MoeBusinessAutoMessageTemplate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_business_auto_message_template
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="body != null">
        body,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="crateTime != null">
        crate_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="body != null">
        #{body,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="crateTime != null">
        #{crateTime,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.svc.message.mapperbean.MoeBusinessAutoMessageTemplate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_business_auto_message_template
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="body != null">
        body = #{body,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="crateTime != null">
        crate_time = #{crateTime,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.svc.message.mapperbean.MoeBusinessAutoMessageTemplate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_business_auto_message_template
    set business_id = #{businessId,jdbcType=INTEGER},
      type = #{type,jdbcType=INTEGER},
      body = #{body,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      crate_time = #{crateTime,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=INTEGER},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectForTransfer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_business_auto_message_template
    where business_id in
    <foreach collection="businessIds" item="businessId" open="(" separator="," close=")">
      #{businessId}
    </foreach>
    and type in (5,9)
  </select>
</mapper>
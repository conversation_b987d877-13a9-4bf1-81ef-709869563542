package com.moego.svc.appointment.utils;

import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.fulfillment.v1.LineItem;
import com.moego.idl.models.fulfillment.v1.PetService;
import com.moego.idl.models.fulfillment.v1.ServiceItem;
import com.moego.idl.models.fulfillment.v1.SurchargeItem;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v2.PricingRule;
import com.moego.idl.models.offering.v2.RuleType;
import com.moego.idl.models.order.v1.ServiceCharge;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.lib.common.proto.MoneyUtils;
import com.moego.svc.appointment.domain.BoardingSplitLodging;
import com.moego.svc.appointment.domain.EvaluationServiceDetail;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.domain.PricingRuleRecordApplyLog;
import com.moego.svc.appointment.domain.ServiceChargeDetail;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2025/5/22
 */
public class LineItemUtils {

    public static final String PET_DETAIL_EXTERNAL_ID_PREFIX = "petdetail_";
    public static final String EVALUATION_EXTERNAL_ID_PREFIX = "evaluation_";
    public static final String SERVICE_CHARGE_EXTERNAL_ID_PREFIX = "servicecharge_";
    private static final String NORMAL_SERVICE_NAME = "Service";
    private static final String HAS_STAFF_SERVICE_NAME = "Service - %s";
    private static final String HAS_LODGING_SERVICE_NAME = "Service - %s(%s)";

    private static final Map<RuleType, String> RULE_TYPE_TO_NAME = Map.of(
            RuleType.MULTIPLE_STAY, "Multiple nights/days",
            RuleType.MULTIPLE_PET, "Multiple pets",
            RuleType.PEAK_DATE, "Peak dates");

    public static List<SurchargeItem> buildSurcharges(
            List<ServiceChargeDetail> serviceChargeDetails, Map<Long, ServiceCharge> idToServiceCharge) {

        return serviceChargeDetails.stream()
                .map(detail -> {
                    var serviceCharge = idToServiceCharge.getOrDefault(
                            detail.getServiceChargeId(), ServiceCharge.getDefaultInstance());

                    return SurchargeItem.newBuilder()
                            .setServiceChargeId(detail.getServiceChargeId())
                            .setName(serviceCharge.getName())
                            .setType(serviceCharge.getSurchargeType())
                            .setServiceChargeDetailId(detail.getId())
                            .setUnitPrice(MoneyUtils.toGoogleMoney(detail.getPrice()))
                            .setQuantity(1)
                            .setTotalPrice(MoneyUtils.toGoogleMoney(detail.getPrice()))
                            .setExternalId(SERVICE_CHARGE_EXTERNAL_ID_PREFIX + detail.getId())
                            .build();
                })
                .toList();
    }

    public static List<PetService> buildPetServices(
            Map<Long, List<ServiceItem>> petIdToServiceItems, Map<Long, BusinessCustomerPetInfoModel> petIdToInfo) {
        return petIdToServiceItems.entrySet().stream()
                .map(entry -> buildPetService(petIdToInfo.get(entry.getKey()), entry.getValue()))
                .toList();
    }

    private static PetService buildPetService(BusinessCustomerPetInfoModel pet, List<ServiceItem> serviceItems) {
        return PetService.newBuilder().setPet(pet).addAllServices(serviceItems).build();
    }

    public static List<LineItem> buildLineItems(
            MoeGroomingPetDetail detail,
            @Nullable StaffModel staff,
            Map<Long, LodgingUnitModel> idToLodging,
            Map<Long, LodgingTypeModel> idToLodgingType,
            List<BoardingSplitLodging> splitLodgings,
            String timeZoneName,
            List<PricingRuleRecordApplyLog> pricingRuleApplyLogs) {
        // 没有 pricing rule 时，以 pet 维度优先取 split lodging 数据，其次取 pet detail
        if (CollectionUtils.isEmpty(pricingRuleApplyLogs)) {
            if (CollectionUtils.isEmpty(splitLodgings)) {
                return List.of(buildServiceLineItem(detail, staff, idToLodging, idToLodgingType));
            } else {
                return buildServiceLineItems(detail, idToLodging, idToLodgingType, timeZoneName, splitLodgings);
            }
        }

        // 有 pricing rule 时，以 pet 维度优先取 split lodging 数据，其次取 pricing rule log
        List<LineItem> result = new ArrayList<>();

        if (CollectionUtils.isEmpty(splitLodgings)) {
            result.addAll(buildServiceLineItems(pricingRuleApplyLogs, detail, idToLodging, idToLodgingType));
        } else {
            result.addAll(buildServiceLineItems(detail, idToLodging, idToLodgingType, timeZoneName, splitLodgings));
        }

        result.addAll(buildPricingRuleLineItems(pricingRuleApplyLogs));

        return result;
    }

    private static List<LineItem> buildPricingRuleLineItems(List<PricingRuleRecordApplyLog> pricingRuleApplyLogs) {
        var dateToLogs =
                pricingRuleApplyLogs.stream().collect(Collectors.groupingBy(PricingRuleRecordApplyLog::getServiceDate));

        Map<String, RuleEffectData> ruleEffectMap = analyzeRuleEffects(dateToLogs);

        return createLineItemsFromRuleEffects(ruleEffectMap);
    }

    private static Map<String, RuleEffectData> analyzeRuleEffects(
            Map<String, List<PricingRuleRecordApplyLog>> dateMap) {

        Map<String, RuleEffectData> ruleEffectMap = new HashMap<>();

        // Process each date's logs to identify rule combinations and their effects
        for (Map.Entry<String, List<PricingRuleRecordApplyLog>> entry : dateMap.entrySet()) {
            List<PricingRuleRecordApplyLog> logsForDate = entry.getValue();

            Set<RuleType> ruleTypes = getRuleTypes(logsForDate);

            // Determine price effect
            BigDecimal originalPrice = logsForDate.get(0).getOriginalPrice();
            BigDecimal adjustedPrice = logsForDate.get(0).getAdjustedPrice();
            BigDecimal effect = adjustedPrice.subtract(originalPrice);

            // Create rule key and determine item type
            var ruleKey = createRuleKey(ruleTypes);

            if (ruleKey.isEmpty()) {
                continue;
            }

            if (!ruleEffectMap.containsKey(ruleKey)) {
                ruleEffectMap.put(
                        ruleKey, new RuleEffectData(effect, 0, LineItem.LineItemType.PRICING_RULE_ADJUSTMENT));
            }

            ruleEffectMap.get(ruleKey).incrementCount();
        }

        return ruleEffectMap;
    }

    private static Set<RuleType> getRuleTypes(List<PricingRuleRecordApplyLog> logsForDate) {
        return logsForDate.stream()
                .filter(PricingRuleRecordApplyLog::getIsUsingRule)
                .map(PricingRuleRecordApplyLog::getPricingRule)
                .map(PricingRule::getType)
                .collect(Collectors.toSet());
    }

    private static String createRuleKey(Set<RuleType> ruleTypes) {
        if (CollectionUtils.isEmpty(ruleTypes)) {
            return "";
        }

        var names = ruleTypes.stream().map(RULE_TYPE_TO_NAME::get).toList();

        return String.join(", ", names);
    }

    private static List<LineItem> createLineItemsFromRuleEffects(Map<String, RuleEffectData> ruleEffectMap) {
        return ruleEffectMap.entrySet().stream()
                .map(entry -> {
                    RuleEffectData data = entry.getValue();
                    return LineItem.newBuilder()
                            .setItemName(entry.getKey())
                            .setUnitPrice(MoneyUtils.toGoogleMoney(data.getEffect()))
                            .setQuantity(data.count)
                            .setLineTotal(
                                    MoneyUtils.toGoogleMoney(data.effect.multiply(BigDecimal.valueOf(data.count))))
                            .setItemType(data.getItemType())
                            .build();
                })
                .toList();
    }

    /**
     * Helper class to track data about rule effects
     */
    @Data
    @AllArgsConstructor
    private static class RuleEffectData {
        private BigDecimal effect;
        private int count;
        private LineItem.LineItemType itemType;

        public void incrementCount() {
            this.count++;
        }
    }

    private static List<LineItem> buildServiceLineItems(
            List<PricingRuleRecordApplyLog> pricingRuleApplyLogs,
            MoeGroomingPetDetail detail,
            Map<Long, LodgingUnitModel> idToLodging,
            Map<Long, LodgingTypeModel> idToLodgingType) {
        // Group logs by date to handle multiple entries per date
        Map<String, List<PricingRuleRecordApplyLog>> dateMap =
                pricingRuleApplyLogs.stream().collect(Collectors.groupingBy(PricingRuleRecordApplyLog::getServiceDate));

        // Create a map of original price → dates with that price
        Map<BigDecimal, Set<String>> priceToDateMap = new HashMap<>();

        // For each date, get the original price from the first log entry for that date
        for (Map.Entry<String, List<PricingRuleRecordApplyLog>> entry : dateMap.entrySet()) {
            String date = entry.getKey();
            List<PricingRuleRecordApplyLog> logsForDate = entry.getValue();

            if (!logsForDate.isEmpty()) {
                BigDecimal originalPrice = logsForDate.get(0).getOriginalPrice();

                // Add this date to the set of dates with this price
                if (!priceToDateMap.containsKey(originalPrice)) {
                    priceToDateMap.put(originalPrice, new HashSet<>());
                }
                priceToDateMap.get(originalPrice).add(date);
            }
        }

        // Create a separate LineItem for each distinct original price
        return priceToDateMap.entrySet().stream()
                .map(entry -> {
                    BigDecimal price = entry.getKey();
                    int daysCount = entry.getValue().size();
                    BigDecimal totalForPrice = price.multiply(BigDecimal.valueOf(daysCount));

                    return LineItem.newBuilder()
                            .setItemName(getLineItemName(detail, null, idToLodging, idToLodgingType))
                            .setUnitPrice(MoneyUtils.toGoogleMoney(price))
                            .setQuantity(daysCount)
                            .setLineTotal(MoneyUtils.toGoogleMoney(totalForPrice))
                            .setItemType(LineItem.LineItemType.SERVICE_PRICE)
                            .build();
                })
                .toList();
    }

    private static LineItem buildServiceLineItem(
            MoeGroomingPetDetail detail,
            @Nullable StaffModel staff,
            Map<Long, LodgingUnitModel> idToLodging,
            Map<Long, LodgingTypeModel> idToLodgingType) {
        return LineItem.newBuilder()
                .setItemName(getLineItemName(detail, staff, idToLodging, idToLodgingType))
                .setUnitPrice(MoneyUtils.toGoogleMoney(detail.getServicePrice()))
                .setQuantity(detail.getQuantity())
                .setLineTotal(MoneyUtils.toGoogleMoney(detail.getTotalPrice()))
                .setItemType(LineItem.LineItemType.SERVICE_PRICE)
                .build();
    }

    private static List<LineItem> buildServiceLineItems(
            MoeGroomingPetDetail detail,
            Map<Long, LodgingUnitModel> idToLodging,
            Map<Long, LodgingTypeModel> idToLodgingType,
            String timeZoneName,
            List<BoardingSplitLodging> splitLodgings) {
        if (CollectionUtils.isEmpty(splitLodgings)) {
            return List.of();
        }
        return splitLodgings.stream()
                .map(splitLodging -> {
                    var startDate = splitLodging
                            .getStartDateTime()
                            .atZone(ZoneId.of(timeZoneName))
                            .toLocalDate();
                    var endDate = splitLodging
                            .getEndDateTime()
                            .atZone(ZoneId.of(timeZoneName))
                            .toLocalDate();
                    // 最后一段 split lodging 需要判断是否包含 checkout day
                    var quantity = ChronoUnit.DAYS.between(startDate, endDate);
                    var includeCheckoutDay = Objects.equals(detail.getEndDate(), endDate.toString())
                            && Objects.equals(
                                    detail.getDateType(),
                                    PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY_VALUE);
                    if (includeCheckoutDay) {
                        ++quantity;
                    }

                    return LineItem.newBuilder()
                            .setItemName(getLineItemName(detail, null, idToLodging, idToLodgingType))
                            .setUnitPrice(MoneyUtils.toGoogleMoney(splitLodging.getPrice()))
                            .setQuantity((int) quantity)
                            .setLineTotal(MoneyUtils.toGoogleMoney(
                                    splitLodging.getPrice().multiply(BigDecimal.valueOf(quantity))))
                            .setItemType(LineItem.LineItemType.SERVICE_PRICE)
                            .build();
                })
                .toList();
    }

    static String getLineItemName(
            MoeGroomingPetDetail detail,
            @Nullable StaffModel staff,
            Map<Long, LodgingUnitModel> idToLodging,
            Map<Long, LodgingTypeModel> idToLodgingType) {
        if (idToLodging.containsKey(detail.getLodgingId())) {
            var lodging = idToLodging.getOrDefault(detail.getLodgingId(), LodgingUnitModel.getDefaultInstance());
            var lodgingType =
                    idToLodgingType.getOrDefault(lodging.getLodgingTypeId(), LodgingTypeModel.getDefaultInstance());
            return String.format(HAS_LODGING_SERVICE_NAME, lodging.getName(), lodgingType.getName());
        } else if (staff != null) {
            return String.format(HAS_STAFF_SERVICE_NAME, staff.getFirstName());
        }
        return NORMAL_SERVICE_NAME;
    }

    public static List<LineItem> buildLineItems(
            EvaluationServiceDetail evaluationDetail,
            @Nullable StaffModel staff,
            @Nullable LodgingUnitModel lodging,
            Map<Long, LodgingTypeModel> idToLodgingType) {

        return List.of(LineItem.newBuilder()
                .setItemName(getLineItemName(staff, lodging, idToLodgingType))
                .setUnitPrice(MoneyUtils.toGoogleMoney(evaluationDetail.getServicePrice()))
                .setQuantity(1)
                .setLineTotal(MoneyUtils.toGoogleMoney(evaluationDetail.getServicePrice()))
                .setItemType(LineItem.LineItemType.SERVICE_PRICE)
                .build());
    }

    static String getLineItemName(
            @Nullable StaffModel staff,
            @Nullable LodgingUnitModel lodging,
            Map<Long, LodgingTypeModel> idToLodgingType) {
        if (lodging != null) {
            var lodgingType =
                    idToLodgingType.getOrDefault(lodging.getLodgingTypeId(), LodgingTypeModel.getDefaultInstance());
            return String.format(HAS_LODGING_SERVICE_NAME, lodging.getName(), lodgingType.getName());
        } else if (staff != null) {
            return String.format(HAS_STAFF_SERVICE_NAME, staff.getFirstName());
        }
        return NORMAL_SERVICE_NAME;
    }
}

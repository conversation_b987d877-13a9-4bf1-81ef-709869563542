package com.moego.svc.appointment.utils;

import static org.mybatis.dynamic.sql.SqlBuilder.and;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isGreaterThan;
import static org.mybatis.dynamic.sql.SqlBuilder.isGreaterThanOrEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isLessThan;
import static org.mybatis.dynamic.sql.SqlBuilder.isLessThanOrEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.or;

import com.google.type.Interval;
import com.moego.idl.utils.v1.TimeOfDayInterval;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import java.util.Objects;
import lombok.experimental.UtilityClass;
import org.mybatis.dynamic.sql.AndOrCriteriaGroup;
import org.mybatis.dynamic.sql.SqlColumn;
import org.springframework.util.ObjectUtils;

@UtilityClass
public class CriteriaUtils {

    public List<AndOrCriteriaGroup> buildDateTimeFilter(
            SqlColumn<String> dateColumn, SqlColumn<Integer> timeColumn, String timezoneName, Interval timeRange) {
        if (ObjectUtils.isEmpty(timeRange)) {
            return List.of();
        }
        var startTime = Instant.ofEpochSecond(timeRange.getStartTime().getSeconds())
                .atZone(ZoneId.of(timezoneName))
                .toLocalDateTime();
        var endTime = Instant.ofEpochSecond(timeRange.getEndTime().getSeconds())
                .atZone(ZoneId.of(timezoneName))
                .toLocalDateTime();
        var startDate = startTime.toLocalDate().toString();
        var startMinutes = startTime.toLocalTime().toSecondOfDay() / 60;
        var endDate = endTime.toLocalDate().toString();
        var endMinutes = endTime.toLocalTime().toSecondOfDay() / 60;
        // 根据时间拼接出时间条件，分为双端和单边
        if (timeRange.hasStartTime() && timeRange.hasEndTime()) {
            if (Objects.equals(startDate, endDate)) {
                return List.of(
                        and(dateColumn, isEqualTo(startDate)),
                        and(timeColumn, isGreaterThanOrEqualTo(startMinutes)),
                        and(timeColumn, isLessThanOrEqualTo(endMinutes)));
            } else {
                return List.of(
                        or(dateColumn, isEqualTo(startDate), and(timeColumn, isGreaterThanOrEqualTo(startMinutes))),
                        or(dateColumn, isGreaterThan(startDate), and(dateColumn, isLessThan(endDate))),
                        or(dateColumn, isEqualTo(endDate), and(timeColumn, isLessThanOrEqualTo(endMinutes))));
            }
        } else if (timeRange.hasStartTime()) {
            return List.of(
                    or(dateColumn, isEqualTo(startDate), and(timeColumn, isGreaterThanOrEqualTo(startMinutes))),
                    or(dateColumn, isGreaterThan(startDate)));
        } else if (timeRange.hasEndTime()) {
            return List.of(
                    or(dateColumn, isEqualTo(endDate), and(timeColumn, isLessThanOrEqualTo(endMinutes))),
                    or(dateColumn, isLessThan(endDate)));
        } else {
            return List.of();
        }
    }

    public Pair<LocalDate, LocalDate> buildStartEndDate(Interval timeRange, String timezoneName) {
        var startTime = Instant.ofEpochSecond(timeRange.getStartTime().getSeconds())
                .atZone(ZoneId.of(timezoneName))
                .toLocalDateTime();
        var endTime = Instant.ofEpochSecond(timeRange.getEndTime().getSeconds())
                .atZone(ZoneId.of(timezoneName))
                .toLocalDateTime();
        var startDate = startTime.toLocalDate();
        var endDate = endTime.toLocalDate();

        return Pair.of(startDate, endDate);
    }

    public List<AndOrCriteriaGroup> buildDateFilter(
            SqlColumn<LocalDate> dateColumn, String timezoneName, Interval timeRange) {
        if (ObjectUtils.isEmpty(timeRange)) {
            return List.of();
        }
        var startEndDate = buildStartEndDate(timeRange, timezoneName);
        var startDate = startEndDate.first();
        var endDate = startEndDate.second();

        if (timeRange.hasStartTime() && timeRange.hasEndTime()) {
            if (Objects.equals(startDate, endDate)) {
                return List.of(and(dateColumn, isEqualTo(startDate)));
            } else {
                return List.of(and(
                        dateColumn, isGreaterThanOrEqualTo(startDate), and(dateColumn, isLessThanOrEqualTo(endDate))));
            }
        } else if (timeRange.hasStartTime()) {
            return List.of(and(dateColumn, isGreaterThanOrEqualTo(startDate)));
        } else if (timeRange.hasEndTime()) {
            return List.of(and(dateColumn, isLessThanOrEqualTo(endDate)));
        } else {
            return List.of();
        }
    }

    public List<AndOrCriteriaGroup> buildTimeFilter(SqlColumn<Integer> timeColumn, TimeOfDayInterval timeRange) {
        if (ObjectUtils.isEmpty(timeRange)) {
            return List.of();
        }

        var startMinutes =
                timeRange.getStart().getHours() * 60 + timeRange.getStart().getMinutes();
        var endMinutes = timeRange.getEnd().getHours() * 60 + timeRange.getEnd().getMinutes();

        if (timeRange.hasStart() && timeRange.hasEnd()) {
            if (Objects.equals(startMinutes, endMinutes)) {
                return List.of(and(timeColumn, isEqualTo(endMinutes)));
            } else {
                return List.of(and(
                        timeColumn,
                        isGreaterThanOrEqualTo(startMinutes),
                        and(timeColumn, isLessThanOrEqualTo(endMinutes))));
            }
        } else if (timeRange.hasStart()) {
            return List.of(and(timeColumn, isGreaterThanOrEqualTo(startMinutes)));
        } else if (timeRange.hasEnd()) {
            return List.of(and(timeColumn, isLessThanOrEqualTo(endMinutes)));
        } else {
            return List.of();
        }
    }
}

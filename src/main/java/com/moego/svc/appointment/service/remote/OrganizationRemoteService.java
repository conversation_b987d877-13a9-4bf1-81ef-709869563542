package com.moego.svc.appointment.service.remote;

import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.models.offering.v1.EvaluationModel;
import com.moego.idl.models.organization.v1.LocationBriefView;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.idl.models.organization.v1.TaxRuleModel;
import com.moego.idl.service.organization.v1.BatchGetTaxRuleRequest;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.CompanyServiceGrpc;
import com.moego.idl.service.organization.v1.DateTimeServiceGrpc;
import com.moego.idl.service.organization.v1.GetCompanyCurrentDayAndTimeRequest;
import com.moego.idl.service.organization.v1.GetCompanyIdRequest;
import com.moego.idl.service.organization.v1.GetCompanyPreferenceSettingRequest;
import com.moego.idl.service.organization.v1.GetLocationListRequest;
import com.moego.idl.service.organization.v1.QueryStaffByIdsRequest;
import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import com.moego.idl.service.organization.v1.TaxRuleServiceGrpc;
import com.moego.svc.appointment.domain.EvaluationServiceDetail;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class OrganizationRemoteService {
    private final CompanyServiceGrpc.CompanyServiceBlockingStub companyServiceBlockingStub;
    private final BusinessServiceGrpc.BusinessServiceBlockingStub businessService;
    private final DateTimeServiceGrpc.DateTimeServiceBlockingStub dateTimeService;
    private final StaffServiceGrpc.StaffServiceBlockingStub staffStub;
    private final TaxRuleServiceGrpc.TaxRuleServiceBlockingStub taxRuleStub;

    public String getTimezone(Long companyId) {
        return companyServiceBlockingStub
                .getCompanyPreferenceSetting(GetCompanyPreferenceSettingRequest.newBuilder()
                        .setCompanyId(companyId)
                        .build())
                .getPreferenceSetting()
                .getTimeZone()
                .getName();
    }

    public Long getCompanyIdByBusinessId(Integer businessId) {
        return businessService
                .getCompanyId(GetCompanyIdRequest.newBuilder()
                        .setBusinessId(businessId.longValue())
                        .build())
                .getCompanyId();
    }

    public String getCompanyCurrentDate(Long companyId) {
        var resp = dateTimeService.getCompanyCurrentDayAndTime(GetCompanyCurrentDayAndTimeRequest.newBuilder()
                .setCompanyId(companyId)
                .build());
        return resp.getCurrentDate();
    }

    public List<Long> listBusinessIds(long companyId) {
        return businessService
                .getLocationList(GetLocationListRequest.newBuilder()
                        .setTokenCompanyId(companyId)
                        .build())
                .getLocationList()
                .stream()
                .map(LocationBriefView::getId)
                .toList();
    }

    public String getCurrency(Long companyId) {
        return companyServiceBlockingStub
                .getCompanyPreferenceSetting(GetCompanyPreferenceSettingRequest.newBuilder()
                        .setCompanyId(companyId)
                        .build())
                .getPreferenceSetting()
                .getCurrencyCode();
    }

    public Map<Long, StaffModel> listStaffsByEvaluationDetails(Collection<EvaluationServiceDetail> evaluationDetails) {
        var staffIds = evaluationDetails.stream()
                .map(EvaluationServiceDetail::getStaffId)
                .distinct()
                .filter(s -> s > 0)
                .toList();
        return listStaffs(staffIds);
    }

    public Map<Long, StaffModel> listStaffsByPetDetails(Collection<MoeGroomingPetDetail> petDetails) {
        var staffIds = petDetails.stream()
                .map(MoeGroomingPetDetail::getStaffId)
                .distinct()
                .filter(s -> s > 0)
                .map(Integer::longValue)
                .toList();
        return listStaffs(staffIds);
    }

    public Map<Long, StaffModel> listStaffs(Collection<Long> staffIds) {
        if (CollectionUtils.isEmpty(staffIds)) {
            return Map.of();
        }
        return staffStub
                .queryStaffByIds(QueryStaffByIdsRequest.newBuilder()
                        .addAllStaffIds(staffIds)
                        .build())
                .getStaffsList()
                .stream()
                .collect(Collectors.toMap(StaffModel::getId, Function.identity()));
    }

    public Map<Long, TaxRuleModel> listTaxRulesMapByServices(Collection<CustomizedServiceView> services) {
        if (CollectionUtils.isEmpty(services)) {
            return Map.of();
        }
        var taxIds = services.stream()
                .map(CustomizedServiceView::getTaxId)
                .distinct()
                .toList();
        return listTaxRules(taxIds);
    }

    public Map<Long, TaxRuleModel> listTaxRulesByEvaluations(Collection<EvaluationModel> evaluations) {
        if (CollectionUtils.isEmpty(evaluations)) {
            return Map.of();
        }
        var taxIds =
                evaluations.stream().map(EvaluationModel::getTaxId).distinct().toList();
        return listTaxRules(taxIds);
    }

    public Map<Long, TaxRuleModel> listTaxRules(Collection<Long> taxIds) {
        if (CollectionUtils.isEmpty(taxIds)) {
            return Map.of();
        }
        return taxRuleStub
                .batchGetTaxRule(
                        BatchGetTaxRuleRequest.newBuilder().addAllIds(taxIds).build())
                .getRulesList()
                .stream()
                .collect(Collectors.toMap(TaxRuleModel::getId, Function.identity()));
    }
}

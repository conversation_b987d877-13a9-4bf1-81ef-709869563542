package com.moego.svc.appointment.service;

import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;

import com.moego.svc.appointment.domain.AppointmentExtraInfo;
import com.moego.svc.appointment.mapper.mysql.AppointmentExtraInfoDynamicSqlSupport;
import com.moego.svc.appointment.mapper.mysql.AppointmentExtraInfoMapper;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 目前仅记录预约是否走了 new order flow 标记
 * 后续存量未做支付的预约数据清洗完成后，该表应该被废弃下线
 *
 * <AUTHOR>
 * @since 2025/5/12
 */
@Service
@RequiredArgsConstructor
public class AppointmentExtraInfoService {

    private final AppointmentExtraInfoMapper extraInfoMapper;

    public void insertNewOrderFlag(long appointmentId) {
        AppointmentExtraInfo extraInfo = new AppointmentExtraInfo();
        extraInfo.setAppointmentId(appointmentId);
        extraInfo.setIsNewOrder(true);
        extraInfoMapper.insertSelective(extraInfo);
    }

    public boolean isNewOrder(long appointmentId) {
        return extraInfoMapper
                .selectOne(c -> c.where(AppointmentExtraInfoDynamicSqlSupport.appointmentId, isEqualTo(appointmentId)))
                .map(AppointmentExtraInfo::getIsNewOrder)
                .orElse(false);
    }

    public Map<Long, Boolean> listNewOrderFlag(List<Long> appointmentIds) {
        if (CollectionUtils.isEmpty(appointmentIds)) {
            return Map.of();
        }
        return extraInfoMapper
                .select(c -> c.where(AppointmentExtraInfoDynamicSqlSupport.appointmentId, isIn(appointmentIds)))
                .stream()
                .collect(Collectors.toMap(AppointmentExtraInfo::getAppointmentId, AppointmentExtraInfo::getIsNewOrder));
    }

    public List<AppointmentExtraInfo> listExtraInfos(List<Long> appointmentIds) {
        if (CollectionUtils.isEmpty(appointmentIds)) {
            return List.of();
        }
        return extraInfoMapper.select(
                c -> c.where(AppointmentExtraInfoDynamicSqlSupport.appointmentId, isIn(appointmentIds)));
    }
}

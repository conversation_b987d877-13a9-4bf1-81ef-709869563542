package com.moego.svc.appointment.service.remote;

import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.service.business_customer.v1.BusinessCustomerServiceGrpc;
import com.moego.idl.service.business_customer.v1.GetCustomerInfoRequest;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.params.UpdateCustomerLastServiceParams;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import com.moego.svc.appointment.service.AppointmentService;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/2/5
 */
@Service
@RequiredArgsConstructor
public class CustomerRemoteService {

    private final AppointmentService appointmentService;
    private final ICustomerCustomerClient customerClient;
    private final BusinessCustomerServiceGrpc.BusinessCustomerServiceBlockingStub customerStub;

    /**
     * Update moe_customer.last_service_time field.
     * This field is used for tagging lapsed client.
     *
     * @param businessId business id
     * @param customerId customer id
     */
    public void refreshCustomerLastServiceTime(Long businessId, Long customerId) {
        MoeGroomingAppointment lastAppointment =
                appointmentService.selectLastAppointment(businessId.intValue(), customerId.intValue());
        customerClient.updateCustomerLastServiceTime(new UpdateCustomerLastServiceParams()
                .setBusinessId(businessId.intValue())
                .setCustomerId(customerId.intValue())
                // last_service_time column not null
                .setLastServiceTime(Objects.nonNull(lastAppointment) ? lastAppointment.getAppointmentDate() : ""));
    }

    public BusinessCustomerInfoModel getCustomerInfo(long customerId) {
        return customerStub
                .getCustomerInfo(
                        GetCustomerInfoRequest.newBuilder().setId(customerId).build())
                .getCustomer();
    }
}

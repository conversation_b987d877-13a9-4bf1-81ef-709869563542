package com.moego.svc.organization.entity;

import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Builder;
import lombok.Data;

@Data
@Builder(toBuilder = true)
public class StaffAvailabilityTimeDay {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_time_day.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_time_day.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_time_day.business_id
     *
     * @mbg.generated
     */
    private Long businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_time_day.staff_id
     *
     * @mbg.generated
     */
    private Long staffId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_time_day.override_date
     *
     * @mbg.generated
     */
    private LocalDate overrideDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_time_day.is_available
     *
     * @mbg.generated
     */
    private Boolean isAvailable;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_time_day.schedule_type
     *
     * @mbg.generated
     */
    private Integer scheduleType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_time_day.day_of_week
     *
     * @mbg.generated
     */
    private Integer dayOfWeek;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_time_day.created_at
     *
     * @mbg.generated
     */
    private LocalDateTime createdAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_time_day.updated_at
     *
     * @mbg.generated
     */
    private LocalDateTime updatedAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_time_day.limit_ids
     *
     * @mbg.generated
     */
    private String limitIds;
}

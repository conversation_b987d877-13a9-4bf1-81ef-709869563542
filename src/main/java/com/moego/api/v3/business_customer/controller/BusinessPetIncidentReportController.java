package com.moego.api.v3.business_customer.controller;

import com.moego.idl.api.business_customer.v1.BusinessPetIncidentReportServiceGrpc;
import com.moego.idl.api.business_customer.v1.CreatePetIncidentReportParams;
import com.moego.idl.api.business_customer.v1.CreatePetIncidentReportResult;
import com.moego.idl.api.business_customer.v1.DeletePetIncidentReportParams;
import com.moego.idl.api.business_customer.v1.DeletePetIncidentReportResult;
import com.moego.idl.api.business_customer.v1.ListPetIncidentReportParams;
import com.moego.idl.api.business_customer.v1.ListPetIncidentReportResult;
import com.moego.idl.api.business_customer.v1.UpdatePetIncidentReportParams;
import com.moego.idl.api.business_customer.v1.UpdatePetIncidentReportResult;
import com.moego.idl.service.business_customer.v1.BusinessPetIncidentReportServiceGrpc.BusinessPetIncidentReportServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.CreatePetIncidentReportRequest;
import com.moego.idl.service.business_customer.v1.DeletePetIncidentReportRequest;
import com.moego.idl.service.business_customer.v1.ListPetIncidentReportRequest;
import com.moego.idl.service.business_customer.v1.UpdatePetIncidentReportRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import io.grpc.stub.StreamObserver;
import lombok.AllArgsConstructor;

@GrpcService
@AllArgsConstructor
public class BusinessPetIncidentReportController
        extends BusinessPetIncidentReportServiceGrpc.BusinessPetIncidentReportServiceImplBase {

    private final BusinessPetIncidentReportServiceBlockingStub petIncidentReportServiceBlockingStub;

    @Override
    @Auth(AuthType.COMPANY)
    public void listPetIncidentReport(
            ListPetIncidentReportParams request, StreamObserver<ListPetIncidentReportResult> responseObserver) {
        var listRequestBuilder = ListPetIncidentReportRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setPetId(request.getPetId());
        if (request.hasBusinessId()) {
            listRequestBuilder.setBusinessId(request.getBusinessId());
        }

        var listResponse = petIncidentReportServiceBlockingStub.listPetIncidentReport(listRequestBuilder.build());

        var result = ListPetIncidentReportResult.newBuilder()
                .addAllIncidentReports(listResponse.getIncidentReportsList())
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.CREATE_OR_EDIT_INCIDENT_REPORT})
    public void createPetIncidentReport(
            CreatePetIncidentReportParams request, StreamObserver<CreatePetIncidentReportResult> responseObserver) {

        var createRequest = CreatePetIncidentReportRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(AuthContext.get().businessId())
                .setStaffId(AuthContext.get().staffId())
                .setIncidentReport(request.getIncidentReport())
                .build();

        responseObserver.onNext(CreatePetIncidentReportResult.newBuilder()
                .setIncidentReport(petIncidentReportServiceBlockingStub
                        .createPetIncidentReport(createRequest)
                        .getIncidentReport())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.CREATE_OR_EDIT_INCIDENT_REPORT})
    public void updatePetIncidentReport(
            UpdatePetIncidentReportParams request, StreamObserver<UpdatePetIncidentReportResult> responseObserver) {

        var updateRequest = UpdatePetIncidentReportRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(AuthContext.get().businessId())
                .setStaffId(AuthContext.get().staffId())
                .setId(request.getId())
                .setIncidentReport(request.getIncidentReport())
                .build();
        petIncidentReportServiceBlockingStub.updatePetIncidentReport(updateRequest);

        responseObserver.onNext(UpdatePetIncidentReportResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.CREATE_OR_EDIT_INCIDENT_REPORT})
    public void deletePetIncidentReport(
            DeletePetIncidentReportParams request, StreamObserver<DeletePetIncidentReportResult> responseObserver) {
        var deleteRequest = DeletePetIncidentReportRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(AuthContext.get().businessId())
                .setStaffId(AuthContext.get().staffId())
                .setId(request.getId())
                .build();
        petIncidentReportServiceBlockingStub.deletePetIncidentReport(deleteRequest);

        responseObserver.onNext(DeletePetIncidentReportResult.getDefaultInstance());
        responseObserver.onCompleted();
    }
}

package com.moego.server.business.web;

import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.business.dto.BusinessWorkingHourDetailDTO;
import com.moego.server.business.service.BusinessWorkingHourService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/business/working/hour")
public class BusinessWorkingHourController {

    @Autowired
    private BusinessWorkingHourService businessWorkingHourService;

    @GetMapping
    @Auth(AuthType.BUSINESS)
    public BusinessWorkingHourDetailDTO getBusinessWorkingHour(AuthContext context) {
        return businessWorkingHourService.getBusinessWorkingHourDetail(context.getBusinessId());
    }

    @PutMapping
    @Auth(AuthType.BUSINESS)
    public void saveBusinessWorkingHour(
            AuthContext context, @RequestBody @Valid BusinessWorkingHourDetailDTO businessWorkingHourDetailDTO) {
        businessWorkingHourDetailDTO.setBusinessId(context.getBusinessId());
        businessWorkingHourService.saveBusinessWorkingHour(businessWorkingHourDetailDTO);
    }
}

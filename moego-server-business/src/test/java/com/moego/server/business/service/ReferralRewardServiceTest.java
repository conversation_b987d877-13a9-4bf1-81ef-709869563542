package com.moego.server.business.service;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.common.enums.BusinessReferralConst;
import com.moego.common.exception.CommonException;
import com.moego.server.business.mapper.MoeReferralBonusRewardMapper;
import com.moego.server.business.mapper.MoeReferralBonusRewardRuleMapper;
import com.moego.server.business.mapper.MoeReferralInfoMapper;
import com.moego.server.business.mapper.MoeReferralRewardRecordMapper;
import com.moego.server.business.mapperbean.MoeReferralBonusReward;
import com.moego.server.business.mapperbean.MoeReferralBonusRewardRule;
import com.moego.server.business.mapperbean.MoeReferralInfo;
import com.moego.server.business.mapperbean.MoeReferralRewardRecord;
import com.moego.server.business.service.params.ReferralBonusParams;
import com.moego.server.business.web.vo.referral.ReferralBonusRewardVO;
import com.moego.server.payment.client.IPaymentStripeClient;
import com.moego.server.payment.dto.StripeCustomerDTO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class ReferralRewardServiceTest {

    @InjectMocks
    private ReferralRewardService referralRewardService;

    @Mock
    private MoeReferralBonusRewardMapper referralBonusRewardMapper;

    @Mock
    private MoeReferralBonusRewardRuleMapper referralBonusRewardRuleMapper;

    @Mock
    private IPaymentStripeClient paymentStripeClient;

    @Mock
    private MoeReferralRewardRecordMapper referralRewardRecordMapper;

    @Mock
    private MoeReferralInfoMapper referralInfoMapper;

    @Test
    public void updateBonusRewardStatus() {
        Integer bonusRewardId = 1;
        Mockito.doReturn(1)
                .when(referralBonusRewardMapper)
                .updateByPrimaryKeySelective(Mockito.any(MoeReferralBonusReward.class));

        boolean res = referralRewardService.updateBonusRewardStatus(bonusRewardId);
        assertThat(res).as("update bonus reward status failure").isTrue();
    }

    @Test
    public void getBonusRewardList() {
        Integer bonusRuleId = 1;
        ReferralBonusParams referralBonusParams =
                ReferralBonusParams.builder().referralId(1).build();
        List<MoeReferralBonusReward> bonusRewardList = new ArrayList<>();
        MoeReferralBonusReward bonusReward = new MoeReferralBonusReward();
        bonusReward.setBonusRuleId(bonusRuleId);
        bonusRewardList.add(bonusReward);
        Mockito.doReturn(bonusRewardList)
                .when(referralBonusRewardMapper)
                .getBonusListByReferralId(referralBonusParams.getReferralId());

        List<MoeReferralBonusRewardRule> bonusRewardRuleList = new ArrayList<>();
        MoeReferralBonusRewardRule bonusRewardRule = new MoeReferralBonusRewardRule();
        bonusRewardRule.setId(bonusRuleId);
        bonusRewardRule.setTargetNumber(3);
        bonusRewardRule.setBonusAmount(BigDecimal.valueOf(88));
        bonusRewardRuleList.add(bonusRewardRule);
        Mockito.doReturn(bonusRewardRuleList)
                .when(referralBonusRewardRuleMapper)
                .getBonusRuleByIdList(Collections.singletonList(bonusRuleId));

        List<ReferralBonusRewardVO> bonusRewardVOList = referralRewardService.getBonusRewardList(referralBonusParams);
        assertThat(bonusRewardList.size()).as("bonusRewardList size not equals").isEqualTo(bonusRewardVOList.size());
        ReferralBonusRewardVO bonusRewardVO = bonusRewardVOList.get(0);
        assertThat(bonusRewardVO.getBonusRuleId())
                .as("bonus rule id not equals")
                .isEqualTo(bonusRewardRule.getId());
        assertThat(bonusRewardVO.getTargetNumber())
                .as("bonus target number not equals")
                .isEqualTo(bonusRewardRule.getTargetNumber());
        assertThat(bonusRewardVO.getBonusAmount())
                .as("bonus amount not equals")
                .isEqualTo(bonusRewardRule.getBonusAmount());
    }

    @Test
    public void handleReferralFixedRewardNotExistStripeCustomer() {
        BigDecimal fixedAmount = BigDecimal.valueOf(25);
        MoeReferralInfo refererInfo = new MoeReferralInfo();
        refererInfo.setId(2);
        refererInfo.setCompanyId(2);

        StripeCustomerDTO stripeCustomerDTO = StripeCustomerDTO.NULL_OBJECT;
        Mockito.doReturn(1)
                .when(referralRewardRecordMapper)
                .insertSelective(Mockito.any(MoeReferralRewardRecord.class));
        Mockito.doReturn(stripeCustomerDTO).when(paymentStripeClient).getStripeCustomer(refererInfo.getCompanyId());
        Mockito.doReturn(1)
                .when(referralRewardRecordMapper)
                .updateByPrimaryKeySelective(Mockito.any(MoeReferralRewardRecord.class));

        referralRewardService.handleReferralFixedReward(refererInfo, 2, fixedAmount);
    }

    @Test
    public void handleReferralFixedRewardExistStripeCustomer() {
        Integer companyId = 1;

        BigDecimal fixedAmount = BigDecimal.valueOf(25);
        MoeReferralInfo refererInfo = new MoeReferralInfo();
        refererInfo.setId(1);
        refererInfo.setCompanyId(companyId);

        StripeCustomerDTO stripeCustomerDTO = new StripeCustomerDTO();
        stripeCustomerDTO.setStripeCustomerId("ut_stripe_customer_id");
        stripeCustomerDTO.setCompanyId(companyId);

        Mockito.doReturn(1)
                .when(referralRewardRecordMapper)
                .insertSelective(Mockito.any(MoeReferralRewardRecord.class));
        Mockito.doReturn(stripeCustomerDTO).when(paymentStripeClient).getStripeCustomer(refererInfo.getCompanyId());
        Mockito.doNothing().when(referralInfoMapper).increaseReferralTotalEarning(refererInfo.getId(), fixedAmount);
        Mockito.doNothing().when(paymentStripeClient).increaseStripeCustomerBalance(Mockito.anyList());
        Mockito.doReturn(1)
                .when(referralRewardRecordMapper)
                .updateByPrimaryKeySelective(Mockito.any(MoeReferralRewardRecord.class));

        referralRewardService.handleReferralFixedReward(refererInfo, 1, fixedAmount);
        Mockito.verify(referralInfoMapper).increaseReferralTotalEarning(refererInfo.getId(), fixedAmount);
        Mockito.verify(paymentStripeClient).increaseStripeCustomerBalance(Mockito.anyList());
    }

    @Test
    public void handleReferralFixedRewardThrowException() {
        Integer companyId = 1;

        BigDecimal fixedAmount = BigDecimal.valueOf(25);
        MoeReferralInfo refererInfo = new MoeReferralInfo();
        refererInfo.setId(1);
        refererInfo.setCompanyId(companyId);

        StripeCustomerDTO stripeCustomerDTO = StripeCustomerDTO.NULL_OBJECT;
        stripeCustomerDTO.setStripeCustomerId("ut_stripe_customer_id");
        stripeCustomerDTO.setCompanyId(companyId);

        Mockito.doReturn(1)
                .when(referralRewardRecordMapper)
                .insertSelective(Mockito.any(MoeReferralRewardRecord.class));
        Mockito.doReturn(stripeCustomerDTO).when(paymentStripeClient).getStripeCustomer(refererInfo.getCompanyId());
        Mockito.doNothing().when(referralInfoMapper).increaseReferralTotalEarning(refererInfo.getId(), fixedAmount);
        Mockito.doReturn(1)
                .when(referralRewardRecordMapper)
                .updateByPrimaryKeySelective(Mockito.any(MoeReferralRewardRecord.class));

        Mockito.doThrow(CommonException.class)
                .when(paymentStripeClient)
                .increaseStripeCustomerBalance(Mockito.anyList());
        referralRewardService.handleReferralFixedReward(refererInfo, 1, fixedAmount);

        Mockito.verify(referralInfoMapper).increaseReferralTotalEarning(refererInfo.getId(), fixedAmount);
    }

    @Test
    public void getZeroEarnReward() {
        Integer companyId = 1;
        List<MoeReferralBonusReward> bonusRewardList = new ArrayList<>();
        Mockito.doReturn(bonusRewardList).when(referralRewardRecordMapper).getUncheckedRewardListByCompanyId(companyId);

        BigDecimal earnReward = referralRewardService.getEarnRewardAndChecked(companyId);
        assertThat(earnReward).as("earn reward not equals zero").isEqualTo(BigDecimal.ZERO);
    }

    @Test
    public void getEarnRewardAndChecked() {
        Integer companyId = 1;
        Integer recordId = 1;
        List<MoeReferralRewardRecord> bonusRewardList = new ArrayList<>();
        MoeReferralRewardRecord rewardRecord = new MoeReferralRewardRecord();
        rewardRecord.setId(recordId);
        rewardRecord.setRewardAmount(BigDecimal.valueOf(25));
        rewardRecord.setStatus(BusinessReferralConst.BONUS_PENDING);
        bonusRewardList.add(rewardRecord);
        Mockito.doReturn(bonusRewardList).when(referralRewardRecordMapper).getUncheckedRewardListByCompanyId(companyId);

        BigDecimal earnReward = referralRewardService.getEarnRewardAndChecked(companyId);
        BigDecimal totalEarn = bonusRewardList.stream()
                .map(MoeReferralRewardRecord::getRewardAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        assertThat(earnReward).as("earn reward not equals").isEqualTo(totalEarn);
    }
}

package com.moego.server.payment.service;

import static com.moego.common.enums.PaymentMethodEnum.MODULE_GROOMING;
import static com.moego.common.enums.PaymentMethodEnum.MODULE_RETAIL;

import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.enums.SubscriptionConst;
import com.moego.common.enums.hardware.BundleSaleEnum;
import com.moego.common.exception.CommonException;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.service.enterprise.v1.EnterpriseServiceGrpc;
import com.moego.idl.service.enterprise.v1.GetEnterpriseRequest;
import com.moego.idl.service.enterprise.v1.GetEnterpriseResponse;
import com.moego.idl.service.order.v1.GetOrderRequest;
import com.moego.idl.service.order.v1.OrderServiceGrpc;
import com.moego.lib.common.exception.BizException;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.dto.CompanyDto;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.payment.constant.PlatformCareStatusEnum;
import com.moego.server.payment.mapperbean.MmStripeCompanyCustomer;
import com.moego.server.payment.mapperbean.MoePlatformCare;
import java.math.BigDecimal;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2021/10/13 3:04 PM
 */
@Service
@Slf4j
public class CheckingService {

    @Autowired
    private IBusinessBusinessClient iBusinessBusinessClient;

    @Autowired
    private ICustomerCustomerClient iCustomerCustomerClient;

    @Autowired
    private OrderServiceGrpc.OrderServiceBlockingStub orderClient;

    @Autowired
    private CompanyCardService companyCardService;

    @Autowired
    private PlatformCareService platformCareService;

    @Autowired
    private EnterpriseServiceGrpc.EnterpriseServiceBlockingStub enterpriseClient;

    public void checkInvoicePaid(Integer businessId, Integer invoiceId, String module) {
        if (!MODULE_GROOMING.equals(module) && !MODULE_RETAIL.equals(module)) {
            return;
        }
        OrderModel order = orderClient.getOrder(GetOrderRequest.newBuilder()
                .setBusinessId(businessId)
                .setId(invoiceId)
                .build());
        if (order == null) {
            throw ExceptionUtil.bizException(Code.CODE_INVOICE_NOT_FOUND);
        }
        if (order.getBusinessId() != businessId) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "businessId check not match");
        }
        BigDecimal paidAmount = BigDecimal.valueOf(order.getPaidAmount());
        BigDecimal total = BigDecimal.valueOf(order.getTotalAmount());
        BigDecimal refund = BigDecimal.valueOf(order.getRefundedAmount());
        if (paidAmount.subtract(refund).compareTo(total) >= 0) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "This invoice has been paid.  Please refresh.");
        }
    }

    /**
     * accountId 必须是companyId的owner 或是 company 的 enterprise master account
     * @param accountId
     * @param companyId
     */
    public void checkCompanyOwner(Integer accountId, Integer companyId) {
        CompanyDto companyDto = iBusinessBusinessClient.getCompanyById(companyId);
        if (companyDto == null) {
            throw new CommonException(ResponseCodeEnum.ACCOUNT_NOT_OWN_THIS_COMPANY);
        }
        // is company owner,check pass
        if (companyDto.getAccountId().equals(accountId)) {
            return;
        }
        if (companyDto.getEnterpriseId() == 0) {
            throw new CommonException(ResponseCodeEnum.ACCOUNT_NOT_OWN_THIS_COMPANY);
        }
        checkEnterpriseOwner(accountId.longValue(), companyDto.getEnterpriseId().longValue());
    }

    public void checkCompanyOwnerExceptEnterprise(Long accountId, Integer companyId) {
        CompanyDto companyDto = iBusinessBusinessClient.getCompanyById(companyId);
        if (companyDto == null || !companyDto.getAccountId().equals(accountId.intValue())) {
            throw new CommonException(ResponseCodeEnum.ACCOUNT_NOT_OWN_THIS_COMPANY);
        }
    }

    public void checkEnterpriseOwner(Long accountId, Long enterpriseId) {
        if (!isEnterpriseOwner(accountId, enterpriseId)) {
            throw new BizException(Code.CODE_ACCOUNT_NOT_OWN_THIS_ENTERPRISE_VALUE, "account not own this enterprise");
        }
    }

    public boolean isEnterpriseOwner(Long accountId, Long enterpriseId) {
        GetEnterpriseResponse enterpriseRes = enterpriseClient.getEnterprise(
                GetEnterpriseRequest.newBuilder().setId(enterpriseId).build());
        // company not belong to any enterprise or account not own this enterprise
        return Objects.equals(enterpriseRes.getEnterprise().getAccountId(), accountId);
    }

    public Integer checkingAndGetCustomerId(String customerCode) {
        Integer customerId = iCustomerCustomerClient.getCustomerIdByCustomerCode(customerCode);
        if (customerId == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "invalid customer code");
        }
        return customerId;
    }

    public void checkHardwareCouponCode(BundleSaleEnum saleType, String hardwareCode, Integer companyId) {
        // 非空字符串才校验
        if (BundleSaleEnum.SALE.equals(saleType) && StringUtils.hasText(hardwareCode)) {
            if (!isValidHardwareCouponCode(hardwareCode)) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "invalid hardware coupon code");
            } else {
                // checking whether already used
                // checking unique link
                MoePlatformCare link = platformCareService.getByCompanyId(Long.valueOf(companyId));
                if (!ObjectUtils.isEmpty(link) && PlatformCareStatusEnum.ORDER_PLACED.getCode() == link.getStatus()) {
                    throw ExceptionUtil.bizException(
                            Code.CODE_PARAMS_ERROR, "MoeGo Care user cannot use hardware discount code");
                }
                // checking sale coupon
                MmStripeCompanyCustomer companyCustomer = companyCardService.getStripeCompanyCustomer(companyId);
                if (companyCustomer != null
                        && companyCardService.alreadyBuyHardwareByDiscount(companyCustomer.getStripeCustomerId())) {
                    throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "already used hardware discount code");
                }
            }
        }
    }

    public static boolean isValidHardwareCouponCode(String hardwareCode) {
        return SubscriptionConst.HARDWARE_ORDER_COUPON_CODE.equalsIgnoreCase(hardwareCode);
    }
}

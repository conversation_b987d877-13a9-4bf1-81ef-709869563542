package com.moego.server.payment.service.params;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ClientTestClockParam {

    @NotNull
    Integer customerId;

    @Schema(description = "针对billing cycle 需要提前的天数，最多提前两个月")
    @Max(62)
    @Min(1)
    @NotNull
    Integer advanceDays;
}

package com.moego.server.payment.mapper;

import com.moego.server.payment.mapperbean.HardwareOrder;
import com.moego.server.payment.mapperbean.HardwareOrderExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface HardwareOrderMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hardware_order
     *
     * @mbg.generated
     */
    long countByExample(HardwareOrderExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hardware_order
     *
     * @mbg.generated
     */
    int deleteByExample(HardwareOrderExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hardware_order
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hardware_order
     *
     * @mbg.generated
     */
    int insert(HardwareOrder record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hardware_order
     *
     * @mbg.generated
     */
    int insertSelective(HardwareOrder record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hardware_order
     *
     * @mbg.generated
     */
    List<HardwareOrder> selectByExample(HardwareOrderExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hardware_order
     *
     * @mbg.generated
     */
    HardwareOrder selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hardware_order
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") HardwareOrder record, @Param("example") HardwareOrderExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hardware_order
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") HardwareOrder record, @Param("example") HardwareOrderExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hardware_order
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(HardwareOrder record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hardware_order
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(HardwareOrder record);
}

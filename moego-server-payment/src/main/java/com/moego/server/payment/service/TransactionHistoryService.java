package com.moego.server.payment.service;

import com.moego.common.SquarePaymentMethodEnum;
import com.moego.common.StripePaymentMethodEnum;
import com.moego.common.enums.PaymentStripeStatus;
import com.moego.common.enums.StripeApi;
import com.moego.common.enums.payment.SplitSyncRecordTypeEnum;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.Pagination;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.payment.dto.refund.StripePaymentIntentView;
import com.moego.server.payment.dto.refund.TransactionHistoryListDTO;
import com.moego.server.payment.dto.refund.TransactionHistoryView;
import com.moego.server.payment.mapper.PaymentMapper;
import com.moego.server.payment.mapper.RefundMapper;
import com.moego.server.payment.mapperbean.Payment;
import com.moego.server.payment.mapstruct.TransactionHistoryMapper;
import com.moego.server.payment.params.refund.StripeRefundParams;
import com.moego.server.payment.params.refund.TransactionHistoryListParams;
import com.stripe.exception.StripeException;
import com.stripe.model.PaymentIntent;
import com.stripe.model.Refund;
import com.stripe.param.RefundCreateParams;
import io.netty.util.internal.StringUtil;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/1/2
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TransactionHistoryService {
    private final PaymentMapper paymentMapper;
    private final RefundMapper refundMapper;
    private final SplitPaymentService splitPaymentService;

    public TransactionHistoryListDTO selectTransactionHistoryList(TransactionHistoryListParams params) {
        List<TransactionHistoryView> views = new ArrayList<>();
        TransactionHistoryListDTO result = TransactionHistoryListDTO.builder()
                .views(views)
                .total(paymentMapper.countTransactionHistoryListByParams(params))
                .build();
        Pagination pagination = Optional.ofNullable(params.getPagination()).orElse(Pagination.ALL);
        List<Payment> transactionHistoryList = paymentMapper.selectTransactionHistoryListByParams(
                params, CommonUtil.getLimitOffset(pagination.pageNum(), pagination.pageSize()), pagination.pageSize());
        views.addAll(transactionHistoryList.stream()
                .map(bo -> {
                    TransactionHistoryView view = TransactionHistoryMapper.INSTANCE.boToView(bo);
                    view.setStatus(bo.getStatusString());
                    view.setStripePaymentMethod(StripePaymentMethodEnum.valueOf(bo.getStripePaymentMethod()));
                    view.setSquarePaymentMethod(SquarePaymentMethodEnum.valueOf(bo.getSquarePaymentMethod()));
                    return view;
                })
                .toList());

        return result;
    }

    public StripePaymentIntentView selectTransactionHistoryById(String id) {
        try {
            var intent = PaymentIntent.retrieve(id);

            // 如果是分账的模式下，application fee的获取方式有变化
            Long applicationFee = intent.getApplicationFeeAmount();
            if (applicationFee == null) {
                applicationFee =
                        Long.valueOf(intent.getMetadata().get(PaymentStripeStatus.SEPARATE_CHARGE_APPLICATION_FEE));
            }
            return StripePaymentIntentView.builder()
                    .id(intent.getId())
                    .amount(intent.getAmount())
                    .amountCapturable(intent.getAmountCapturable())
                    .amountDetails(intent.getAmountDetails())
                    .amountReceived(intent.getAmountReceived())
                    .application(intent.getApplication())
                    .applicationFeeAmount(applicationFee)
                    .automaticPaymentMethods(intent.getAutomaticPaymentMethods())
                    .canceledAt(intent.getCanceledAt())
                    .cancellationReason(intent.getCancellationReason())
                    .captureMethod(intent.getCaptureMethod())
                    .clientSecret(intent.getClientSecret())
                    .confirmationMethod(intent.getConfirmationMethod())
                    .created(intent.getCreated())
                    .customer(intent.getCustomer())
                    .currency(intent.getCurrency())
                    .description(intent.getDescription())
                    .invoice(intent.getInvoice())
                    .lastPaymentError(intent.getLastPaymentError())
                    .latestCharge(intent.getLatestCharge())
                    .livemode(intent.getLivemode())
                    .metadata(intent.getMetadata())
                    .nextAction(intent.getNextAction())
                    .object(intent.getObject())
                    .onBehalfOf(intent.getOnBehalfOf())
                    .paymentMethod(intent.getPaymentMethod())
                    .paymentMethodOptions(intent.getPaymentMethodOptions())
                    .paymentMethodTypes(intent.getPaymentMethodTypes())
                    .processing(intent.getProcessing())
                    .receiptEmail(intent.getReceiptEmail())
                    .review(intent.getReview())
                    .setupFutureUsage(intent.getSetupFutureUsage())
                    .shipping(intent.getShipping())
                    .source(intent.getSource())
                    .statementDescriptor(intent.getStatementDescriptor())
                    .statementDescriptorSuffix(intent.getStatementDescriptorSuffix())
                    .status(intent.getStatus())
                    .transferData(intent.getTransferData())
                    .transferGroup(intent.getTransferGroup())
                    .build();
        } catch (StripeException e) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, String.format("payment intent not found: %s", e.getMessage()));
        }
    }

    /**
     * stripe refund api
     *
     * @param params refund params
     * @see <a href="https://stripe.com/docs/api/refunds/create">create refund </a>
     */
    public String clientRefund(StripeRefundParams params) {
        if (StringUtil.isNullOrEmpty(params.getPaymentIntent())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "No payment intent, please check");
        }
        Payment payment = paymentMapper.selectByStripeIntentId(params.getPaymentIntent());
        if (payment == null) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR,
                    "No payment intent, please check, paymentIntent=" + params.getPaymentIntent());
        }
        Map<String, String> meta = new HashMap<>();
        meta.put(StripeApi.REFUND_SOURCE, StripeApi.ADMIN);

        var refundBuilder = RefundCreateParams.builder()
                .setMetadata(meta)
                .setAmount(params.getAmount())
                .setPaymentIntent(params.getPaymentIntent());
        // 分账的场景下不能直接设置reverse transfer
        if (!splitPaymentService.checkRecordExist(payment.getId(), SplitSyncRecordTypeEnum.PAYMENT)) {
            refundBuilder.setReverseTransfer(true);
        }

        Optional.ofNullable(params.getReason())
                .filter(paramsReason -> !paramsReason.isBlank())
                .ifPresent(reason -> refundBuilder.setReason(RefundCreateParams.Reason.valueOf(params.getReason())));
        return createRefund(refundBuilder.build());
    }

    public String businessRefund(StripeRefundParams params) {
        if (StringUtil.isNullOrEmpty(params.getCharge())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "No charge, please check");
        }
        Map<String, String> meta = new HashMap<>();
        meta.put(StripeApi.REFUND_SOURCE, StripeApi.ADMIN);
        var refundBuilder = RefundCreateParams.builder()
                .setMetadata(meta)
                .setAmount(params.getAmount())
                .setCharge(params.getCharge());

        Optional.ofNullable(params.getReason())
                .filter(paramsReason -> !paramsReason.isBlank())
                .ifPresent(reason -> refundBuilder.setReason(RefundCreateParams.Reason.valueOf(params.getReason())));

        return createRefund(refundBuilder.build());
    }

    private String createRefund(RefundCreateParams params) {
        try {
            return Refund.create(params).getId();
        } catch (StripeException e) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, String.format("refund error: %s", e.getMessage()));
        }
    }

    public TransactionHistoryListDTO getTransactionHistoryListByIntent(List<String> paymentIntentIds) {
        var builder = TransactionHistoryListDTO.builder();
        var payments = paymentMapper.selectByStripeIntentIds(paymentIntentIds);
        if (payments.isEmpty()) return builder.build();
        return builder.views(payments.stream()
                        .map(bo -> {
                            // payments 映射 transaction history view
                            TransactionHistoryView view = TransactionHistoryMapper.INSTANCE.boToView(bo);
                            view.setStatus(bo.getStatusString());
                            view.setStripePaymentMethod(StripePaymentMethodEnum.valueOf(bo.getStripePaymentMethod()));
                            view.setSquarePaymentMethod(SquarePaymentMethodEnum.valueOf(bo.getSquarePaymentMethod()));
                            return view;
                        })
                        .toList())
                .total(payments.size())
                .build();
    }
}

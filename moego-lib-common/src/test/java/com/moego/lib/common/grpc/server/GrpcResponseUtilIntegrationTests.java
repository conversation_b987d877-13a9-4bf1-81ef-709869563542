package com.moego.lib.common.grpc.server;

import static com.moego.lib.common.GrpcServerPortSupplier.getPort;
import static org.assertj.core.api.Assertions.assertThat;

import com.moego.lib.common.EchoSvcController;
import com.moego.lib.common.GrpcServerPortSupplier;
import com.moego.lib.common.grpc.GrpcUtil;
import com.moego.model.echo.v1.EchoRequest;
import com.moego.model.echo.v1.EchoResponse;
import com.moego.svc.echo.v1.EchoGrpc;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import io.grpc.Metadata;
import io.grpc.stub.MetadataUtils;
import java.util.concurrent.atomic.AtomicReference;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * {@link GrpcResponseUtil} tester.
 *
 * <AUTHOR>
 */
public class GrpcResponseUtilIntegrationTests {

    static ConfigurableApplicationContext ctx;
    static ManagedChannel channel;
    static AtomicReference<Metadata> header = new AtomicReference<>();
    static AtomicReference<Metadata> trailers = new AtomicReference<>();

    @BeforeAll
    static void beforeAll() {
        ctx = new SpringApplicationBuilder(Config.class)
                .web(WebApplicationType.NONE)
                .properties("moego.grpc.server.port=0")
                .run();
        channel = ManagedChannelBuilder.forAddress("localhost", getPort())
                .intercept(MetadataUtils.newCaptureMetadataInterceptor(header, trailers))
                .usePlaintext()
                .build();
    }

    @AfterAll
    static void afterAll() {
        if (ctx != null) {
            ctx.close();
        }
        GrpcUtil.shutdownChannel(channel);
    }

    @Test
    void testGrpcResponses() {
        EchoGrpc.EchoBlockingStub client = EchoGrpc.newBlockingStub(channel);

        EchoResponse response =
                client.echo(EchoRequest.newBuilder().setMessage("hello").build());
        assertThat(response.getMessage()).isEqualTo("hello");

        Metadata metadata = header.get();
        assertThat(metadata).isNotNull();
        assertThat(metadata.keys()).contains("response", "response2");
        assertThat(metadata.get(Metadata.Key.of("response", Metadata.ASCII_STRING_MARSHALLER)))
                .isEqualTo("hello");
        assertThat(metadata.get(Metadata.Key.of("response2", Metadata.ASCII_STRING_MARSHALLER)))
                .isEqualTo("hello");
    }

    @Configuration(proxyBeanMethods = false)
    @EnableAutoConfiguration
    @Import({EchoSvcController.class, GrpcServerPortSupplier.class})
    static class Config {}
}

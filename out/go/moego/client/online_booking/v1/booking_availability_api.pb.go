// @since 2024-03-27 10:22:39
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/client/online_booking/v1/booking_availability_api.proto

package onlinebookingapipb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	v13 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	v15 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/grooming/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1"
	v14 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/enterprise/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get available service item types params
type GetAvailableServiceItemTypesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*GetAvailableServiceItemTypesParams_Name
	//	*GetAvailableServiceItemTypesParams_Domain
	Anonymous isGetAvailableServiceItemTypesParams_Anonymous `protobuf_oneof:"anonymous"`
}

func (x *GetAvailableServiceItemTypesParams) Reset() {
	*x = GetAvailableServiceItemTypesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableServiceItemTypesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableServiceItemTypesParams) ProtoMessage() {}

func (x *GetAvailableServiceItemTypesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableServiceItemTypesParams.ProtoReflect.Descriptor instead.
func (*GetAvailableServiceItemTypesParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{0}
}

func (m *GetAvailableServiceItemTypesParams) GetAnonymous() isGetAvailableServiceItemTypesParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *GetAvailableServiceItemTypesParams) GetName() string {
	if x, ok := x.GetAnonymous().(*GetAvailableServiceItemTypesParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *GetAvailableServiceItemTypesParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*GetAvailableServiceItemTypesParams_Domain); ok {
		return x.Domain
	}
	return ""
}

type isGetAvailableServiceItemTypesParams_Anonymous interface {
	isGetAvailableServiceItemTypesParams_Anonymous()
}

type GetAvailableServiceItemTypesParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type GetAvailableServiceItemTypesParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*GetAvailableServiceItemTypesParams_Name) isGetAvailableServiceItemTypesParams_Anonymous() {}

func (*GetAvailableServiceItemTypesParams_Domain) isGetAvailableServiceItemTypesParams_Anonymous() {}

// get available service item types result
type GetAvailableServiceItemTypesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// available service item types
	ServiceItemTypes []v1.ServiceItemType `protobuf:"varint,1,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
}

func (x *GetAvailableServiceItemTypesResult) Reset() {
	*x = GetAvailableServiceItemTypesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableServiceItemTypesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableServiceItemTypesResult) ProtoMessage() {}

func (x *GetAvailableServiceItemTypesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableServiceItemTypesResult.ProtoReflect.Descriptor instead.
func (*GetAvailableServiceItemTypesResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetAvailableServiceItemTypesResult) GetServiceItemTypes() []v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

// get available pets params
type GetAvailablePetsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*GetAvailablePetsParams_Name
	//	*GetAvailablePetsParams_Domain
	Anonymous isGetAvailablePetsParams_Anonymous `protobuf_oneof:"anonymous"`
	// selected service item type
	ServiceItemType *v1.ServiceItemType `protobuf:"varint,3,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType,oneof" json:"service_item_type,omitempty"`
}

func (x *GetAvailablePetsParams) Reset() {
	*x = GetAvailablePetsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailablePetsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailablePetsParams) ProtoMessage() {}

func (x *GetAvailablePetsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailablePetsParams.ProtoReflect.Descriptor instead.
func (*GetAvailablePetsParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{2}
}

func (m *GetAvailablePetsParams) GetAnonymous() isGetAvailablePetsParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *GetAvailablePetsParams) GetName() string {
	if x, ok := x.GetAnonymous().(*GetAvailablePetsParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *GetAvailablePetsParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*GetAvailablePetsParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *GetAvailablePetsParams) GetServiceItemType() v1.ServiceItemType {
	if x != nil && x.ServiceItemType != nil {
		return *x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

type isGetAvailablePetsParams_Anonymous interface {
	isGetAvailablePetsParams_Anonymous()
}

type GetAvailablePetsParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type GetAvailablePetsParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*GetAvailablePetsParams_Name) isGetAvailablePetsParams_Anonymous() {}

func (*GetAvailablePetsParams_Domain) isGetAvailablePetsParams_Anonymous() {}

// get available pets result
type GetAvailablePetsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// available pets
	Pets []*GetAvailablePetResult `protobuf:"bytes,1,rep,name=pets,proto3" json:"pets,omitempty"`
}

func (x *GetAvailablePetsResult) Reset() {
	*x = GetAvailablePetsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailablePetsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailablePetsResult) ProtoMessage() {}

func (x *GetAvailablePetsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailablePetsResult.ProtoReflect.Descriptor instead.
func (*GetAvailablePetsResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{3}
}

func (x *GetAvailablePetsResult) GetPets() []*GetAvailablePetResult {
	if x != nil {
		return x.Pets
	}
	return nil
}

// get available pet result
type GetAvailablePetResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business pet info
	Pet *v11.BusinessCustomerPetOnlineBookingView `protobuf:"bytes,1,opt,name=pet,proto3" json:"pet,omitempty"`
	// ob custom question answers
	QuestionAnswers map[string]string `protobuf:"bytes,2,rep,name=question_answers,json=questionAnswers,proto3" json:"question_answers,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// pet vaccine list
	VaccineRecords []*v11.BusinessPetVaccineRecordModel `protobuf:"bytes,3,rep,name=vaccine_records,json=vaccineRecords,proto3" json:"vaccine_records,omitempty"`
	// pet availability, unavailable reasons
	UnavailableReasons []v12.PetUnavailableReason `protobuf:"varint,4,rep,packed,name=unavailable_reasons,json=unavailableReasons,proto3,enum=moego.models.online_booking.v1.PetUnavailableReason" json:"unavailable_reasons,omitempty"`
}

func (x *GetAvailablePetResult) Reset() {
	*x = GetAvailablePetResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailablePetResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailablePetResult) ProtoMessage() {}

func (x *GetAvailablePetResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailablePetResult.ProtoReflect.Descriptor instead.
func (*GetAvailablePetResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{4}
}

func (x *GetAvailablePetResult) GetPet() *v11.BusinessCustomerPetOnlineBookingView {
	if x != nil {
		return x.Pet
	}
	return nil
}

func (x *GetAvailablePetResult) GetQuestionAnswers() map[string]string {
	if x != nil {
		return x.QuestionAnswers
	}
	return nil
}

func (x *GetAvailablePetResult) GetVaccineRecords() []*v11.BusinessPetVaccineRecordModel {
	if x != nil {
		return x.VaccineRecords
	}
	return nil
}

func (x *GetAvailablePetResult) GetUnavailableReasons() []v12.PetUnavailableReason {
	if x != nil {
		return x.UnavailableReasons
	}
	return nil
}

// get available dates params
type GetAvailableDatesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*GetAvailableDatesParams_Name
	//	*GetAvailableDatesParams_Domain
	Anonymous isGetAvailableDatesParams_Anonymous `protobuf_oneof:"anonymous"`
	// selected service item type
	ServiceItemType v1.ServiceItemType `protobuf:"varint,3,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// date from
	StartDate *date.Date `protobuf:"bytes,4,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
	// date to
	EndDate *date.Date `protobuf:"bytes,5,opt,name=end_date,json=endDate,proto3,oneof" json:"end_date,omitempty"`
}

func (x *GetAvailableDatesParams) Reset() {
	*x = GetAvailableDatesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableDatesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableDatesParams) ProtoMessage() {}

func (x *GetAvailableDatesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableDatesParams.ProtoReflect.Descriptor instead.
func (*GetAvailableDatesParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{5}
}

func (m *GetAvailableDatesParams) GetAnonymous() isGetAvailableDatesParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *GetAvailableDatesParams) GetName() string {
	if x, ok := x.GetAnonymous().(*GetAvailableDatesParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *GetAvailableDatesParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*GetAvailableDatesParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *GetAvailableDatesParams) GetServiceItemType() v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

func (x *GetAvailableDatesParams) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *GetAvailableDatesParams) GetEndDate() *date.Date {
	if x != nil {
		return x.EndDate
	}
	return nil
}

type isGetAvailableDatesParams_Anonymous interface {
	isGetAvailableDatesParams_Anonymous()
}

type GetAvailableDatesParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type GetAvailableDatesParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*GetAvailableDatesParams_Name) isGetAvailableDatesParams_Anonymous() {}

func (*GetAvailableDatesParams_Domain) isGetAvailableDatesParams_Anonymous() {}

// get available dates result
type GetAvailableDatesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// available start date, yyyy-MM-dd
	AvailableStartDate *date.Date `protobuf:"bytes,1,opt,name=available_start_date,json=availableStartDate,proto3" json:"available_start_date,omitempty"`
	// available end date, yyyy-MM-dd
	AvailableEndDate *date.Date `protobuf:"bytes,2,opt,name=available_end_date,json=availableEndDate,proto3" json:"available_end_date,omitempty"`
	// unavailable dates, yyyy-MM-dd
	//
	// Deprecated: Do not use.
	UnavailableDates []*date.Date `protobuf:"bytes,3,rep,name=unavailable_dates,json=unavailableDates,proto3" json:"unavailable_dates,omitempty"`
	// unavailable arrival dates
	UnavailableArrivalDates []*date.Date `protobuf:"bytes,4,rep,name=unavailable_arrival_dates,json=unavailableArrivalDates,proto3" json:"unavailable_arrival_dates,omitempty"`
	// unavailable pick up dates
	UnavailablePickUpDates []*date.Date `protobuf:"bytes,5,rep,name=unavailable_pick_up_dates,json=unavailablePickUpDates,proto3" json:"unavailable_pick_up_dates,omitempty"`
}

func (x *GetAvailableDatesResult) Reset() {
	*x = GetAvailableDatesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableDatesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableDatesResult) ProtoMessage() {}

func (x *GetAvailableDatesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableDatesResult.ProtoReflect.Descriptor instead.
func (*GetAvailableDatesResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{6}
}

func (x *GetAvailableDatesResult) GetAvailableStartDate() *date.Date {
	if x != nil {
		return x.AvailableStartDate
	}
	return nil
}

func (x *GetAvailableDatesResult) GetAvailableEndDate() *date.Date {
	if x != nil {
		return x.AvailableEndDate
	}
	return nil
}

// Deprecated: Do not use.
func (x *GetAvailableDatesResult) GetUnavailableDates() []*date.Date {
	if x != nil {
		return x.UnavailableDates
	}
	return nil
}

func (x *GetAvailableDatesResult) GetUnavailableArrivalDates() []*date.Date {
	if x != nil {
		return x.UnavailableArrivalDates
	}
	return nil
}

func (x *GetAvailableDatesResult) GetUnavailablePickUpDates() []*date.Date {
	if x != nil {
		return x.UnavailablePickUpDates
	}
	return nil
}

// get available time ranges params
type GetAvailableTimeRangesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*GetAvailableTimeRangesParams_Name
	//	*GetAvailableTimeRangesParams_Domain
	Anonymous isGetAvailableTimeRangesParams_Anonymous `protobuf_oneof:"anonymous"`
	// selected service item type
	ServiceItemType v1.ServiceItemType `protobuf:"varint,3,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// date from
	StartDate *date.Date `protobuf:"bytes,4,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// date to
	EndDate *date.Date `protobuf:"bytes,5,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// pet 和选择的 service 信息
	PetServices []*GetAvailableTimeRangesParams_PetServices `protobuf:"bytes,6,rep,name=pet_services,json=petServices,proto3" json:"pet_services,omitempty"`
}

func (x *GetAvailableTimeRangesParams) Reset() {
	*x = GetAvailableTimeRangesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableTimeRangesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableTimeRangesParams) ProtoMessage() {}

func (x *GetAvailableTimeRangesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableTimeRangesParams.ProtoReflect.Descriptor instead.
func (*GetAvailableTimeRangesParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{7}
}

func (m *GetAvailableTimeRangesParams) GetAnonymous() isGetAvailableTimeRangesParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *GetAvailableTimeRangesParams) GetName() string {
	if x, ok := x.GetAnonymous().(*GetAvailableTimeRangesParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *GetAvailableTimeRangesParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*GetAvailableTimeRangesParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *GetAvailableTimeRangesParams) GetServiceItemType() v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

func (x *GetAvailableTimeRangesParams) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *GetAvailableTimeRangesParams) GetEndDate() *date.Date {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *GetAvailableTimeRangesParams) GetPetServices() []*GetAvailableTimeRangesParams_PetServices {
	if x != nil {
		return x.PetServices
	}
	return nil
}

type isGetAvailableTimeRangesParams_Anonymous interface {
	isGetAvailableTimeRangesParams_Anonymous()
}

type GetAvailableTimeRangesParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type GetAvailableTimeRangesParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*GetAvailableTimeRangesParams_Name) isGetAvailableTimeRangesParams_Anonymous() {}

func (*GetAvailableTimeRangesParams_Domain) isGetAvailableTimeRangesParams_Anonymous() {}

// get available time ranges result
type GetAvailableTimeRangesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// available time ranges
	DayTimeRanges []*GetAvailableTimeRangesResult_DayAvailableTimeRanges `protobuf:"bytes,1,rep,name=day_time_ranges,json=dayTimeRanges,proto3" json:"day_time_ranges,omitempty"`
}

func (x *GetAvailableTimeRangesResult) Reset() {
	*x = GetAvailableTimeRangesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableTimeRangesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableTimeRangesResult) ProtoMessage() {}

func (x *GetAvailableTimeRangesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableTimeRangesResult.ProtoReflect.Descriptor instead.
func (*GetAvailableTimeRangesResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{8}
}

func (x *GetAvailableTimeRangesResult) GetDayTimeRanges() []*GetAvailableTimeRangesResult_DayAvailableTimeRanges {
	if x != nil {
		return x.DayTimeRanges
	}
	return nil
}

// get business working hour params
type GetBusinessWorkingHourParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*GetBusinessWorkingHourParams_Name
	//	*GetBusinessWorkingHourParams_Domain
	Anonymous isGetBusinessWorkingHourParams_Anonymous `protobuf_oneof:"anonymous"`
}

func (x *GetBusinessWorkingHourParams) Reset() {
	*x = GetBusinessWorkingHourParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBusinessWorkingHourParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBusinessWorkingHourParams) ProtoMessage() {}

func (x *GetBusinessWorkingHourParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBusinessWorkingHourParams.ProtoReflect.Descriptor instead.
func (*GetBusinessWorkingHourParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{9}
}

func (m *GetBusinessWorkingHourParams) GetAnonymous() isGetBusinessWorkingHourParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *GetBusinessWorkingHourParams) GetName() string {
	if x, ok := x.GetAnonymous().(*GetBusinessWorkingHourParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *GetBusinessWorkingHourParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*GetBusinessWorkingHourParams_Domain); ok {
		return x.Domain
	}
	return ""
}

type isGetBusinessWorkingHourParams_Anonymous interface {
	isGetBusinessWorkingHourParams_Anonymous()
}

type GetBusinessWorkingHourParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type GetBusinessWorkingHourParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*GetBusinessWorkingHourParams_Name) isGetBusinessWorkingHourParams_Anonymous() {}

func (*GetBusinessWorkingHourParams_Domain) isGetBusinessWorkingHourParams_Anonymous() {}

// get business working hour result
type GetBusinessWorkingHourResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// monday
	Monday []*v12.TimeRangeModel `protobuf:"bytes,1,rep,name=monday,proto3" json:"monday,omitempty"`
	// tuesday
	Tuesday []*v12.TimeRangeModel `protobuf:"bytes,2,rep,name=tuesday,proto3" json:"tuesday,omitempty"`
	// wednesday
	Wednesday []*v12.TimeRangeModel `protobuf:"bytes,3,rep,name=wednesday,proto3" json:"wednesday,omitempty"`
	// thursday
	Thursday []*v12.TimeRangeModel `protobuf:"bytes,4,rep,name=thursday,proto3" json:"thursday,omitempty"`
	// friday
	Friday []*v12.TimeRangeModel `protobuf:"bytes,5,rep,name=friday,proto3" json:"friday,omitempty"`
	// saturday
	Saturday []*v12.TimeRangeModel `protobuf:"bytes,6,rep,name=saturday,proto3" json:"saturday,omitempty"`
	// sunday
	Sunday []*v12.TimeRangeModel `protobuf:"bytes,7,rep,name=sunday,proto3" json:"sunday,omitempty"`
}

func (x *GetBusinessWorkingHourResult) Reset() {
	*x = GetBusinessWorkingHourResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBusinessWorkingHourResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBusinessWorkingHourResult) ProtoMessage() {}

func (x *GetBusinessWorkingHourResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBusinessWorkingHourResult.ProtoReflect.Descriptor instead.
func (*GetBusinessWorkingHourResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{10}
}

func (x *GetBusinessWorkingHourResult) GetMonday() []*v12.TimeRangeModel {
	if x != nil {
		return x.Monday
	}
	return nil
}

func (x *GetBusinessWorkingHourResult) GetTuesday() []*v12.TimeRangeModel {
	if x != nil {
		return x.Tuesday
	}
	return nil
}

func (x *GetBusinessWorkingHourResult) GetWednesday() []*v12.TimeRangeModel {
	if x != nil {
		return x.Wednesday
	}
	return nil
}

func (x *GetBusinessWorkingHourResult) GetThursday() []*v12.TimeRangeModel {
	if x != nil {
		return x.Thursday
	}
	return nil
}

func (x *GetBusinessWorkingHourResult) GetFriday() []*v12.TimeRangeModel {
	if x != nil {
		return x.Friday
	}
	return nil
}

func (x *GetBusinessWorkingHourResult) GetSaturday() []*v12.TimeRangeModel {
	if x != nil {
		return x.Saturday
	}
	return nil
}

func (x *GetBusinessWorkingHourResult) GetSunday() []*v12.TimeRangeModel {
	if x != nil {
		return x.Sunday
	}
	return nil
}

// get pet available services params
type GetPetAvailableServicesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*GetPetAvailableServicesParams_Name
	//	*GetPetAvailableServicesParams_Domain
	Anonymous isGetPetAvailableServicesParams_Anonymous `protobuf_oneof:"anonymous"`
	// selected service item type
	ServiceItemType v1.ServiceItemType `protobuf:"varint,3,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// selected service type
	ServiceType v1.ServiceType `protobuf:"varint,4,opt,name=service_type,json=serviceType,proto3,enum=moego.models.offering.v1.ServiceType" json:"service_type,omitempty"`
	// selected pet
	Pets []*v12.BookingPetDef `protobuf:"bytes,5,rep,name=pets,proto3" json:"pets,omitempty"`
	// selected services, used to filter available addons
	SelectedServiceIds []int64 `protobuf:"varint,6,rep,packed,name=selected_service_ids,json=selectedServiceIds,proto3" json:"selected_service_ids,omitempty"`
	// start date
	StartDate *date.Date `protobuf:"bytes,7,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
	// end date
	EndDate *date.Date `protobuf:"bytes,8,opt,name=end_date,json=endDate,proto3,oneof" json:"end_date,omitempty"`
	// dates of the daycare
	SpecificDates []*date.Date `protobuf:"bytes,9,rep,name=specific_dates,json=specificDates,proto3" json:"specific_dates,omitempty"`
	// if book with other service item type, default is false
	BookWithOtherServiceItemType *bool `protobuf:"varint,10,opt,name=book_with_other_service_item_type,json=bookWithOtherServiceItemType,proto3,oneof" json:"book_with_other_service_item_type,omitempty"`
}

func (x *GetPetAvailableServicesParams) Reset() {
	*x = GetPetAvailableServicesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPetAvailableServicesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetAvailableServicesParams) ProtoMessage() {}

func (x *GetPetAvailableServicesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetAvailableServicesParams.ProtoReflect.Descriptor instead.
func (*GetPetAvailableServicesParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{11}
}

func (m *GetPetAvailableServicesParams) GetAnonymous() isGetPetAvailableServicesParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *GetPetAvailableServicesParams) GetName() string {
	if x, ok := x.GetAnonymous().(*GetPetAvailableServicesParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *GetPetAvailableServicesParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*GetPetAvailableServicesParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *GetPetAvailableServicesParams) GetServiceItemType() v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

func (x *GetPetAvailableServicesParams) GetServiceType() v1.ServiceType {
	if x != nil {
		return x.ServiceType
	}
	return v1.ServiceType(0)
}

func (x *GetPetAvailableServicesParams) GetPets() []*v12.BookingPetDef {
	if x != nil {
		return x.Pets
	}
	return nil
}

func (x *GetPetAvailableServicesParams) GetSelectedServiceIds() []int64 {
	if x != nil {
		return x.SelectedServiceIds
	}
	return nil
}

func (x *GetPetAvailableServicesParams) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *GetPetAvailableServicesParams) GetEndDate() *date.Date {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *GetPetAvailableServicesParams) GetSpecificDates() []*date.Date {
	if x != nil {
		return x.SpecificDates
	}
	return nil
}

func (x *GetPetAvailableServicesParams) GetBookWithOtherServiceItemType() bool {
	if x != nil && x.BookWithOtherServiceItemType != nil {
		return *x.BookWithOtherServiceItemType
	}
	return false
}

type isGetPetAvailableServicesParams_Anonymous interface {
	isGetPetAvailableServicesParams_Anonymous()
}

type GetPetAvailableServicesParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type GetPetAvailableServicesParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*GetPetAvailableServicesParams_Name) isGetPetAvailableServicesParams_Anonymous() {}

func (*GetPetAvailableServicesParams_Domain) isGetPetAvailableServicesParams_Anonymous() {}

// get pet available services result
type GetPetAvailableServicesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// all services information
	Categories []*v1.CustomizedServiceCategoryView `protobuf:"bytes,1,rep,name=categories,proto3" json:"categories,omitempty"`
	// available services
	AvailableServiceIds []int64 `protobuf:"varint,2,rep,packed,name=available_service_ids,json=availableServiceIds,proto3" json:"available_service_ids,omitempty"`
	// pet's available services and customized service prices
	PetServices []*v12.BookingPetServiceDef `protobuf:"bytes,3,rep,name=pet_services,json=petServices,proto3" json:"pet_services,omitempty"`
	// book online service config
	ServiceConfigs []*v12.ServiceConfigView `protobuf:"bytes,4,rep,name=service_configs,json=serviceConfigs,proto3" json:"service_configs,omitempty"`
	// lodging occupied status. For boarding/daycare service
	ServiceCapacity map[int64]*GetPetAvailableServicesResult_ServiceCapacity `protobuf:"bytes,5,rep,name=service_capacity,json=serviceCapacity,proto3" json:"service_capacity,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// bundle sale services, deprecated, use service brief views instead
	//
	// Deprecated: Do not use.
	BundleSaleServices []*v1.ServiceBundleSaleView `protobuf:"bytes,6,rep,name=bundle_sale_services,json=bundleSaleServices,proto3" json:"bundle_sale_services,omitempty"`
	// service brief views
	ServiceBriefViews []*v1.ServiceBriefView `protobuf:"bytes,7,rep,name=service_brief_views,json=serviceBriefViews,proto3" json:"service_brief_views,omitempty"`
}

func (x *GetPetAvailableServicesResult) Reset() {
	*x = GetPetAvailableServicesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPetAvailableServicesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetAvailableServicesResult) ProtoMessage() {}

func (x *GetPetAvailableServicesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetAvailableServicesResult.ProtoReflect.Descriptor instead.
func (*GetPetAvailableServicesResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{12}
}

func (x *GetPetAvailableServicesResult) GetCategories() []*v1.CustomizedServiceCategoryView {
	if x != nil {
		return x.Categories
	}
	return nil
}

func (x *GetPetAvailableServicesResult) GetAvailableServiceIds() []int64 {
	if x != nil {
		return x.AvailableServiceIds
	}
	return nil
}

func (x *GetPetAvailableServicesResult) GetPetServices() []*v12.BookingPetServiceDef {
	if x != nil {
		return x.PetServices
	}
	return nil
}

func (x *GetPetAvailableServicesResult) GetServiceConfigs() []*v12.ServiceConfigView {
	if x != nil {
		return x.ServiceConfigs
	}
	return nil
}

func (x *GetPetAvailableServicesResult) GetServiceCapacity() map[int64]*GetPetAvailableServicesResult_ServiceCapacity {
	if x != nil {
		return x.ServiceCapacity
	}
	return nil
}

// Deprecated: Do not use.
func (x *GetPetAvailableServicesResult) GetBundleSaleServices() []*v1.ServiceBundleSaleView {
	if x != nil {
		return x.BundleSaleServices
	}
	return nil
}

func (x *GetPetAvailableServicesResult) GetServiceBriefViews() []*v1.ServiceBriefView {
	if x != nil {
		return x.ServiceBriefViews
	}
	return nil
}

// get available evaluation test list params
type GetAvailableEvaluationListParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*GetAvailableEvaluationListParams_Name
	//	*GetAvailableEvaluationListParams_Domain
	Anonymous isGetAvailableEvaluationListParams_Anonymous `protobuf_oneof:"anonymous"`
	// service item type
	ServiceItemType *v1.ServiceItemType `protobuf:"varint,3,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType,oneof" json:"service_item_type,omitempty"`
	// selected pet
	Pets []*v12.BookingPetDef `protobuf:"bytes,5,rep,name=pets,proto3" json:"pets,omitempty"`
}

func (x *GetAvailableEvaluationListParams) Reset() {
	*x = GetAvailableEvaluationListParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableEvaluationListParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableEvaluationListParams) ProtoMessage() {}

func (x *GetAvailableEvaluationListParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableEvaluationListParams.ProtoReflect.Descriptor instead.
func (*GetAvailableEvaluationListParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{13}
}

func (m *GetAvailableEvaluationListParams) GetAnonymous() isGetAvailableEvaluationListParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *GetAvailableEvaluationListParams) GetName() string {
	if x, ok := x.GetAnonymous().(*GetAvailableEvaluationListParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *GetAvailableEvaluationListParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*GetAvailableEvaluationListParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *GetAvailableEvaluationListParams) GetServiceItemType() v1.ServiceItemType {
	if x != nil && x.ServiceItemType != nil {
		return *x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

func (x *GetAvailableEvaluationListParams) GetPets() []*v12.BookingPetDef {
	if x != nil {
		return x.Pets
	}
	return nil
}

type isGetAvailableEvaluationListParams_Anonymous interface {
	isGetAvailableEvaluationListParams_Anonymous()
}

type GetAvailableEvaluationListParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type GetAvailableEvaluationListParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*GetAvailableEvaluationListParams_Name) isGetAvailableEvaluationListParams_Anonymous() {}

func (*GetAvailableEvaluationListParams_Domain) isGetAvailableEvaluationListParams_Anonymous() {}

// get available evaluation test list result
type GetAvailableEvaluationListResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// available evaluation tests
	Evaluations []*v1.EvaluationBriefView `protobuf:"bytes,1,rep,name=evaluations,proto3" json:"evaluations,omitempty"`
	// available services
	AvailableEvaluationIds []int64 `protobuf:"varint,2,rep,packed,name=available_evaluation_ids,json=availableEvaluationIds,proto3" json:"available_evaluation_ids,omitempty"`
}

func (x *GetAvailableEvaluationListResult) Reset() {
	*x = GetAvailableEvaluationListResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableEvaluationListResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableEvaluationListResult) ProtoMessage() {}

func (x *GetAvailableEvaluationListResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableEvaluationListResult.ProtoReflect.Descriptor instead.
func (*GetAvailableEvaluationListResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{14}
}

func (x *GetAvailableEvaluationListResult) GetEvaluations() []*v1.EvaluationBriefView {
	if x != nil {
		return x.Evaluations
	}
	return nil
}

func (x *GetAvailableEvaluationListResult) GetAvailableEvaluationIds() []int64 {
	if x != nil {
		return x.AvailableEvaluationIds
	}
	return nil
}

// get accepted pet types in online booking
type GetAcceptedPetTypesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*GetAcceptedPetTypesParams_Name
	//	*GetAcceptedPetTypesParams_Domain
	Anonymous isGetAcceptedPetTypesParams_Anonymous `protobuf_oneof:"anonymous"`
	// service item type
	ServiceItemType v1.ServiceItemType `protobuf:"varint,3,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
}

func (x *GetAcceptedPetTypesParams) Reset() {
	*x = GetAcceptedPetTypesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAcceptedPetTypesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAcceptedPetTypesParams) ProtoMessage() {}

func (x *GetAcceptedPetTypesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAcceptedPetTypesParams.ProtoReflect.Descriptor instead.
func (*GetAcceptedPetTypesParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{15}
}

func (m *GetAcceptedPetTypesParams) GetAnonymous() isGetAcceptedPetTypesParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *GetAcceptedPetTypesParams) GetName() string {
	if x, ok := x.GetAnonymous().(*GetAcceptedPetTypesParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *GetAcceptedPetTypesParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*GetAcceptedPetTypesParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *GetAcceptedPetTypesParams) GetServiceItemType() v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

type isGetAcceptedPetTypesParams_Anonymous interface {
	isGetAcceptedPetTypesParams_Anonymous()
}

type GetAcceptedPetTypesParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type GetAcceptedPetTypesParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*GetAcceptedPetTypesParams_Name) isGetAcceptedPetTypesParams_Anonymous() {}

func (*GetAcceptedPetTypesParams_Domain) isGetAcceptedPetTypesParams_Anonymous() {}

// get accepted pet types result
type GetAcceptedPetTypesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// accepted pet types
	PetTypes []v13.PetType `protobuf:"varint,1,rep,packed,name=pet_types,json=petTypes,proto3,enum=moego.models.customer.v1.PetType" json:"pet_types,omitempty"`
}

func (x *GetAcceptedPetTypesResult) Reset() {
	*x = GetAcceptedPetTypesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAcceptedPetTypesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAcceptedPetTypesResult) ProtoMessage() {}

func (x *GetAcceptedPetTypesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAcceptedPetTypesResult.ProtoReflect.Descriptor instead.
func (*GetAcceptedPetTypesResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{16}
}

func (x *GetAcceptedPetTypesResult) GetPetTypes() []v13.PetType {
	if x != nil {
		return x.PetTypes
	}
	return nil
}

// get accepted customer types in online booking
type GetAcceptedCustomerTypesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*GetAcceptedCustomerTypesParams_Name
	//	*GetAcceptedCustomerTypesParams_Domain
	Anonymous isGetAcceptedCustomerTypesParams_Anonymous `protobuf_oneof:"anonymous"`
}

func (x *GetAcceptedCustomerTypesParams) Reset() {
	*x = GetAcceptedCustomerTypesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAcceptedCustomerTypesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAcceptedCustomerTypesParams) ProtoMessage() {}

func (x *GetAcceptedCustomerTypesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAcceptedCustomerTypesParams.ProtoReflect.Descriptor instead.
func (*GetAcceptedCustomerTypesParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{17}
}

func (m *GetAcceptedCustomerTypesParams) GetAnonymous() isGetAcceptedCustomerTypesParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *GetAcceptedCustomerTypesParams) GetName() string {
	if x, ok := x.GetAnonymous().(*GetAcceptedCustomerTypesParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *GetAcceptedCustomerTypesParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*GetAcceptedCustomerTypesParams_Domain); ok {
		return x.Domain
	}
	return ""
}

type isGetAcceptedCustomerTypesParams_Anonymous interface {
	isGetAcceptedCustomerTypesParams_Anonymous()
}

type GetAcceptedCustomerTypesParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type GetAcceptedCustomerTypesParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*GetAcceptedCustomerTypesParams_Name) isGetAcceptedCustomerTypesParams_Anonymous() {}

func (*GetAcceptedCustomerTypesParams_Domain) isGetAcceptedCustomerTypesParams_Anonymous() {}

// get accepted customer types result
type GetAcceptedCustomerTypesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// accepted customer types
	AcceptCustomerTypes []*GetAcceptedCustomerTypesResult_AcceptCustomerType `protobuf:"bytes,1,rep,name=accept_customer_types,json=acceptCustomerTypes,proto3" json:"accept_customer_types,omitempty"`
}

func (x *GetAcceptedCustomerTypesResult) Reset() {
	*x = GetAcceptedCustomerTypesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAcceptedCustomerTypesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAcceptedCustomerTypesResult) ProtoMessage() {}

func (x *GetAcceptedCustomerTypesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAcceptedCustomerTypesResult.ProtoReflect.Descriptor instead.
func (*GetAcceptedCustomerTypesResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{18}
}

func (x *GetAcceptedCustomerTypesResult) GetAcceptCustomerTypes() []*GetAcceptedCustomerTypesResult_AcceptCustomerType {
	if x != nil {
		return x.AcceptCustomerTypes
	}
	return nil
}

// get time range params
type GetTimeRangeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*GetTimeRangeParams_Name
	//	*GetTimeRangeParams_Domain
	Anonymous isGetTimeRangeParams_Anonymous `protobuf_oneof:"anonymous"`
	// service item type
	ServiceItemType v1.ServiceItemType `protobuf:"varint,3,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
}

func (x *GetTimeRangeParams) Reset() {
	*x = GetTimeRangeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTimeRangeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTimeRangeParams) ProtoMessage() {}

func (x *GetTimeRangeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTimeRangeParams.ProtoReflect.Descriptor instead.
func (*GetTimeRangeParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{19}
}

func (m *GetTimeRangeParams) GetAnonymous() isGetTimeRangeParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *GetTimeRangeParams) GetName() string {
	if x, ok := x.GetAnonymous().(*GetTimeRangeParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *GetTimeRangeParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*GetTimeRangeParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *GetTimeRangeParams) GetServiceItemType() v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

type isGetTimeRangeParams_Anonymous interface {
	isGetTimeRangeParams_Anonymous()
}

type GetTimeRangeParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type GetTimeRangeParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*GetTimeRangeParams_Name) isGetTimeRangeParams_Anonymous() {}

func (*GetTimeRangeParams_Domain) isGetTimeRangeParams_Anonymous() {}

// get time range result
type GetTimeRangeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pick up time range
	ArrivalPickUpTimeRange *v12.ArrivalPickUpTimeDef `protobuf:"bytes,1,opt,name=arrival_pick_up_time_range,json=arrivalPickUpTimeRange,proto3" json:"arrival_pick_up_time_range,omitempty"`
}

func (x *GetTimeRangeResult) Reset() {
	*x = GetTimeRangeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTimeRangeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTimeRangeResult) ProtoMessage() {}

func (x *GetTimeRangeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTimeRangeResult.ProtoReflect.Descriptor instead.
func (*GetTimeRangeResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{20}
}

func (x *GetTimeRangeResult) GetArrivalPickUpTimeRange() *v12.ArrivalPickUpTimeDef {
	if x != nil {
		return x.ArrivalPickUpTimeRange
	}
	return nil
}

// GetAvailableGroupClassesParams params
type GetAvailableGroupClassesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*GetAvailableGroupClassesParams_Name
	//	*GetAvailableGroupClassesParams_Domain
	Anonymous isGetAvailableGroupClassesParams_Anonymous `protobuf_oneof:"anonymous"`
	// selected pet
	PetId int64 `protobuf:"varint,3,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,4,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *GetAvailableGroupClassesParams) Reset() {
	*x = GetAvailableGroupClassesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableGroupClassesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableGroupClassesParams) ProtoMessage() {}

func (x *GetAvailableGroupClassesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableGroupClassesParams.ProtoReflect.Descriptor instead.
func (*GetAvailableGroupClassesParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{21}
}

func (m *GetAvailableGroupClassesParams) GetAnonymous() isGetAvailableGroupClassesParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *GetAvailableGroupClassesParams) GetName() string {
	if x, ok := x.GetAnonymous().(*GetAvailableGroupClassesParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *GetAvailableGroupClassesParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*GetAvailableGroupClassesParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *GetAvailableGroupClassesParams) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *GetAvailableGroupClassesParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type isGetAvailableGroupClassesParams_Anonymous interface {
	isGetAvailableGroupClassesParams_Anonymous()
}

type GetAvailableGroupClassesParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type GetAvailableGroupClassesParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*GetAvailableGroupClassesParams_Name) isGetAvailableGroupClassesParams_Anonymous() {}

func (*GetAvailableGroupClassesParams_Domain) isGetAvailableGroupClassesParams_Anonymous() {}

// GetAvailableGroupClassesResult
type GetAvailableGroupClassesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// available group classes
	GroupClasses []*v1.GroupClassModel `protobuf:"bytes,1,rep,name=group_classes,json=groupClasses,proto3" json:"group_classes,omitempty"`
	// OB service configs
	ObServiceConfigs []*GetAvailableGroupClassesResult_OBServiceConfigView `protobuf:"bytes,2,rep,name=ob_service_configs,json=obServiceConfigs,proto3" json:"ob_service_configs,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *GetAvailableGroupClassesResult) Reset() {
	*x = GetAvailableGroupClassesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableGroupClassesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableGroupClassesResult) ProtoMessage() {}

func (x *GetAvailableGroupClassesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableGroupClassesResult.ProtoReflect.Descriptor instead.
func (*GetAvailableGroupClassesResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{22}
}

func (x *GetAvailableGroupClassesResult) GetGroupClasses() []*v1.GroupClassModel {
	if x != nil {
		return x.GroupClasses
	}
	return nil
}

func (x *GetAvailableGroupClassesResult) GetObServiceConfigs() []*GetAvailableGroupClassesResult_OBServiceConfigView {
	if x != nil {
		return x.ObServiceConfigs
	}
	return nil
}

func (x *GetAvailableGroupClassesResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// get available training class batches params
type GetPetAvailableGroupClassInstancesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*GetPetAvailableGroupClassInstancesParams_Name
	//	*GetPetAvailableGroupClassInstancesParams_Domain
	Anonymous isGetPetAvailableGroupClassInstancesParams_Anonymous `protobuf_oneof:"anonymous"`
	// group class id
	GroupClassId int64 `protobuf:"varint,3,opt,name=group_class_id,json=groupClassId,proto3" json:"group_class_id,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,4,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *GetPetAvailableGroupClassInstancesParams) Reset() {
	*x = GetPetAvailableGroupClassInstancesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPetAvailableGroupClassInstancesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetAvailableGroupClassInstancesParams) ProtoMessage() {}

func (x *GetPetAvailableGroupClassInstancesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetAvailableGroupClassInstancesParams.ProtoReflect.Descriptor instead.
func (*GetPetAvailableGroupClassInstancesParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{23}
}

func (m *GetPetAvailableGroupClassInstancesParams) GetAnonymous() isGetPetAvailableGroupClassInstancesParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *GetPetAvailableGroupClassInstancesParams) GetName() string {
	if x, ok := x.GetAnonymous().(*GetPetAvailableGroupClassInstancesParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *GetPetAvailableGroupClassInstancesParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*GetPetAvailableGroupClassInstancesParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *GetPetAvailableGroupClassInstancesParams) GetGroupClassId() int64 {
	if x != nil {
		return x.GroupClassId
	}
	return 0
}

func (x *GetPetAvailableGroupClassInstancesParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type isGetPetAvailableGroupClassInstancesParams_Anonymous interface {
	isGetPetAvailableGroupClassInstancesParams_Anonymous()
}

type GetPetAvailableGroupClassInstancesParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type GetPetAvailableGroupClassInstancesParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*GetPetAvailableGroupClassInstancesParams_Name) isGetPetAvailableGroupClassInstancesParams_Anonymous() {
}

func (*GetPetAvailableGroupClassInstancesParams_Domain) isGetPetAvailableGroupClassInstancesParams_Anonymous() {
}

// get available training class batches result
type GetPetAvailableGroupClassInstancesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// group instances with sessions
	GroupClassInstances []*GetPetAvailableGroupClassInstancesResult_GroupClassInstanceWithSessionsView `protobuf:"bytes,1,rep,name=group_class_instances,json=groupClassInstances,proto3" json:"group_class_instances,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *GetPetAvailableGroupClassInstancesResult) Reset() {
	*x = GetPetAvailableGroupClassInstancesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPetAvailableGroupClassInstancesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetAvailableGroupClassInstancesResult) ProtoMessage() {}

func (x *GetPetAvailableGroupClassInstancesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetAvailableGroupClassInstancesResult.ProtoReflect.Descriptor instead.
func (*GetPetAvailableGroupClassInstancesResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{24}
}

func (x *GetPetAvailableGroupClassInstancesResult) GetGroupClassInstances() []*GetPetAvailableGroupClassInstancesResult_GroupClassInstanceWithSessionsView {
	if x != nil {
		return x.GroupClassInstances
	}
	return nil
}

func (x *GetPetAvailableGroupClassInstancesResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// CheckSpecialEvaluationParams
type CheckSpecialEvaluationParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*CheckSpecialEvaluationParams_Name
	//	*CheckSpecialEvaluationParams_Domain
	Anonymous isCheckSpecialEvaluationParams_Anonymous `protobuf_oneof:"anonymous"`
	// the pet service ids
	PetServiceIds map[int64]*v14.CheckSpecialEvaluationRequest_ServiceIDs `protobuf:"bytes,3,rep,name=pet_service_ids,json=petServiceIds,proto3" json:"pet_service_ids,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *CheckSpecialEvaluationParams) Reset() {
	*x = CheckSpecialEvaluationParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckSpecialEvaluationParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckSpecialEvaluationParams) ProtoMessage() {}

func (x *CheckSpecialEvaluationParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckSpecialEvaluationParams.ProtoReflect.Descriptor instead.
func (*CheckSpecialEvaluationParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{25}
}

func (m *CheckSpecialEvaluationParams) GetAnonymous() isCheckSpecialEvaluationParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *CheckSpecialEvaluationParams) GetName() string {
	if x, ok := x.GetAnonymous().(*CheckSpecialEvaluationParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *CheckSpecialEvaluationParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*CheckSpecialEvaluationParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *CheckSpecialEvaluationParams) GetPetServiceIds() map[int64]*v14.CheckSpecialEvaluationRequest_ServiceIDs {
	if x != nil {
		return x.PetServiceIds
	}
	return nil
}

type isCheckSpecialEvaluationParams_Anonymous interface {
	isCheckSpecialEvaluationParams_Anonymous()
}

type CheckSpecialEvaluationParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type CheckSpecialEvaluationParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*CheckSpecialEvaluationParams_Name) isCheckSpecialEvaluationParams_Anonymous() {}

func (*CheckSpecialEvaluationParams_Domain) isCheckSpecialEvaluationParams_Anonymous() {}

// CheckSpecialEvaluationResult
type CheckSpecialEvaluationResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet service evaluation check results
	Results []*v14.CheckSpecialEvaluationResponse_Result `protobuf:"bytes,1,rep,name=results,proto3" json:"results,omitempty"`
}

func (x *CheckSpecialEvaluationResult) Reset() {
	*x = CheckSpecialEvaluationResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckSpecialEvaluationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckSpecialEvaluationResult) ProtoMessage() {}

func (x *CheckSpecialEvaluationResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckSpecialEvaluationResult.ProtoReflect.Descriptor instead.
func (*CheckSpecialEvaluationResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{26}
}

func (x *CheckSpecialEvaluationResult) GetResults() []*v14.CheckSpecialEvaluationResponse_Result {
	if x != nil {
		return x.Results
	}
	return nil
}

// pet 和选择的 service 信息
type GetAvailableTimeRangesParams_PetServices struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet info
	Pet *Pet `protobuf:"bytes,1,opt,name=pet,proto3" json:"pet,omitempty"`
	// pet selected services
	Services []*GetAvailableTimeRangesParams_PetServices_Service `protobuf:"bytes,2,rep,name=services,proto3" json:"services,omitempty"`
}

func (x *GetAvailableTimeRangesParams_PetServices) Reset() {
	*x = GetAvailableTimeRangesParams_PetServices{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableTimeRangesParams_PetServices) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableTimeRangesParams_PetServices) ProtoMessage() {}

func (x *GetAvailableTimeRangesParams_PetServices) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableTimeRangesParams_PetServices.ProtoReflect.Descriptor instead.
func (*GetAvailableTimeRangesParams_PetServices) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{7, 0}
}

func (x *GetAvailableTimeRangesParams_PetServices) GetPet() *Pet {
	if x != nil {
		return x.Pet
	}
	return nil
}

func (x *GetAvailableTimeRangesParams_PetServices) GetServices() []*GetAvailableTimeRangesParams_PetServices_Service {
	if x != nil {
		return x.Services
	}
	return nil
}

// selected services
type GetAvailableTimeRangesParams_PetServices_Service struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service type
	//
	// Types that are assignable to Service:
	//
	//	*GetAvailableTimeRangesParams_PetServices_Service_Evaluation_
	Service isGetAvailableTimeRangesParams_PetServices_Service_Service `protobuf_oneof:"service"`
}

func (x *GetAvailableTimeRangesParams_PetServices_Service) Reset() {
	*x = GetAvailableTimeRangesParams_PetServices_Service{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableTimeRangesParams_PetServices_Service) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableTimeRangesParams_PetServices_Service) ProtoMessage() {}

func (x *GetAvailableTimeRangesParams_PetServices_Service) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableTimeRangesParams_PetServices_Service.ProtoReflect.Descriptor instead.
func (*GetAvailableTimeRangesParams_PetServices_Service) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{7, 0, 0}
}

func (m *GetAvailableTimeRangesParams_PetServices_Service) GetService() isGetAvailableTimeRangesParams_PetServices_Service_Service {
	if m != nil {
		return m.Service
	}
	return nil
}

func (x *GetAvailableTimeRangesParams_PetServices_Service) GetEvaluation() *GetAvailableTimeRangesParams_PetServices_Service_Evaluation {
	if x, ok := x.GetService().(*GetAvailableTimeRangesParams_PetServices_Service_Evaluation_); ok {
		return x.Evaluation
	}
	return nil
}

type isGetAvailableTimeRangesParams_PetServices_Service_Service interface {
	isGetAvailableTimeRangesParams_PetServices_Service_Service()
}

type GetAvailableTimeRangesParams_PetServices_Service_Evaluation_ struct {
	// evaluation service
	Evaluation *GetAvailableTimeRangesParams_PetServices_Service_Evaluation `protobuf:"bytes,5,opt,name=evaluation,proto3,oneof"`
}

func (*GetAvailableTimeRangesParams_PetServices_Service_Evaluation_) isGetAvailableTimeRangesParams_PetServices_Service_Service() {
}

// Evaluation service
type GetAvailableTimeRangesParams_PetServices_Service_Evaluation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// evaluation id
	EvaluationId int64 `protobuf:"varint,1,opt,name=evaluation_id,json=evaluationId,proto3" json:"evaluation_id,omitempty"`
	// selected date
	Date *date.Date `protobuf:"bytes,2,opt,name=date,proto3" json:"date,omitempty"`
	// 如果没传说明还没有选择 time
	Time *int32 `protobuf:"varint,3,opt,name=time,proto3,oneof" json:"time,omitempty"`
}

func (x *GetAvailableTimeRangesParams_PetServices_Service_Evaluation) Reset() {
	*x = GetAvailableTimeRangesParams_PetServices_Service_Evaluation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableTimeRangesParams_PetServices_Service_Evaluation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableTimeRangesParams_PetServices_Service_Evaluation) ProtoMessage() {}

func (x *GetAvailableTimeRangesParams_PetServices_Service_Evaluation) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableTimeRangesParams_PetServices_Service_Evaluation.ProtoReflect.Descriptor instead.
func (*GetAvailableTimeRangesParams_PetServices_Service_Evaluation) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{7, 0, 0, 0}
}

func (x *GetAvailableTimeRangesParams_PetServices_Service_Evaluation) GetEvaluationId() int64 {
	if x != nil {
		return x.EvaluationId
	}
	return 0
}

func (x *GetAvailableTimeRangesParams_PetServices_Service_Evaluation) GetDate() *date.Date {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *GetAvailableTimeRangesParams_PetServices_Service_Evaluation) GetTime() int32 {
	if x != nil && x.Time != nil {
		return *x.Time
	}
	return 0
}

// available time ranges for a certain day
type GetAvailableTimeRangesResult_DayAvailableTimeRanges struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// date
	Date *date.Date `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	// arrival time range list
	ArrivalTimeRange []*v12.DayTimeRangeDef `protobuf:"bytes,2,rep,name=arrival_time_range,json=arrivalTimeRange,proto3" json:"arrival_time_range,omitempty"`
	// pick up time range list
	PickUpTimeRange []*v12.DayTimeRangeDef `protobuf:"bytes,3,rep,name=pick_up_time_range,json=pickUpTimeRange,proto3" json:"pick_up_time_range,omitempty"`
}

func (x *GetAvailableTimeRangesResult_DayAvailableTimeRanges) Reset() {
	*x = GetAvailableTimeRangesResult_DayAvailableTimeRanges{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableTimeRangesResult_DayAvailableTimeRanges) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableTimeRangesResult_DayAvailableTimeRanges) ProtoMessage() {}

func (x *GetAvailableTimeRangesResult_DayAvailableTimeRanges) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableTimeRangesResult_DayAvailableTimeRanges.ProtoReflect.Descriptor instead.
func (*GetAvailableTimeRangesResult_DayAvailableTimeRanges) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{8, 0}
}

func (x *GetAvailableTimeRangesResult_DayAvailableTimeRanges) GetDate() *date.Date {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *GetAvailableTimeRangesResult_DayAvailableTimeRanges) GetArrivalTimeRange() []*v12.DayTimeRangeDef {
	if x != nil {
		return x.ArrivalTimeRange
	}
	return nil
}

func (x *GetAvailableTimeRangesResult_DayAvailableTimeRanges) GetPickUpTimeRange() []*v12.DayTimeRangeDef {
	if x != nil {
		return x.PickUpTimeRange
	}
	return nil
}

// service capacity
type GetPetAvailableServicesResult_ServiceCapacity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service id
	ServiceId int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// if lodgings for service is available
	IsLodgingAvailable bool `protobuf:"varint,2,opt,name=is_lodging_available,json=isLodgingAvailable,proto3" json:"is_lodging_available,omitempty"`
}

func (x *GetPetAvailableServicesResult_ServiceCapacity) Reset() {
	*x = GetPetAvailableServicesResult_ServiceCapacity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPetAvailableServicesResult_ServiceCapacity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetAvailableServicesResult_ServiceCapacity) ProtoMessage() {}

func (x *GetPetAvailableServicesResult_ServiceCapacity) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetAvailableServicesResult_ServiceCapacity.ProtoReflect.Descriptor instead.
func (*GetPetAvailableServicesResult_ServiceCapacity) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{12, 1}
}

func (x *GetPetAvailableServicesResult_ServiceCapacity) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *GetPetAvailableServicesResult_ServiceCapacity) GetIsLodgingAvailable() bool {
	if x != nil {
		return x.IsLodgingAvailable
	}
	return false
}

// accept customer type
type GetAcceptedCustomerTypesResult_AcceptCustomerType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service item type
	ServiceItemType v1.ServiceItemType `protobuf:"varint,1,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// accepted customer type
	AcceptCustomerType v12.AcceptCustomerType `protobuf:"varint,2,opt,name=accept_customer_type,json=acceptCustomerType,proto3,enum=moego.models.online_booking.v1.AcceptCustomerType" json:"accept_customer_type,omitempty"`
}

func (x *GetAcceptedCustomerTypesResult_AcceptCustomerType) Reset() {
	*x = GetAcceptedCustomerTypesResult_AcceptCustomerType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAcceptedCustomerTypesResult_AcceptCustomerType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAcceptedCustomerTypesResult_AcceptCustomerType) ProtoMessage() {}

func (x *GetAcceptedCustomerTypesResult_AcceptCustomerType) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAcceptedCustomerTypesResult_AcceptCustomerType.ProtoReflect.Descriptor instead.
func (*GetAcceptedCustomerTypesResult_AcceptCustomerType) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{18, 0}
}

func (x *GetAcceptedCustomerTypesResult_AcceptCustomerType) GetServiceItemType() v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

func (x *GetAcceptedCustomerTypesResult_AcceptCustomerType) GetAcceptCustomerType() v12.AcceptCustomerType {
	if x != nil {
		return x.AcceptCustomerType
	}
	return v12.AcceptCustomerType(0)
}

// OB Service Config
type GetAvailableGroupClassesResult_OBServiceConfigView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// group class id
	GroupClassId int64 `protobuf:"varint,1,opt,name=group_class_id,json=groupClassId,proto3" json:"group_class_id,omitempty"`
	// show price type
	ShowPriceType v15.ShowBasePrice `protobuf:"varint,2,opt,name=show_price_type,json=showPriceType,proto3,enum=moego.models.grooming.v1.ShowBasePrice" json:"show_price_type,omitempty"`
}

func (x *GetAvailableGroupClassesResult_OBServiceConfigView) Reset() {
	*x = GetAvailableGroupClassesResult_OBServiceConfigView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableGroupClassesResult_OBServiceConfigView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableGroupClassesResult_OBServiceConfigView) ProtoMessage() {}

func (x *GetAvailableGroupClassesResult_OBServiceConfigView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableGroupClassesResult_OBServiceConfigView.ProtoReflect.Descriptor instead.
func (*GetAvailableGroupClassesResult_OBServiceConfigView) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{22, 0}
}

func (x *GetAvailableGroupClassesResult_OBServiceConfigView) GetGroupClassId() int64 {
	if x != nil {
		return x.GroupClassId
	}
	return 0
}

func (x *GetAvailableGroupClassesResult_OBServiceConfigView) GetShowPriceType() v15.ShowBasePrice {
	if x != nil {
		return x.ShowPriceType
	}
	return v15.ShowBasePrice(0)
}

// group instance with sessions view
type GetPetAvailableGroupClassInstancesResult_GroupClassInstanceWithSessionsView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// group instance
	GroupClassInstance *v1.GroupClassInstance `protobuf:"bytes,1,opt,name=group_class_instance,json=groupClassInstance,proto3" json:"group_class_instance,omitempty"`
	// sessions
	Sessions []*v1.GroupClassSession `protobuf:"bytes,2,rep,name=sessions,proto3" json:"sessions,omitempty"`
	// trainer
	Trainer *GetPetAvailableGroupClassInstancesResult_GroupClassInstanceWithSessionsView_TrainerView `protobuf:"bytes,3,opt,name=trainer,proto3" json:"trainer,omitempty"`
}

func (x *GetPetAvailableGroupClassInstancesResult_GroupClassInstanceWithSessionsView) Reset() {
	*x = GetPetAvailableGroupClassInstancesResult_GroupClassInstanceWithSessionsView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPetAvailableGroupClassInstancesResult_GroupClassInstanceWithSessionsView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetAvailableGroupClassInstancesResult_GroupClassInstanceWithSessionsView) ProtoMessage() {}

func (x *GetPetAvailableGroupClassInstancesResult_GroupClassInstanceWithSessionsView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetAvailableGroupClassInstancesResult_GroupClassInstanceWithSessionsView.ProtoReflect.Descriptor instead.
func (*GetPetAvailableGroupClassInstancesResult_GroupClassInstanceWithSessionsView) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{24, 0}
}

func (x *GetPetAvailableGroupClassInstancesResult_GroupClassInstanceWithSessionsView) GetGroupClassInstance() *v1.GroupClassInstance {
	if x != nil {
		return x.GroupClassInstance
	}
	return nil
}

func (x *GetPetAvailableGroupClassInstancesResult_GroupClassInstanceWithSessionsView) GetSessions() []*v1.GroupClassSession {
	if x != nil {
		return x.Sessions
	}
	return nil
}

func (x *GetPetAvailableGroupClassInstancesResult_GroupClassInstanceWithSessionsView) GetTrainer() *GetPetAvailableGroupClassInstancesResult_GroupClassInstanceWithSessionsView_TrainerView {
	if x != nil {
		return x.Trainer
	}
	return nil
}

// Trainer
type GetPetAvailableGroupClassInstancesResult_GroupClassInstanceWithSessionsView_TrainerView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the staff id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// first name
	FirstName string `protobuf:"bytes,2,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// last name
	LastName string `protobuf:"bytes,3,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// avatar
	AvatarPath string `protobuf:"bytes,4,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
}

func (x *GetPetAvailableGroupClassInstancesResult_GroupClassInstanceWithSessionsView_TrainerView) Reset() {
	*x = GetPetAvailableGroupClassInstancesResult_GroupClassInstanceWithSessionsView_TrainerView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPetAvailableGroupClassInstancesResult_GroupClassInstanceWithSessionsView_TrainerView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetAvailableGroupClassInstancesResult_GroupClassInstanceWithSessionsView_TrainerView) ProtoMessage() {
}

func (x *GetPetAvailableGroupClassInstancesResult_GroupClassInstanceWithSessionsView_TrainerView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetAvailableGroupClassInstancesResult_GroupClassInstanceWithSessionsView_TrainerView.ProtoReflect.Descriptor instead.
func (*GetPetAvailableGroupClassInstancesResult_GroupClassInstanceWithSessionsView_TrainerView) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP(), []int{24, 0, 0}
}

func (x *GetPetAvailableGroupClassInstancesResult_GroupClassInstanceWithSessionsView_TrainerView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetPetAvailableGroupClassInstancesResult_GroupClassInstanceWithSessionsView_TrainerView) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *GetPetAvailableGroupClassInstancesResult_GroupClassInstanceWithSessionsView_TrainerView) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *GetPetAvailableGroupClassInstancesResult_GroupClassInstanceWithSessionsView_TrainerView) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

var File_moego_client_online_booking_v1_booking_availability_api_proto protoreflect.FileDescriptor

var file_moego_client_online_booking_v1_booking_availability_api_proto_rawDesc = []byte{
	0x0a, 0x3d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x1e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a,
	0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x38, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x44, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x4a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f,
	0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x65,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x41, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x62, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x64,
	0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x42, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x62, 0x5f, 0x61, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3a, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3b, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x65, 0x65, 0x6b, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x73, 0x75, 0x70,
	0x70, 0x6f, 0x72, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f,
	0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x66, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x42, 0x10, 0x0a, 0x09, 0x61,
	0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x7d, 0x0a,
	0x22, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x57, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0xd8, 0x01, 0x0a,
	0x16, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x65, 0x74,
	0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a,
	0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52,
	0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x66, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa,
	0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x01, 0x52, 0x0f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x42,
	0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42,
	0x01, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74,
	0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0x63, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x49, 0x0a, 0x04, 0x70, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x65, 0x74,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x04, 0x70, 0x65, 0x74, 0x73, 0x22, 0xff, 0x03, 0x0a,
	0x15, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x65, 0x74,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x59, 0x0a, 0x03, 0x70, 0x65, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x65, 0x74, 0x4f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x65, 0x77, 0x52, 0x03, 0x70, 0x65,
	0x74, 0x12, 0x75, 0x0a, 0x10, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x6e,
	0x73, 0x77, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x6e, 0x73, 0x77, 0x65,
	0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x73, 0x12, 0x69, 0x0a, 0x0f, 0x76, 0x61, 0x63, 0x63,
	0x69, 0x6e, 0x65, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65,
	0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x52, 0x0e, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x73, 0x12, 0x65, 0x0a, 0x13, 0x75, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x65, 0x74, 0x55, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65,
	0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x12, 0x75, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x6c, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x1a, 0x42, 0x0a, 0x14, 0x51, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xc4,
	0x02, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44,
	0x61, 0x74, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x61, 0x0a, 0x11, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x35, 0x0a,
	0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x44, 0x61, 0x74, 0x65, 0x48, 0x01, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x48, 0x02, 0x52, 0x07, 0x65, 0x6e, 0x64,
	0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79,
	0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x22, 0x94, 0x03, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x4d, 0x0a, 0x14, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61,
	0x74, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x12, 0x61, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x49, 0x0a, 0x12, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x65, 0x6e,
	0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x10, 0x61, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x6c, 0x65, 0x45, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x42, 0x0a, 0x11, 0x75,
	0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x10, 0x75,
	0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x65, 0x73, 0x12,
	0x4d, 0x0a, 0x19, 0x75, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x61,
	0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x17, 0x75, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x65, 0x73, 0x12, 0x4c,
	0x0a, 0x19, 0x75, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x70, 0x69,
	0x63, 0x6b, 0x5f, 0x75, 0x70, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x44, 0x61, 0x74, 0x65, 0x52, 0x16, 0x75, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x44, 0x61, 0x74, 0x65, 0x73, 0x22, 0xeb, 0x06, 0x0a,
	0x1c, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x61, 0x0a,
	0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52,
	0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x30, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x2c, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x6b, 0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x48, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x2e, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x52, 0x0b, 0x70, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x1a, 0xd8, 0x03,
	0x0a, 0x0b, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x35, 0x0a,
	0x03, 0x70, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x52,
	0x03, 0x70, 0x65, 0x74, 0x12, 0x6c, 0x0a, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x50, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x2e, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x1a, 0xa3, 0x02, 0x0a, 0x07, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x7d,
	0x0a, 0x0a, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x5b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x2e, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48,
	0x00, 0x52, 0x0a, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x8d, 0x01,
	0x0a, 0x0a, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x0d,
	0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x65, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x17, 0x0a, 0x04, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x04, 0x74, 0x69, 0x6d,
	0x65, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x09, 0x0a,
	0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e,
	0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x9a, 0x03, 0x0a, 0x1c, 0x47,
	0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7b, 0x0a, 0x0f, 0x64,
	0x61, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x53, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x2e, 0x44, 0x61, 0x79, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x0d, 0x64, 0x61, 0x79, 0x54, 0x69,
	0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x1a, 0xfc, 0x01, 0x0a, 0x16, 0x44, 0x61, 0x79,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x73, 0x12, 0x25, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x44, 0x61, 0x74, 0x65, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x5d, 0x0a, 0x12, 0x61, 0x72,
	0x72, 0x69, 0x76, 0x61, 0x6c, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x52, 0x10, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c,
	0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x5c, 0x0a, 0x12, 0x70, 0x69, 0x63,
	0x6b, 0x5f, 0x75, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x52, 0x0f, 0x70, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69,
	0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x22, 0x60, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x48, 0x6f, 0x75,
	0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a,
	0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52,
	0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79,
	0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0xa6, 0x04, 0x0a, 0x1c, 0x47, 0x65,
	0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67,
	0x48, 0x6f, 0x75, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x46, 0x0a, 0x06, 0x6d, 0x6f,
	0x6e, 0x64, 0x61, 0x79, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08, 0x74, 0x68, 0x75, 0x72,
	0x73, 0x64, 0x61, 0x79, 0x12, 0x46, 0x0a, 0x06, 0x66, 0x72, 0x69, 0x64, 0x61, 0x79, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x06, 0x66, 0x72, 0x69, 0x64, 0x61, 0x79, 0x12, 0x4a, 0x0a, 0x08,
	0x73, 0x61, 0x74, 0x75, 0x72, 0x64, 0x61, 0x79, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08,
	0x73, 0x61, 0x74, 0x75, 0x72, 0x64, 0x61, 0x79, 0x12, 0x46, 0x0a, 0x06, 0x73, 0x75, 0x6e, 0x64,
	0x61, 0x79, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x06, 0x73, 0x75, 0x6e, 0x64, 0x61, 0x79,
	0x22, 0xf5, 0x05, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x12, 0x61, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74,
	0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04,
	0x10, 0x01, 0x20, 0x00, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x54, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0b,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4b, 0x0a, 0x04, 0x70,
	0x65, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x50, 0x65, 0x74, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02,
	0x08, 0x01, 0x52, 0x04, 0x70, 0x65, 0x74, 0x73, 0x12, 0x3a, 0x0a, 0x14, 0x73, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x00,
	0x52, 0x12, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x73, 0x12, 0x3f, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x48, 0x01, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61,
	0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x3b, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x48, 0x02, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x42, 0x0a, 0x0e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x92, 0x01, 0x02, 0x10, 0x3c, 0x52, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x63, 0x44, 0x61, 0x74, 0x65, 0x73, 0x12, 0x4c, 0x0a, 0x21, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x77,
	0x69, 0x74, 0x68, 0x5f, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x08, 0x48, 0x03, 0x52, 0x1c, 0x62, 0x6f, 0x6f, 0x6b, 0x57, 0x69, 0x74, 0x68, 0x4f, 0x74, 0x68,
	0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75,
	0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x42, 0x24, 0x0a, 0x22, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x77, 0x69, 0x74, 0x68,
	0x5f, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0x9b, 0x07, 0x0a, 0x1d, 0x47, 0x65, 0x74,
	0x50, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x57, 0x0a, 0x0a, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x69, 0x7a, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x69, 0x65, 0x73, 0x12, 0x32, 0x0a, 0x15, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x03, 0x52, 0x13, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x57, 0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x44, 0x65, 0x66, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x12, 0x5a, 0x0a, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x7d, 0x0a, 0x10,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x52, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x70,
	0x61, 0x63, 0x69, 0x74, 0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x12, 0x65, 0x0a, 0x14, 0x62,
	0x75, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x73, 0x61, 0x6c, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64,
	0x6c, 0x65, 0x53, 0x61, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77, 0x42, 0x02, 0x18, 0x01, 0x52, 0x12,
	0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x53, 0x61, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x12, 0x5a, 0x0a, 0x13, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x72,
	0x69, 0x65, 0x66, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x42, 0x72, 0x69, 0x65, 0x66, 0x56, 0x69, 0x65, 0x77, 0x52, 0x11, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x42, 0x72, 0x69, 0x65, 0x66, 0x56, 0x69, 0x65, 0x77, 0x73, 0x1a, 0x91,
	0x01, 0x0a, 0x14, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69,
	0x74, 0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x63, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43,
	0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x1a, 0x62, 0x0a, 0x0f, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x70,
	0x61, 0x63, 0x69, 0x74, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x69, 0x73, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x12, 0x69, 0x73, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x22, 0xad, 0x02, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x64, 0x0a, 0x11, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x48, 0x01, 0x52, 0x0f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x4b, 0x0a, 0x04, 0x70, 0x65, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x09, 0x77, 0x65, 0x64, 0x6e, 0x65, 0x73, 0x64, 0x61, 0x79, 0x12, 0x4a, 0x0a, 0x08, 0x74, 0x68,
	0x75, 0x72, 0x73, 0x64, 0x61, 0x79, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08, 0x74, 0x68,
	0x75, 0x72, 0x73, 0x64, 0x61, 0x79, 0x12, 0x46, 0x0a, 0x06, 0x66, 0x72, 0x69, 0x64, 0x61, 0x79,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x06, 0x66, 0x72, 0x69, 0x64, 0x61, 0x79, 0x12, 0x4a,
	0x0a, 0x08, 0x73, 0x61, 0x74, 0x75, 0x72, 0x64, 0x61, 0x79, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x08, 0x73, 0x61, 0x74, 0x75, 0x72, 0x64, 0x61, 0x79, 0x12, 0x46, 0x0a, 0x06, 0x73, 0x75,
	0x6e, 0x64, 0x61, 0x79, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x06, 0x73, 0x75, 0x6e, 0x64,
	0x61, 0x79, 0x22, 0xf5, 0x05, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x12, 0x61, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82,
	0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x54, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00,
	0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4b, 0x0a,
	0x04, 0x70, 0x65, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x50, 0x65, 0x74, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92,
	0x01, 0x02, 0x08, 0x01, 0x52, 0x04, 0x70, 0x65, 0x74, 0x73, 0x12, 0x3a, 0x0a, 0x14, 0x73, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02,
	0x08, 0x00, 0x52, 0x12, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x3f, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x48, 0x01, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x3b, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x48, 0x02, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x42, 0x0a, 0x0e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x10, 0x3c, 0x52, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69,
	0x66, 0x69, 0x63, 0x44, 0x61, 0x74, 0x65, 0x73, 0x12, 0x4c, 0x0a, 0x21, 0x62, 0x6f, 0x6f, 0x6b,
	0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x08, 0x48, 0x03, 0x52, 0x1c, 0x62, 0x6f, 0x6f, 0x6b, 0x57, 0x69, 0x74, 0x68, 0x4f,
	0x74, 0x68, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d,
	0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x42, 0x24, 0x0a, 0x22, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x77, 0x69,
	0x74, 0x68, 0x5f, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0xbb, 0x06, 0x0a, 0x1d, 0x47,
	0x65, 0x74, 0x50, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x57, 0x0a, 0x0a,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x32, 0x0a, 0x15, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x03, 0x52, 0x13, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x57, 0x0a, 0x0c, 0x70, 0x65, 0x74,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x44, 0x65, 0x66, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x12, 0x5a, 0x0a, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x7d,
	0x0a, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69,
	0x74, 0x79, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x52, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43,
	0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x12, 0x61, 0x0a,
	0x14, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x73, 0x61, 0x6c, 0x65, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x75,
	0x6e, 0x64, 0x6c, 0x65, 0x53, 0x61, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x12, 0x62, 0x75,
	0x6e, 0x64, 0x6c, 0x65, 0x53, 0x61, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x1a, 0x91, 0x01, 0x0a, 0x14, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x70, 0x61,
	0x63, 0x69, 0x74, 0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x63, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50,
	0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x1a, 0x62, 0x0a, 0x0f, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43,
	0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x69, 0x73, 0x5f, 0x6c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x69, 0x73, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x22, 0xad, 0x02, 0x0a, 0x20, 0x47, 0x65, 0x74,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x64, 0x0a,
	0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x48, 0x01, 0x52,
	0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x4b, 0x0a, 0x04, 0x70, 0x65, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x50, 0x65, 0x74, 0x44, 0x65, 0x66,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x10, 0x64, 0x52, 0x04, 0x70, 0x65, 0x74, 0x73,
	0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8,
	0x42, 0x01, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0xad, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4f, 0x0a,
	0x0b, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x72, 0x69, 0x65, 0x66, 0x56, 0x69, 0x65,
	0x77, 0x52, 0x0b, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x38,
	0x0a, 0x18, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x65, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x16, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x22, 0xc0, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74,
	0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x61, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f,
	0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x5b, 0x0a, 0x19, 0x47,
	0x65, 0x74, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3e, 0x0a, 0x09, 0x70, 0x65, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08,
	0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0x62, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x41,
	0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e,
	0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0xfc, 0x02, 0x0a,
	0x1e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x85, 0x01, 0x0a, 0x15, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x51, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e,
	0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x13, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x73, 0x1a, 0xd1, 0x01, 0x0a, 0x12, 0x41, 0x63, 0x63, 0x65,
	0x70, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x55,
	0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x64, 0x0a, 0x14, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x12, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x22, 0xb9, 0x01, 0x0a, 0x12,
	0x47, 0x65, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x12, 0x61, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74,
	0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04,
	0x10, 0x01, 0x20, 0x00, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f,
	0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x86, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x70,
	0x0a, 0x1a, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x5f, 0x70, 0x69, 0x63, 0x6b, 0x5f, 0x75,
	0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55,
	0x70, 0x54, 0x69, 0x6d, 0x65, 0x44, 0x65, 0x66, 0x52, 0x16, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61,
	0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x22, 0xbc, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x10, 0x0a,
	0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22,
	0xc6, 0x03, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x4e, 0x0a, 0x0d, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63, 0x6c, 0x61, 0x73,
	0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73,
	0x65, 0x73, 0x12, 0x80, 0x01, 0x0a, 0x12, 0x6f, 0x62, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x52, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e,
	0x4f, 0x42, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x56,
	0x69, 0x65, 0x77, 0x52, 0x10, 0x6f, 0x62, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x8c, 0x01, 0x0a, 0x13, 0x4f, 0x42,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x56, 0x69, 0x65,
	0x77, 0x12, 0x24, 0x0a, 0x0e, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x64, 0x12, 0x4f, 0x0a, 0x0f, 0x73, 0x68, 0x6f, 0x77, 0x5f,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x68, 0x6f, 0x77,
	0x42, 0x61, 0x73, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x0d, 0x73, 0x68, 0x6f, 0x77, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0xd5, 0x01, 0x0a, 0x28, 0x47, 0x65, 0x74,
	0x50, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x64,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x64,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x24, 0x0a, 0x0e, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63,
	0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x10,
	0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01,
	0x22, 0xf0, 0x05, 0x0a, 0x28, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x6c, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x9f, 0x01,
	0x0a, 0x15, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x6b, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x50, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61,
	0x73, 0x73, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x57, 0x69, 0x74, 0x68, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x56, 0x69, 0x65, 0x77, 0x52, 0x13, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x12,
	0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x1a, 0xdd, 0x03, 0x0a, 0x22, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61,
	0x73, 0x73, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x57, 0x69, 0x74, 0x68, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x56, 0x69, 0x65, 0x77, 0x12, 0x5e, 0x0a, 0x14, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x12, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61,
	0x73, 0x73, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x47, 0x0a, 0x08, 0x73, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61,
	0x73, 0x73, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x73, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x91, 0x01, 0x0a, 0x07, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x77, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x57, 0x69, 0x74, 0x68, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x56, 0x69,
	0x65, 0x77, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x52, 0x07,
	0x74, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x1a, 0x7a, 0x0a, 0x0b, 0x54, 0x72, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x50,
	0x61, 0x74, 0x68, 0x22, 0xe3, 0x02, 0x0a, 0x1c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x70, 0x65,
	0x63, 0x69, 0x61, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x12, 0x77, 0x0a, 0x0f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4f, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x50, 0x65, 0x74, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d,
	0x70, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x1a, 0x87, 0x01,
	0x0a, 0x12, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x5b, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x44, 0x73, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79,
	0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x7c, 0x0a, 0x1c, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5c, 0x0a, 0x07, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x70,
	0x65, 0x63, 0x69, 0x61, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x07,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x32, 0xcf, 0x0f, 0x0a, 0x1a, 0x42, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xa6, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x42, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x82, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65,
	0x50, 0x65, 0x74, 0x73, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x50, 0x65, 0x74, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x36, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x73, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x85, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x65, 0x73, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x65, 0x73, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x44, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x94, 0x01, 0x0a,
	0x16, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x94, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x48, 0x6f, 0x75, 0x72, 0x12, 0x3c,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x57, 0x6f, 0x72, 0x6b, 0x69,
	0x6e, 0x67, 0x48, 0x6f, 0x75, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3c, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67,
	0x48, 0x6f, 0x75, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x97, 0x01, 0x0a, 0x17, 0x47,
	0x65, 0x74, 0x50, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0xa0, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x6c, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x8b, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x41,
	0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12,
	0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x50, 0x65, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41,
	0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x9a, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63,
	0x65, 0x70, 0x74, 0x65, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x12, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x76, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x9a, 0x01, 0x0a, 0x18, 0x47,
	0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x12, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x65,
	0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0xb8, 0x01, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x50,
	0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x48,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x48, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x94, 0x01, 0x0a, 0x16, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x70, 0x65, 0x63,
	0x69, 0x61, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3c, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3c, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x92, 0x01, 0x0a, 0x26, 0x63, 0x6f,
	0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x66, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescOnce sync.Once
	file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescData = file_moego_client_online_booking_v1_booking_availability_api_proto_rawDesc
)

func file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescGZIP() []byte {
	file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescOnce.Do(func() {
		file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescData)
	})
	return file_moego_client_online_booking_v1_booking_availability_api_proto_rawDescData
}

var file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes = make([]protoimpl.MessageInfo, 39)
var file_moego_client_online_booking_v1_booking_availability_api_proto_goTypes = []interface{}{
	(*GetAvailableServiceItemTypesParams)(nil),                  // 0: moego.client.online_booking.v1.GetAvailableServiceItemTypesParams
	(*GetAvailableServiceItemTypesResult)(nil),                  // 1: moego.client.online_booking.v1.GetAvailableServiceItemTypesResult
	(*GetAvailablePetsParams)(nil),                              // 2: moego.client.online_booking.v1.GetAvailablePetsParams
	(*GetAvailablePetsResult)(nil),                              // 3: moego.client.online_booking.v1.GetAvailablePetsResult
	(*GetAvailablePetResult)(nil),                               // 4: moego.client.online_booking.v1.GetAvailablePetResult
	(*GetAvailableDatesParams)(nil),                             // 5: moego.client.online_booking.v1.GetAvailableDatesParams
	(*GetAvailableDatesResult)(nil),                             // 6: moego.client.online_booking.v1.GetAvailableDatesResult
	(*GetAvailableTimeRangesParams)(nil),                        // 7: moego.client.online_booking.v1.GetAvailableTimeRangesParams
	(*GetAvailableTimeRangesResult)(nil),                        // 8: moego.client.online_booking.v1.GetAvailableTimeRangesResult
	(*GetBusinessWorkingHourParams)(nil),                        // 9: moego.client.online_booking.v1.GetBusinessWorkingHourParams
	(*GetBusinessWorkingHourResult)(nil),                        // 10: moego.client.online_booking.v1.GetBusinessWorkingHourResult
	(*GetPetAvailableServicesParams)(nil),                       // 11: moego.client.online_booking.v1.GetPetAvailableServicesParams
	(*GetPetAvailableServicesResult)(nil),                       // 12: moego.client.online_booking.v1.GetPetAvailableServicesResult
	(*GetAvailableEvaluationListParams)(nil),                    // 13: moego.client.online_booking.v1.GetAvailableEvaluationListParams
	(*GetAvailableEvaluationListResult)(nil),                    // 14: moego.client.online_booking.v1.GetAvailableEvaluationListResult
	(*GetAcceptedPetTypesParams)(nil),                           // 15: moego.client.online_booking.v1.GetAcceptedPetTypesParams
	(*GetAcceptedPetTypesResult)(nil),                           // 16: moego.client.online_booking.v1.GetAcceptedPetTypesResult
	(*GetAcceptedCustomerTypesParams)(nil),                      // 17: moego.client.online_booking.v1.GetAcceptedCustomerTypesParams
	(*GetAcceptedCustomerTypesResult)(nil),                      // 18: moego.client.online_booking.v1.GetAcceptedCustomerTypesResult
	(*GetTimeRangeParams)(nil),                                  // 19: moego.client.online_booking.v1.GetTimeRangeParams
	(*GetTimeRangeResult)(nil),                                  // 20: moego.client.online_booking.v1.GetTimeRangeResult
	(*GetAvailableGroupClassesParams)(nil),                      // 21: moego.client.online_booking.v1.GetAvailableGroupClassesParams
	(*GetAvailableGroupClassesResult)(nil),                      // 22: moego.client.online_booking.v1.GetAvailableGroupClassesResult
	(*GetPetAvailableGroupClassInstancesParams)(nil),            // 23: moego.client.online_booking.v1.GetPetAvailableGroupClassInstancesParams
	(*GetPetAvailableGroupClassInstancesResult)(nil),            // 24: moego.client.online_booking.v1.GetPetAvailableGroupClassInstancesResult
	(*CheckSpecialEvaluationParams)(nil),                        // 25: moego.client.online_booking.v1.CheckSpecialEvaluationParams
	(*CheckSpecialEvaluationResult)(nil),                        // 26: moego.client.online_booking.v1.CheckSpecialEvaluationResult
	nil,                                                         // 27: moego.client.online_booking.v1.GetAvailablePetResult.QuestionAnswersEntry
	(*GetAvailableTimeRangesResult_DayAvailableTimeRanges)(nil), // 28: moego.client.online_booking.v1.GetAvailableTimeRangesResult.DayAvailableTimeRanges
	nil, // 29: moego.client.online_booking.v1.GetPetAvailableServicesResult.ServiceCapacityEntry
	(*GetPetAvailableServicesResult_ServiceCapacity)(nil),                                           // 30: moego.client.online_booking.v1.GetPetAvailableServicesResult.ServiceCapacity
	(*GetAcceptedCustomerTypesResult_AcceptCustomerType)(nil),                                       // 31: moego.client.online_booking.v1.GetAcceptedCustomerTypesResult.AcceptCustomerType
	(*GetAvailableGroupClassesResult_OBServiceConfigView)(nil),                                      // 32: moego.client.online_booking.v1.GetAvailableGroupClassesResult.OBServiceConfigView
	(*GetPetAvailableGroupClassInstancesResult_GroupClassInstanceWithSessionsView)(nil),             // 33: moego.client.online_booking.v1.GetPetAvailableGroupClassInstancesResult.GroupClassInstanceWithSessionsView
	(*GetPetAvailableGroupClassInstancesResult_GroupClassInstanceWithSessionsView_TrainerView)(nil), // 34: moego.client.online_booking.v1.GetPetAvailableGroupClassInstancesResult.GroupClassInstanceWithSessionsView.TrainerView
	nil,                     // 35: moego.client.online_booking.v1.CheckSpecialEvaluationParams.PetServiceIdsEntry
	(v1.ServiceItemType)(0), // 36: moego.models.offering.v1.ServiceItemType
	(*v11.BusinessCustomerPetOnlineBookingView)(nil),     // 37: moego.models.business_customer.v1.BusinessCustomerPetOnlineBookingView
	(*v11.BusinessPetVaccineRecordModel)(nil),            // 38: moego.models.business_customer.v1.BusinessPetVaccineRecordModel
	(v12.PetUnavailableReason)(0),                        // 39: moego.models.online_booking.v1.PetUnavailableReason
	(*date.Date)(nil),                                    // 40: google.type.Date
	(*v12.TimeRangeModel)(nil),                           // 41: moego.models.online_booking.v1.TimeRangeModel
	(v1.ServiceType)(0),                                  // 42: moego.models.offering.v1.ServiceType
	(*v12.BookingPetDef)(nil),                            // 43: moego.models.online_booking.v1.BookingPetDef
	(*v1.CustomizedServiceCategoryView)(nil),             // 44: moego.models.offering.v1.CustomizedServiceCategoryView
	(*v12.BookingPetServiceDef)(nil),                     // 45: moego.models.online_booking.v1.BookingPetServiceDef
	(*v12.ServiceConfigView)(nil),                        // 46: moego.models.online_booking.v1.ServiceConfigView
	(*v1.ServiceBundleSaleView)(nil),                     // 47: moego.models.offering.v1.ServiceBundleSaleView
	(*v1.ServiceBriefView)(nil),                          // 48: moego.models.offering.v1.ServiceBriefView
	(*v1.EvaluationBriefView)(nil),                       // 49: moego.models.offering.v1.EvaluationBriefView
	(v13.PetType)(0),                                     // 50: moego.models.customer.v1.PetType
	(*v12.ArrivalPickUpTimeDef)(nil),                     // 51: moego.models.online_booking.v1.ArrivalPickUpTimeDef
	(*v2.PaginationRequest)(nil),                         // 52: moego.utils.v2.PaginationRequest
	(*v1.GroupClassModel)(nil),                           // 53: moego.models.offering.v1.GroupClassModel
	(*v2.PaginationResponse)(nil),                        // 54: moego.utils.v2.PaginationResponse
	(*v14.CheckSpecialEvaluationResponse_Result)(nil),    // 55: moego.service.enterprise.v1.CheckSpecialEvaluationResponse.Result
	(*v12.DayTimeRangeDef)(nil),                          // 56: moego.models.online_booking.v1.DayTimeRangeDef
	(v12.AcceptCustomerType)(0),                          // 57: moego.models.online_booking.v1.AcceptCustomerType
	(v15.ShowBasePrice)(0),                               // 58: moego.models.grooming.v1.ShowBasePrice
	(*v1.GroupClassInstance)(nil),                        // 59: moego.models.offering.v1.GroupClassInstance
	(*v1.GroupClassSession)(nil),                         // 60: moego.models.offering.v1.GroupClassSession
	(*v14.CheckSpecialEvaluationRequest_ServiceIDs)(nil), // 61: moego.service.enterprise.v1.CheckSpecialEvaluationRequest.ServiceIDs
}
var file_moego_client_online_booking_v1_booking_availability_api_proto_depIdxs = []int32{
	39, // 0: moego.client.online_booking.v1.GetAvailableServiceItemTypesResult.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	39, // 1: moego.client.online_booking.v1.GetAvailablePetsParams.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	4,  // 2: moego.client.online_booking.v1.GetAvailablePetsResult.pets:type_name -> moego.client.online_booking.v1.GetAvailablePetResult
	40, // 3: moego.client.online_booking.v1.GetAvailablePetResult.pet:type_name -> moego.models.business_customer.v1.BusinessCustomerPetOnlineBookingView
	27, // 4: moego.client.online_booking.v1.GetAvailablePetResult.question_answers:type_name -> moego.client.online_booking.v1.GetAvailablePetResult.QuestionAnswersEntry
	38, // 5: moego.client.online_booking.v1.GetAvailablePetResult.vaccine_records:type_name -> moego.models.business_customer.v1.BusinessPetVaccineRecordModel
	39, // 6: moego.client.online_booking.v1.GetAvailablePetResult.unavailable_reasons:type_name -> moego.models.online_booking.v1.PetUnavailableReason
	36, // 7: moego.client.online_booking.v1.GetAvailableDatesParams.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	40, // 8: moego.client.online_booking.v1.GetAvailableDatesParams.start_date:type_name -> google.type.Date
	40, // 9: moego.client.online_booking.v1.GetAvailableDatesParams.end_date:type_name -> google.type.Date
	40, // 10: moego.client.online_booking.v1.GetAvailableDatesResult.available_start_date:type_name -> google.type.Date
	40, // 11: moego.client.online_booking.v1.GetAvailableDatesResult.available_end_date:type_name -> google.type.Date
	40, // 12: moego.client.online_booking.v1.GetAvailableDatesResult.unavailable_dates:type_name -> google.type.Date
	40, // 13: moego.client.online_booking.v1.GetAvailableDatesResult.unavailable_arrival_dates:type_name -> google.type.Date
	40, // 14: moego.client.online_booking.v1.GetAvailableDatesResult.unavailable_pick_up_dates:type_name -> google.type.Date
	36, // 15: moego.client.online_booking.v1.GetAvailableTimeRangesParams.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	40, // 16: moego.client.online_booking.v1.GetAvailableTimeRangesParams.start_date:type_name -> google.type.Date
	40, // 17: moego.client.online_booking.v1.GetAvailableTimeRangesParams.end_date:type_name -> google.type.Date
	28, // 18: moego.client.online_booking.v1.GetAvailableTimeRangesResult.day_time_ranges:type_name -> moego.client.online_booking.v1.GetAvailableTimeRangesResult.DayAvailableTimeRanges
	41, // 19: moego.client.online_booking.v1.GetBusinessWorkingHourResult.monday:type_name -> moego.models.online_booking.v1.TimeRangeModel
	41, // 20: moego.client.online_booking.v1.GetBusinessWorkingHourResult.tuesday:type_name -> moego.models.online_booking.v1.TimeRangeModel
	41, // 21: moego.client.online_booking.v1.GetBusinessWorkingHourResult.wednesday:type_name -> moego.models.online_booking.v1.TimeRangeModel
	41, // 22: moego.client.online_booking.v1.GetBusinessWorkingHourResult.thursday:type_name -> moego.models.online_booking.v1.TimeRangeModel
	41, // 23: moego.client.online_booking.v1.GetBusinessWorkingHourResult.friday:type_name -> moego.models.online_booking.v1.TimeRangeModel
	41, // 24: moego.client.online_booking.v1.GetBusinessWorkingHourResult.saturday:type_name -> moego.models.online_booking.v1.TimeRangeModel
	41, // 25: moego.client.online_booking.v1.GetBusinessWorkingHourResult.sunday:type_name -> moego.models.online_booking.v1.TimeRangeModel
	36, // 26: moego.client.online_booking.v1.GetPetAvailableServicesParams.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	42, // 27: moego.client.online_booking.v1.GetPetAvailableServicesParams.service_type:type_name -> moego.models.offering.v1.ServiceType
	43, // 28: moego.client.online_booking.v1.GetPetAvailableServicesParams.pets:type_name -> moego.models.online_booking.v1.BookingPetDef
	40, // 29: moego.client.online_booking.v1.GetPetAvailableServicesParams.start_date:type_name -> google.type.Date
	40, // 30: moego.client.online_booking.v1.GetPetAvailableServicesParams.end_date:type_name -> google.type.Date
	40, // 31: moego.client.online_booking.v1.GetPetAvailableServicesParams.specific_dates:type_name -> google.type.Date
	44, // 32: moego.client.online_booking.v1.GetPetAvailableServicesResult.categories:type_name -> moego.models.offering.v1.CustomizedServiceCategoryView
	45, // 33: moego.client.online_booking.v1.GetPetAvailableServicesResult.pet_services:type_name -> moego.models.online_booking.v1.BookingPetServiceDef
	46, // 34: moego.client.online_booking.v1.GetPetAvailableServicesResult.service_configs:type_name -> moego.models.online_booking.v1.ServiceConfigView
	29, // 35: moego.client.online_booking.v1.GetPetAvailableServicesResult.service_capacity:type_name -> moego.client.online_booking.v1.GetPetAvailableServicesResult.ServiceCapacityEntry
	47, // 36: moego.client.online_booking.v1.GetPetAvailableServicesResult.bundle_sale_services:type_name -> moego.models.offering.v1.ServiceBundleSaleView
	48, // 37: moego.client.online_booking.v1.GetPetAvailableServicesResult.service_brief_views:type_name -> moego.models.offering.v1.ServiceBriefView
	36, // 38: moego.client.online_booking.v1.GetAvailableEvaluationListParams.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	43, // 39: moego.client.online_booking.v1.GetAvailableEvaluationListParams.pets:type_name -> moego.models.online_booking.v1.BookingPetDef
	49, // 40: moego.client.online_booking.v1.GetAvailableEvaluationListResult.evaluations:type_name -> moego.models.offering.v1.EvaluationBriefView
	36, // 41: moego.client.online_booking.v1.GetAcceptedPetTypesParams.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	50, // 42: moego.client.online_booking.v1.GetAcceptedPetTypesResult.pet_types:type_name -> moego.models.customer.v1.PetType
	31, // 43: moego.client.online_booking.v1.GetAcceptedCustomerTypesResult.accept_customer_types:type_name -> moego.client.online_booking.v1.GetAcceptedCustomerTypesResult.AcceptCustomerType
	36, // 44: moego.client.online_booking.v1.GetTimeRangeParams.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	51, // 45: moego.client.online_booking.v1.GetTimeRangeResult.arrival_pick_up_time_range:type_name -> moego.models.online_booking.v1.ArrivalPickUpTimeDef
	52, // 46: moego.client.online_booking.v1.GetAvailableGroupClassesParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	53, // 47: moego.client.online_booking.v1.GetAvailableGroupClassesResult.group_classes:type_name -> moego.models.offering.v1.GroupClassModel
	32, // 48: moego.client.online_booking.v1.GetAvailableGroupClassesResult.ob_service_configs:type_name -> moego.client.online_booking.v1.GetAvailableGroupClassesResult.OBServiceConfigView
	54, // 49: moego.client.online_booking.v1.GetAvailableGroupClassesResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	52, // 50: moego.client.online_booking.v1.GetPetAvailableGroupClassInstancesParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	33, // 51: moego.client.online_booking.v1.GetPetAvailableGroupClassInstancesResult.group_class_instances:type_name -> moego.client.online_booking.v1.GetPetAvailableGroupClassInstancesResult.GroupClassInstanceWithSessionsView
	54, // 52: moego.client.online_booking.v1.GetPetAvailableGroupClassInstancesResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	35, // 53: moego.client.online_booking.v1.CheckSpecialEvaluationParams.pet_service_ids:type_name -> moego.client.online_booking.v1.CheckSpecialEvaluationParams.PetServiceIdsEntry
	55, // 54: moego.client.online_booking.v1.CheckSpecialEvaluationResult.results:type_name -> moego.service.enterprise.v1.CheckSpecialEvaluationResponse.Result
	40, // 55: moego.client.online_booking.v1.GetAvailableTimeRangesResult.DayAvailableTimeRanges.date:type_name -> google.type.Date
	56, // 56: moego.client.online_booking.v1.GetAvailableTimeRangesResult.DayAvailableTimeRanges.arrival_time_range:type_name -> moego.models.online_booking.v1.DayTimeRangeDef
	56, // 57: moego.client.online_booking.v1.GetAvailableTimeRangesResult.DayAvailableTimeRanges.pick_up_time_range:type_name -> moego.models.online_booking.v1.DayTimeRangeDef
	30, // 58: moego.client.online_booking.v1.GetPetAvailableServicesResult.ServiceCapacityEntry.value:type_name -> moego.client.online_booking.v1.GetPetAvailableServicesResult.ServiceCapacity
	36, // 59: moego.client.online_booking.v1.GetAcceptedCustomerTypesResult.AcceptCustomerType.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	57, // 60: moego.client.online_booking.v1.GetAcceptedCustomerTypesResult.AcceptCustomerType.accept_customer_type:type_name -> moego.models.online_booking.v1.AcceptCustomerType
	58, // 61: moego.client.online_booking.v1.GetAvailableGroupClassesResult.OBServiceConfigView.show_price_type:type_name -> moego.models.grooming.v1.ShowBasePrice
	59, // 62: moego.client.online_booking.v1.GetPetAvailableGroupClassInstancesResult.GroupClassInstanceWithSessionsView.group_class_instance:type_name -> moego.models.offering.v1.GroupClassInstance
	60, // 63: moego.client.online_booking.v1.GetPetAvailableGroupClassInstancesResult.GroupClassInstanceWithSessionsView.sessions:type_name -> moego.models.offering.v1.GroupClassSession
	34, // 64: moego.client.online_booking.v1.GetPetAvailableGroupClassInstancesResult.GroupClassInstanceWithSessionsView.trainer:type_name -> moego.client.online_booking.v1.GetPetAvailableGroupClassInstancesResult.GroupClassInstanceWithSessionsView.TrainerView
	61, // 65: moego.client.online_booking.v1.CheckSpecialEvaluationParams.PetServiceIdsEntry.value:type_name -> moego.service.enterprise.v1.CheckSpecialEvaluationRequest.ServiceIDs
	0,  // 66: moego.client.online_booking.v1.BookingAvailabilityService.GetAvailableServiceItemTypes:input_type -> moego.client.online_booking.v1.GetAvailableServiceItemTypesParams
	2,  // 67: moego.client.online_booking.v1.BookingAvailabilityService.GetAvailablePets:input_type -> moego.client.online_booking.v1.GetAvailablePetsParams
	5,  // 68: moego.client.online_booking.v1.BookingAvailabilityService.GetAvailableDates:input_type -> moego.client.online_booking.v1.GetAvailableDatesParams
	7,  // 69: moego.client.online_booking.v1.BookingAvailabilityService.GetAvailableTimeRanges:input_type -> moego.client.online_booking.v1.GetAvailableTimeRangesParams
	9,  // 70: moego.client.online_booking.v1.BookingAvailabilityService.GetBusinessWorkingHour:input_type -> moego.client.online_booking.v1.GetBusinessWorkingHourParams
	11, // 71: moego.client.online_booking.v1.BookingAvailabilityService.GetPetAvailableServices:input_type -> moego.client.online_booking.v1.GetPetAvailableServicesParams
	13, // 72: moego.client.online_booking.v1.BookingAvailabilityService.GetAvailableEvaluationList:input_type -> moego.client.online_booking.v1.GetAvailableEvaluationListParams
	15, // 73: moego.client.online_booking.v1.BookingAvailabilityService.GetAcceptedPetTypes:input_type -> moego.client.online_booking.v1.GetAcceptedPetTypesParams
	17, // 74: moego.client.online_booking.v1.BookingAvailabilityService.GetAcceptedCustomerTypes:input_type -> moego.client.online_booking.v1.GetAcceptedCustomerTypesParams
	19, // 75: moego.client.online_booking.v1.BookingAvailabilityService.GetTimeRange:input_type -> moego.client.online_booking.v1.GetTimeRangeParams
	21, // 76: moego.client.online_booking.v1.BookingAvailabilityService.GetAvailableGroupClasses:input_type -> moego.client.online_booking.v1.GetAvailableGroupClassesParams
	23, // 77: moego.client.online_booking.v1.BookingAvailabilityService.GetPetAvailableGroupClassInstances:input_type -> moego.client.online_booking.v1.GetPetAvailableGroupClassInstancesParams
	25, // 78: moego.client.online_booking.v1.BookingAvailabilityService.CheckSpecialEvaluation:input_type -> moego.client.online_booking.v1.CheckSpecialEvaluationParams
	1,  // 79: moego.client.online_booking.v1.BookingAvailabilityService.GetAvailableServiceItemTypes:output_type -> moego.client.online_booking.v1.GetAvailableServiceItemTypesResult
	3,  // 80: moego.client.online_booking.v1.BookingAvailabilityService.GetAvailablePets:output_type -> moego.client.online_booking.v1.GetAvailablePetsResult
	6,  // 81: moego.client.online_booking.v1.BookingAvailabilityService.GetAvailableDates:output_type -> moego.client.online_booking.v1.GetAvailableDatesResult
	8,  // 82: moego.client.online_booking.v1.BookingAvailabilityService.GetAvailableTimeRanges:output_type -> moego.client.online_booking.v1.GetAvailableTimeRangesResult
	10, // 83: moego.client.online_booking.v1.BookingAvailabilityService.GetBusinessWorkingHour:output_type -> moego.client.online_booking.v1.GetBusinessWorkingHourResult
	12, // 84: moego.client.online_booking.v1.BookingAvailabilityService.GetPetAvailableServices:output_type -> moego.client.online_booking.v1.GetPetAvailableServicesResult
	14, // 85: moego.client.online_booking.v1.BookingAvailabilityService.GetAvailableEvaluationList:output_type -> moego.client.online_booking.v1.GetAvailableEvaluationListResult
	16, // 86: moego.client.online_booking.v1.BookingAvailabilityService.GetAcceptedPetTypes:output_type -> moego.client.online_booking.v1.GetAcceptedPetTypesResult
	18, // 87: moego.client.online_booking.v1.BookingAvailabilityService.GetAcceptedCustomerTypes:output_type -> moego.client.online_booking.v1.GetAcceptedCustomerTypesResult
	20, // 88: moego.client.online_booking.v1.BookingAvailabilityService.GetTimeRange:output_type -> moego.client.online_booking.v1.GetTimeRangeResult
	22, // 89: moego.client.online_booking.v1.BookingAvailabilityService.GetAvailableGroupClasses:output_type -> moego.client.online_booking.v1.GetAvailableGroupClassesResult
	24, // 90: moego.client.online_booking.v1.BookingAvailabilityService.GetPetAvailableGroupClassInstances:output_type -> moego.client.online_booking.v1.GetPetAvailableGroupClassInstancesResult
	26, // 91: moego.client.online_booking.v1.BookingAvailabilityService.CheckSpecialEvaluation:output_type -> moego.client.online_booking.v1.CheckSpecialEvaluationResult
	79, // [79:92] is the sub-list for method output_type
	66, // [66:79] is the sub-list for method input_type
	66, // [66:66] is the sub-list for extension type_name
	66, // [66:66] is the sub-list for extension extendee
	0,  // [0:66] is the sub-list for field type_name
}

func init() { file_moego_client_online_booking_v1_booking_availability_api_proto_init() }
func file_moego_client_online_booking_v1_booking_availability_api_proto_init() {
	if File_moego_client_online_booking_v1_booking_availability_api_proto != nil {
		return
	}
	file_moego_client_online_booking_v1_booking_request_api_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableServiceItemTypesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableServiceItemTypesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailablePetsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailablePetsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailablePetResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableDatesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableDatesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableTimeRangesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableTimeRangesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBusinessWorkingHourParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBusinessWorkingHourResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPetAvailableServicesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPetAvailableServicesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableEvaluationListParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableEvaluationListResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAcceptedPetTypesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAcceptedPetTypesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAcceptedCustomerTypesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAcceptedCustomerTypesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTimeRangeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTimeRangeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableGroupClassesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableGroupClassesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPetAvailableGroupClassInstancesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPetAvailableGroupClassInstancesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckSpecialEvaluationParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckSpecialEvaluationResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableTimeRangesParams_PetServices); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableTimeRangesParams_PetServices_Service); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableTimeRangesParams_PetServices_Service_Evaluation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableTimeRangesResult_DayAvailableTimeRanges); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPetAvailableServicesResult_ServiceCapacity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAcceptedCustomerTypesResult_AcceptCustomerType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableGroupClassesResult_OBServiceConfigView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPetAvailableGroupClassInstancesResult_GroupClassInstanceWithSessionsView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPetAvailableGroupClassInstancesResult_GroupClassInstanceWithSessionsView_TrainerView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*GetAvailableServiceItemTypesParams_Name)(nil),
		(*GetAvailableServiceItemTypesParams_Domain)(nil),
	}
	file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*GetAvailablePetsParams_Name)(nil),
		(*GetAvailablePetsParams_Domain)(nil),
	}
	file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[5].OneofWrappers = []interface{}{
		(*GetAvailableDatesParams_Name)(nil),
		(*GetAvailableDatesParams_Domain)(nil),
	}
	file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[7].OneofWrappers = []interface{}{
		(*GetAvailableTimeRangesParams_Name)(nil),
		(*GetAvailableTimeRangesParams_Domain)(nil),
	}
	file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[9].OneofWrappers = []interface{}{
		(*GetBusinessWorkingHourParams_Name)(nil),
		(*GetBusinessWorkingHourParams_Domain)(nil),
	}
	file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[11].OneofWrappers = []interface{}{
		(*GetPetAvailableServicesParams_Name)(nil),
		(*GetPetAvailableServicesParams_Domain)(nil),
	}
	file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[13].OneofWrappers = []interface{}{
		(*GetAvailableEvaluationListParams_Name)(nil),
		(*GetAvailableEvaluationListParams_Domain)(nil),
	}
	file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[15].OneofWrappers = []interface{}{
		(*GetAcceptedPetTypesParams_Name)(nil),
		(*GetAcceptedPetTypesParams_Domain)(nil),
	}
	file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[17].OneofWrappers = []interface{}{
		(*GetAcceptedCustomerTypesParams_Name)(nil),
		(*GetAcceptedCustomerTypesParams_Domain)(nil),
	}
	file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[19].OneofWrappers = []interface{}{
		(*GetTimeRangeParams_Name)(nil),
		(*GetTimeRangeParams_Domain)(nil),
	}
	file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[21].OneofWrappers = []interface{}{
		(*GetAvailableGroupClassesParams_Name)(nil),
		(*GetAvailableGroupClassesParams_Domain)(nil),
	}
	file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[23].OneofWrappers = []interface{}{
		(*GetPetAvailableGroupClassInstancesParams_Name)(nil),
		(*GetPetAvailableGroupClassInstancesParams_Domain)(nil),
	}
	file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[25].OneofWrappers = []interface{}{
		(*CheckSpecialEvaluationParams_Name)(nil),
		(*CheckSpecialEvaluationParams_Domain)(nil),
	}
	file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[29].OneofWrappers = []interface{}{
		(*GetAvailableTimeRangesParams_PetServices_Service_Evaluation_)(nil),
	}
	file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes[30].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_client_online_booking_v1_booking_availability_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   39,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_client_online_booking_v1_booking_availability_api_proto_goTypes,
		DependencyIndexes: file_moego_client_online_booking_v1_booking_availability_api_proto_depIdxs,
		MessageInfos:      file_moego_client_online_booking_v1_booking_availability_api_proto_msgTypes,
	}.Build()
	File_moego_client_online_booking_v1_booking_availability_api_proto = out.File
	file_moego_client_online_booking_v1_booking_availability_api_proto_rawDesc = nil
	file_moego_client_online_booking_v1_booking_availability_api_proto_goTypes = nil
	file_moego_client_online_booking_v1_booking_availability_api_proto_depIdxs = nil
}

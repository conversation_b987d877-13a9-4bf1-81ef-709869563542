// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/appointment/v1/appointment_service.proto

package appointmentsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AppointmentServiceClient is the client API for AppointmentService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AppointmentServiceClient interface {
	// Create a appointment
	// Applicable to quick add and advanced add appointment scenarios
	CreateAppointment(ctx context.Context, in *CreateAppointmentRequest, opts ...grpc.CallOption) (*CreateAppointmentResponse, error)
	// Incremental update appointment
	// Applicable to add/modify pet&service, switch on multi-pets start at the same time.
	UpdateAppointment(ctx context.Context, in *UpdateAppointmentRequest, opts ...grpc.CallOption) (*UpdateAppointmentResponse, error)
	// 增量更新 appointment
	// NOTE：这个方法名应该叫做 UpdateAppointment，但是被占用了，
	// 而且 UpdateAppointment 的 request 使用了 api 层的定义，没法扩展，所以直接新开一个接口。
	UpdateAppointmentSelective(ctx context.Context, in *UpdateAppointmentSelectiveRequest, opts ...grpc.CallOption) (*UpdateAppointmentSelectiveResponse, error)
	// Get appointment
	GetAppointment(ctx context.Context, in *GetAppointmentRequest, opts ...grpc.CallOption) (*GetAppointmentResponse, error)
	// Get appointment list
	GetAppointmentList(ctx context.Context, in *GetAppointmentListRequest, opts ...grpc.CallOption) (*GetAppointmentListResponse, error)
	// get customer last appointment
	GetCustomerLastAppointment(ctx context.Context, in *GetCustomerLastAppointmentRequest, opts ...grpc.CallOption) (*GetCustomerLastAppointmentResponse, error)
	// Calculate appointment invoice
	// Estimated total, including service, add-on, tax, discount etc.
	CalculateAppointmentInvoice(ctx context.Context, in *CalculateAppointmentInvoiceRequest, opts ...grpc.CallOption) (*CalculateAppointmentInvoiceResponse, error)
	// get in progress appointment
	GetInProgressAppointment(ctx context.Context, in *GetInProgressAppointmentRequest, opts ...grpc.CallOption) (*GetInProgressAppointmentResponse, error)
	// Create block
	CreateBlock(ctx context.Context, in *CreateBlockRequest, opts ...grpc.CallOption) (*CreateBlockResponse, error)
	// List appointments
	ListAppointments(ctx context.Context, in *ListAppointmentsRequest, opts ...grpc.CallOption) (*ListAppointmentsResponse, error)
	// list block times
	ListBlockTimes(ctx context.Context, in *ListBlockTimesRequest, opts ...grpc.CallOption) (*ListBlockTimesResponse, error)
	// Create a appointment for online booking
	// Applicable to online booking scenarios for boarding, daycare & evaluation services
	CreateAppointmentForOnlineBooking(ctx context.Context, in *CreateAppointmentForOnlineBookingRequest, opts ...grpc.CallOption) (*CreateAppointmentForOnlineBookingResponse, error)
	// batch quick check-in will create appointments for the selected pets and service, and set the appointment status to "checked_in"
	// if the appointment already exists, it will update the appointment status to "checked_in"
	// if multiple pets belong to the same customer, will be in the same appointment
	BatchQuickCheckIn(ctx context.Context, in *BatchQuickCheckInRequest, opts ...grpc.CallOption) (*BatchQuickCheckInResponse, error)
	// list appointments for pet
	ListAppointmentForPets(ctx context.Context, in *ListAppointmentForPetsRequest, opts ...grpc.CallOption) (*ListAppointmentForPetsResponse, error)
	// list appointments for customer
	ListAppointmentsForCustomers(ctx context.Context, in *ListAppointmentsForCustomersRequest, opts ...grpc.CallOption) (*ListAppointmentsForCustomersResponse, error)
	// Cancel appointment
	CancelAppointment(ctx context.Context, in *CancelAppointmentRequest, opts ...grpc.CallOption) (*CancelAppointmentResponse, error)
	// Batch book again appointment by staff and date
	BatchBookAgainAppointment(ctx context.Context, in *BatchBookAgainAppointmentRequest, opts ...grpc.CallOption) (*BatchBookAgainAppointmentResponse, error)
	// Batch cancel appointment by staff and date
	BatchCancelAppointment(ctx context.Context, in *BatchCancelAppointmentRequest, opts ...grpc.CallOption) (*BatchCancelAppointmentResponse, error)
	// Count appointment for pets
	CountAppointmentForPets(ctx context.Context, in *CountAppointmentForPetsRequest, opts ...grpc.CallOption) (*CountAppointmentForPetsResponse, error)
	// Delete appointments
	DeleteAppointments(ctx context.Context, in *DeleteAppointmentsRequest, opts ...grpc.CallOption) (*DeleteAppointmentsResponse, error)
	// RestoreAppointments 用于恢复之前被删除的 appointment 记录
	RestoreAppointments(ctx context.Context, in *RestoreAppointmentsRequest, opts ...grpc.CallOption) (*RestoreAppointmentsResponse, error)
	// Reschedule boarding appointment
	// 处理与 boarding 相关的附加数据，如 daycare、grooming、addon 等服务。
	// 如果传入的 end_date 早于当前 end_date，则删除 boarding 关联的所有 daycare、grooming、addon 数据。
	// 如果传入的 end_date 晚于当前 end_date，则在 boarding 上添加相应的新增 daycare、grooming、addon 数据。
	RescheduleBoardingAppointment(ctx context.Context, in *RescheduleBoardingAppointmentRequest, opts ...grpc.CallOption) (*RescheduleBoardingAppointmentResponse, error)
	// Sync appointment to order, temporary for data fix
	SyncAppointmentToOrder(ctx context.Context, in *SyncAppointmentToOrderRequest, opts ...grpc.CallOption) (*SyncAppointmentToOrderResponse, error)
	// Preview order detail by appointment id, temporary for data fix
	PreviewOrderDetail(ctx context.Context, in *PreviewOrderDetailRequest, opts ...grpc.CallOption) (*PreviewOrderDetailResponse, error)
	// Preview order line items by appointment id, for invoice preview
	PreviewOrderLineItems(ctx context.Context, in *PreviewOrderLineItemsRequest, opts ...grpc.CallOption) (*PreviewOrderLineItemsResponse, error)
	// Get appointment time overlap list
	GetTimeOverlapAppointmentList(ctx context.Context, in *GetTimeOverlapAppointmentListRequest, opts ...grpc.CallOption) (*GetTimeOverlapAppointmentListResponse, error)
	// Preview estimated order for appointment
	// estimated total = total service price + service charge(auto apply)
	PreviewEstimateOrder(ctx context.Context, in *PreviewEstimateOrderRequest, opts ...grpc.CallOption) (*PreviewEstimateOrderResponse, error)
	// List extra info for appointment
	ListExtraInfo(ctx context.Context, in *ListExtraInfoRequest, opts ...grpc.CallOption) (*ListExtraInfoResponse, error)
}

type appointmentServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAppointmentServiceClient(cc grpc.ClientConnInterface) AppointmentServiceClient {
	return &appointmentServiceClient{cc}
}

func (c *appointmentServiceClient) CreateAppointment(ctx context.Context, in *CreateAppointmentRequest, opts ...grpc.CallOption) (*CreateAppointmentResponse, error) {
	out := new(CreateAppointmentResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentService/CreateAppointment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) UpdateAppointment(ctx context.Context, in *UpdateAppointmentRequest, opts ...grpc.CallOption) (*UpdateAppointmentResponse, error) {
	out := new(UpdateAppointmentResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentService/UpdateAppointment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) UpdateAppointmentSelective(ctx context.Context, in *UpdateAppointmentSelectiveRequest, opts ...grpc.CallOption) (*UpdateAppointmentSelectiveResponse, error) {
	out := new(UpdateAppointmentSelectiveResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentService/UpdateAppointmentSelective", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) GetAppointment(ctx context.Context, in *GetAppointmentRequest, opts ...grpc.CallOption) (*GetAppointmentResponse, error) {
	out := new(GetAppointmentResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentService/GetAppointment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) GetAppointmentList(ctx context.Context, in *GetAppointmentListRequest, opts ...grpc.CallOption) (*GetAppointmentListResponse, error) {
	out := new(GetAppointmentListResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentService/GetAppointmentList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) GetCustomerLastAppointment(ctx context.Context, in *GetCustomerLastAppointmentRequest, opts ...grpc.CallOption) (*GetCustomerLastAppointmentResponse, error) {
	out := new(GetCustomerLastAppointmentResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentService/GetCustomerLastAppointment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) CalculateAppointmentInvoice(ctx context.Context, in *CalculateAppointmentInvoiceRequest, opts ...grpc.CallOption) (*CalculateAppointmentInvoiceResponse, error) {
	out := new(CalculateAppointmentInvoiceResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentService/CalculateAppointmentInvoice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) GetInProgressAppointment(ctx context.Context, in *GetInProgressAppointmentRequest, opts ...grpc.CallOption) (*GetInProgressAppointmentResponse, error) {
	out := new(GetInProgressAppointmentResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentService/GetInProgressAppointment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) CreateBlock(ctx context.Context, in *CreateBlockRequest, opts ...grpc.CallOption) (*CreateBlockResponse, error) {
	out := new(CreateBlockResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentService/CreateBlock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) ListAppointments(ctx context.Context, in *ListAppointmentsRequest, opts ...grpc.CallOption) (*ListAppointmentsResponse, error) {
	out := new(ListAppointmentsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentService/ListAppointments", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) ListBlockTimes(ctx context.Context, in *ListBlockTimesRequest, opts ...grpc.CallOption) (*ListBlockTimesResponse, error) {
	out := new(ListBlockTimesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentService/ListBlockTimes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) CreateAppointmentForOnlineBooking(ctx context.Context, in *CreateAppointmentForOnlineBookingRequest, opts ...grpc.CallOption) (*CreateAppointmentForOnlineBookingResponse, error) {
	out := new(CreateAppointmentForOnlineBookingResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentService/CreateAppointmentForOnlineBooking", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) BatchQuickCheckIn(ctx context.Context, in *BatchQuickCheckInRequest, opts ...grpc.CallOption) (*BatchQuickCheckInResponse, error) {
	out := new(BatchQuickCheckInResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentService/BatchQuickCheckIn", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) ListAppointmentForPets(ctx context.Context, in *ListAppointmentForPetsRequest, opts ...grpc.CallOption) (*ListAppointmentForPetsResponse, error) {
	out := new(ListAppointmentForPetsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentService/ListAppointmentForPets", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) ListAppointmentsForCustomers(ctx context.Context, in *ListAppointmentsForCustomersRequest, opts ...grpc.CallOption) (*ListAppointmentsForCustomersResponse, error) {
	out := new(ListAppointmentsForCustomersResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentService/ListAppointmentsForCustomers", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) CancelAppointment(ctx context.Context, in *CancelAppointmentRequest, opts ...grpc.CallOption) (*CancelAppointmentResponse, error) {
	out := new(CancelAppointmentResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentService/CancelAppointment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) BatchBookAgainAppointment(ctx context.Context, in *BatchBookAgainAppointmentRequest, opts ...grpc.CallOption) (*BatchBookAgainAppointmentResponse, error) {
	out := new(BatchBookAgainAppointmentResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentService/BatchBookAgainAppointment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) BatchCancelAppointment(ctx context.Context, in *BatchCancelAppointmentRequest, opts ...grpc.CallOption) (*BatchCancelAppointmentResponse, error) {
	out := new(BatchCancelAppointmentResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentService/BatchCancelAppointment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) CountAppointmentForPets(ctx context.Context, in *CountAppointmentForPetsRequest, opts ...grpc.CallOption) (*CountAppointmentForPetsResponse, error) {
	out := new(CountAppointmentForPetsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentService/CountAppointmentForPets", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) DeleteAppointments(ctx context.Context, in *DeleteAppointmentsRequest, opts ...grpc.CallOption) (*DeleteAppointmentsResponse, error) {
	out := new(DeleteAppointmentsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentService/DeleteAppointments", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) RestoreAppointments(ctx context.Context, in *RestoreAppointmentsRequest, opts ...grpc.CallOption) (*RestoreAppointmentsResponse, error) {
	out := new(RestoreAppointmentsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentService/RestoreAppointments", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) RescheduleBoardingAppointment(ctx context.Context, in *RescheduleBoardingAppointmentRequest, opts ...grpc.CallOption) (*RescheduleBoardingAppointmentResponse, error) {
	out := new(RescheduleBoardingAppointmentResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentService/RescheduleBoardingAppointment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) SyncAppointmentToOrder(ctx context.Context, in *SyncAppointmentToOrderRequest, opts ...grpc.CallOption) (*SyncAppointmentToOrderResponse, error) {
	out := new(SyncAppointmentToOrderResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentService/SyncAppointmentToOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) PreviewOrderDetail(ctx context.Context, in *PreviewOrderDetailRequest, opts ...grpc.CallOption) (*PreviewOrderDetailResponse, error) {
	out := new(PreviewOrderDetailResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentService/PreviewOrderDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) PreviewOrderLineItems(ctx context.Context, in *PreviewOrderLineItemsRequest, opts ...grpc.CallOption) (*PreviewOrderLineItemsResponse, error) {
	out := new(PreviewOrderLineItemsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentService/PreviewOrderLineItems", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) GetTimeOverlapAppointmentList(ctx context.Context, in *GetTimeOverlapAppointmentListRequest, opts ...grpc.CallOption) (*GetTimeOverlapAppointmentListResponse, error) {
	out := new(GetTimeOverlapAppointmentListResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentService/GetTimeOverlapAppointmentList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) PreviewEstimateOrder(ctx context.Context, in *PreviewEstimateOrderRequest, opts ...grpc.CallOption) (*PreviewEstimateOrderResponse, error) {
	out := new(PreviewEstimateOrderResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentService/PreviewEstimateOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) ListExtraInfo(ctx context.Context, in *ListExtraInfoRequest, opts ...grpc.CallOption) (*ListExtraInfoResponse, error) {
	out := new(ListExtraInfoResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentService/ListExtraInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AppointmentServiceServer is the server API for AppointmentService service.
// All implementations must embed UnimplementedAppointmentServiceServer
// for forward compatibility
type AppointmentServiceServer interface {
	// Create a appointment
	// Applicable to quick add and advanced add appointment scenarios
	CreateAppointment(context.Context, *CreateAppointmentRequest) (*CreateAppointmentResponse, error)
	// Incremental update appointment
	// Applicable to add/modify pet&service, switch on multi-pets start at the same time.
	UpdateAppointment(context.Context, *UpdateAppointmentRequest) (*UpdateAppointmentResponse, error)
	// 增量更新 appointment
	// NOTE：这个方法名应该叫做 UpdateAppointment，但是被占用了，
	// 而且 UpdateAppointment 的 request 使用了 api 层的定义，没法扩展，所以直接新开一个接口。
	UpdateAppointmentSelective(context.Context, *UpdateAppointmentSelectiveRequest) (*UpdateAppointmentSelectiveResponse, error)
	// Get appointment
	GetAppointment(context.Context, *GetAppointmentRequest) (*GetAppointmentResponse, error)
	// Get appointment list
	GetAppointmentList(context.Context, *GetAppointmentListRequest) (*GetAppointmentListResponse, error)
	// get customer last appointment
	GetCustomerLastAppointment(context.Context, *GetCustomerLastAppointmentRequest) (*GetCustomerLastAppointmentResponse, error)
	// Calculate appointment invoice
	// Estimated total, including service, add-on, tax, discount etc.
	CalculateAppointmentInvoice(context.Context, *CalculateAppointmentInvoiceRequest) (*CalculateAppointmentInvoiceResponse, error)
	// get in progress appointment
	GetInProgressAppointment(context.Context, *GetInProgressAppointmentRequest) (*GetInProgressAppointmentResponse, error)
	// Create block
	CreateBlock(context.Context, *CreateBlockRequest) (*CreateBlockResponse, error)
	// List appointments
	ListAppointments(context.Context, *ListAppointmentsRequest) (*ListAppointmentsResponse, error)
	// list block times
	ListBlockTimes(context.Context, *ListBlockTimesRequest) (*ListBlockTimesResponse, error)
	// Create a appointment for online booking
	// Applicable to online booking scenarios for boarding, daycare & evaluation services
	CreateAppointmentForOnlineBooking(context.Context, *CreateAppointmentForOnlineBookingRequest) (*CreateAppointmentForOnlineBookingResponse, error)
	// batch quick check-in will create appointments for the selected pets and service, and set the appointment status to "checked_in"
	// if the appointment already exists, it will update the appointment status to "checked_in"
	// if multiple pets belong to the same customer, will be in the same appointment
	BatchQuickCheckIn(context.Context, *BatchQuickCheckInRequest) (*BatchQuickCheckInResponse, error)
	// list appointments for pet
	ListAppointmentForPets(context.Context, *ListAppointmentForPetsRequest) (*ListAppointmentForPetsResponse, error)
	// list appointments for customer
	ListAppointmentsForCustomers(context.Context, *ListAppointmentsForCustomersRequest) (*ListAppointmentsForCustomersResponse, error)
	// Cancel appointment
	CancelAppointment(context.Context, *CancelAppointmentRequest) (*CancelAppointmentResponse, error)
	// Batch book again appointment by staff and date
	BatchBookAgainAppointment(context.Context, *BatchBookAgainAppointmentRequest) (*BatchBookAgainAppointmentResponse, error)
	// Batch cancel appointment by staff and date
	BatchCancelAppointment(context.Context, *BatchCancelAppointmentRequest) (*BatchCancelAppointmentResponse, error)
	// Count appointment for pets
	CountAppointmentForPets(context.Context, *CountAppointmentForPetsRequest) (*CountAppointmentForPetsResponse, error)
	// Delete appointments
	DeleteAppointments(context.Context, *DeleteAppointmentsRequest) (*DeleteAppointmentsResponse, error)
	// RestoreAppointments 用于恢复之前被删除的 appointment 记录
	RestoreAppointments(context.Context, *RestoreAppointmentsRequest) (*RestoreAppointmentsResponse, error)
	// Reschedule boarding appointment
	// 处理与 boarding 相关的附加数据，如 daycare、grooming、addon 等服务。
	// 如果传入的 end_date 早于当前 end_date，则删除 boarding 关联的所有 daycare、grooming、addon 数据。
	// 如果传入的 end_date 晚于当前 end_date，则在 boarding 上添加相应的新增 daycare、grooming、addon 数据。
	RescheduleBoardingAppointment(context.Context, *RescheduleBoardingAppointmentRequest) (*RescheduleBoardingAppointmentResponse, error)
	// Sync appointment to order, temporary for data fix
	SyncAppointmentToOrder(context.Context, *SyncAppointmentToOrderRequest) (*SyncAppointmentToOrderResponse, error)
	// Preview order detail by appointment id, temporary for data fix
	PreviewOrderDetail(context.Context, *PreviewOrderDetailRequest) (*PreviewOrderDetailResponse, error)
	// Preview order line items by appointment id, for invoice preview
	PreviewOrderLineItems(context.Context, *PreviewOrderLineItemsRequest) (*PreviewOrderLineItemsResponse, error)
	// Get appointment time overlap list
	GetTimeOverlapAppointmentList(context.Context, *GetTimeOverlapAppointmentListRequest) (*GetTimeOverlapAppointmentListResponse, error)
	// Preview estimated order for appointment
	// estimated total = total service price + service charge(auto apply)
	PreviewEstimateOrder(context.Context, *PreviewEstimateOrderRequest) (*PreviewEstimateOrderResponse, error)
	// List extra info for appointment
	ListExtraInfo(context.Context, *ListExtraInfoRequest) (*ListExtraInfoResponse, error)
	mustEmbedUnimplementedAppointmentServiceServer()
}

// UnimplementedAppointmentServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAppointmentServiceServer struct {
}

func (UnimplementedAppointmentServiceServer) CreateAppointment(context.Context, *CreateAppointmentRequest) (*CreateAppointmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAppointment not implemented")
}
func (UnimplementedAppointmentServiceServer) UpdateAppointment(context.Context, *UpdateAppointmentRequest) (*UpdateAppointmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAppointment not implemented")
}
func (UnimplementedAppointmentServiceServer) UpdateAppointmentSelective(context.Context, *UpdateAppointmentSelectiveRequest) (*UpdateAppointmentSelectiveResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAppointmentSelective not implemented")
}
func (UnimplementedAppointmentServiceServer) GetAppointment(context.Context, *GetAppointmentRequest) (*GetAppointmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAppointment not implemented")
}
func (UnimplementedAppointmentServiceServer) GetAppointmentList(context.Context, *GetAppointmentListRequest) (*GetAppointmentListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAppointmentList not implemented")
}
func (UnimplementedAppointmentServiceServer) GetCustomerLastAppointment(context.Context, *GetCustomerLastAppointmentRequest) (*GetCustomerLastAppointmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomerLastAppointment not implemented")
}
func (UnimplementedAppointmentServiceServer) CalculateAppointmentInvoice(context.Context, *CalculateAppointmentInvoiceRequest) (*CalculateAppointmentInvoiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CalculateAppointmentInvoice not implemented")
}
func (UnimplementedAppointmentServiceServer) GetInProgressAppointment(context.Context, *GetInProgressAppointmentRequest) (*GetInProgressAppointmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInProgressAppointment not implemented")
}
func (UnimplementedAppointmentServiceServer) CreateBlock(context.Context, *CreateBlockRequest) (*CreateBlockResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBlock not implemented")
}
func (UnimplementedAppointmentServiceServer) ListAppointments(context.Context, *ListAppointmentsRequest) (*ListAppointmentsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAppointments not implemented")
}
func (UnimplementedAppointmentServiceServer) ListBlockTimes(context.Context, *ListBlockTimesRequest) (*ListBlockTimesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListBlockTimes not implemented")
}
func (UnimplementedAppointmentServiceServer) CreateAppointmentForOnlineBooking(context.Context, *CreateAppointmentForOnlineBookingRequest) (*CreateAppointmentForOnlineBookingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAppointmentForOnlineBooking not implemented")
}
func (UnimplementedAppointmentServiceServer) BatchQuickCheckIn(context.Context, *BatchQuickCheckInRequest) (*BatchQuickCheckInResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchQuickCheckIn not implemented")
}
func (UnimplementedAppointmentServiceServer) ListAppointmentForPets(context.Context, *ListAppointmentForPetsRequest) (*ListAppointmentForPetsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAppointmentForPets not implemented")
}
func (UnimplementedAppointmentServiceServer) ListAppointmentsForCustomers(context.Context, *ListAppointmentsForCustomersRequest) (*ListAppointmentsForCustomersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAppointmentsForCustomers not implemented")
}
func (UnimplementedAppointmentServiceServer) CancelAppointment(context.Context, *CancelAppointmentRequest) (*CancelAppointmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelAppointment not implemented")
}
func (UnimplementedAppointmentServiceServer) BatchBookAgainAppointment(context.Context, *BatchBookAgainAppointmentRequest) (*BatchBookAgainAppointmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchBookAgainAppointment not implemented")
}
func (UnimplementedAppointmentServiceServer) BatchCancelAppointment(context.Context, *BatchCancelAppointmentRequest) (*BatchCancelAppointmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchCancelAppointment not implemented")
}
func (UnimplementedAppointmentServiceServer) CountAppointmentForPets(context.Context, *CountAppointmentForPetsRequest) (*CountAppointmentForPetsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAppointmentForPets not implemented")
}
func (UnimplementedAppointmentServiceServer) DeleteAppointments(context.Context, *DeleteAppointmentsRequest) (*DeleteAppointmentsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAppointments not implemented")
}
func (UnimplementedAppointmentServiceServer) RestoreAppointments(context.Context, *RestoreAppointmentsRequest) (*RestoreAppointmentsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RestoreAppointments not implemented")
}
func (UnimplementedAppointmentServiceServer) RescheduleBoardingAppointment(context.Context, *RescheduleBoardingAppointmentRequest) (*RescheduleBoardingAppointmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RescheduleBoardingAppointment not implemented")
}
func (UnimplementedAppointmentServiceServer) SyncAppointmentToOrder(context.Context, *SyncAppointmentToOrderRequest) (*SyncAppointmentToOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncAppointmentToOrder not implemented")
}
func (UnimplementedAppointmentServiceServer) PreviewOrderDetail(context.Context, *PreviewOrderDetailRequest) (*PreviewOrderDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PreviewOrderDetail not implemented")
}
func (UnimplementedAppointmentServiceServer) PreviewOrderLineItems(context.Context, *PreviewOrderLineItemsRequest) (*PreviewOrderLineItemsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PreviewOrderLineItems not implemented")
}
func (UnimplementedAppointmentServiceServer) GetTimeOverlapAppointmentList(context.Context, *GetTimeOverlapAppointmentListRequest) (*GetTimeOverlapAppointmentListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTimeOverlapAppointmentList not implemented")
}
func (UnimplementedAppointmentServiceServer) PreviewEstimateOrder(context.Context, *PreviewEstimateOrderRequest) (*PreviewEstimateOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PreviewEstimateOrder not implemented")
}
func (UnimplementedAppointmentServiceServer) ListExtraInfo(context.Context, *ListExtraInfoRequest) (*ListExtraInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListExtraInfo not implemented")
}
func (UnimplementedAppointmentServiceServer) mustEmbedUnimplementedAppointmentServiceServer() {}

// UnsafeAppointmentServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AppointmentServiceServer will
// result in compilation errors.
type UnsafeAppointmentServiceServer interface {
	mustEmbedUnimplementedAppointmentServiceServer()
}

func RegisterAppointmentServiceServer(s grpc.ServiceRegistrar, srv AppointmentServiceServer) {
	s.RegisterService(&AppointmentService_ServiceDesc, srv)
}

func _AppointmentService_CreateAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAppointmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).CreateAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentService/CreateAppointment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).CreateAppointment(ctx, req.(*CreateAppointmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_UpdateAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAppointmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).UpdateAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentService/UpdateAppointment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).UpdateAppointment(ctx, req.(*UpdateAppointmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_UpdateAppointmentSelective_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAppointmentSelectiveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).UpdateAppointmentSelective(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentService/UpdateAppointmentSelective",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).UpdateAppointmentSelective(ctx, req.(*UpdateAppointmentSelectiveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_GetAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAppointmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).GetAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentService/GetAppointment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).GetAppointment(ctx, req.(*GetAppointmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_GetAppointmentList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAppointmentListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).GetAppointmentList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentService/GetAppointmentList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).GetAppointmentList(ctx, req.(*GetAppointmentListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_GetCustomerLastAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCustomerLastAppointmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).GetCustomerLastAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentService/GetCustomerLastAppointment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).GetCustomerLastAppointment(ctx, req.(*GetCustomerLastAppointmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_CalculateAppointmentInvoice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CalculateAppointmentInvoiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).CalculateAppointmentInvoice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentService/CalculateAppointmentInvoice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).CalculateAppointmentInvoice(ctx, req.(*CalculateAppointmentInvoiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_GetInProgressAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInProgressAppointmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).GetInProgressAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentService/GetInProgressAppointment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).GetInProgressAppointment(ctx, req.(*GetInProgressAppointmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_CreateBlock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBlockRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).CreateBlock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentService/CreateBlock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).CreateBlock(ctx, req.(*CreateBlockRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_ListAppointments_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAppointmentsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).ListAppointments(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentService/ListAppointments",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).ListAppointments(ctx, req.(*ListAppointmentsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_ListBlockTimes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBlockTimesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).ListBlockTimes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentService/ListBlockTimes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).ListBlockTimes(ctx, req.(*ListBlockTimesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_CreateAppointmentForOnlineBooking_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAppointmentForOnlineBookingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).CreateAppointmentForOnlineBooking(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentService/CreateAppointmentForOnlineBooking",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).CreateAppointmentForOnlineBooking(ctx, req.(*CreateAppointmentForOnlineBookingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_BatchQuickCheckIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchQuickCheckInRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).BatchQuickCheckIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentService/BatchQuickCheckIn",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).BatchQuickCheckIn(ctx, req.(*BatchQuickCheckInRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_ListAppointmentForPets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAppointmentForPetsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).ListAppointmentForPets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentService/ListAppointmentForPets",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).ListAppointmentForPets(ctx, req.(*ListAppointmentForPetsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_ListAppointmentsForCustomers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAppointmentsForCustomersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).ListAppointmentsForCustomers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentService/ListAppointmentsForCustomers",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).ListAppointmentsForCustomers(ctx, req.(*ListAppointmentsForCustomersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_CancelAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelAppointmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).CancelAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentService/CancelAppointment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).CancelAppointment(ctx, req.(*CancelAppointmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_BatchBookAgainAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchBookAgainAppointmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).BatchBookAgainAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentService/BatchBookAgainAppointment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).BatchBookAgainAppointment(ctx, req.(*BatchBookAgainAppointmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_BatchCancelAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCancelAppointmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).BatchCancelAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentService/BatchCancelAppointment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).BatchCancelAppointment(ctx, req.(*BatchCancelAppointmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_CountAppointmentForPets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAppointmentForPetsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).CountAppointmentForPets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentService/CountAppointmentForPets",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).CountAppointmentForPets(ctx, req.(*CountAppointmentForPetsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_DeleteAppointments_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAppointmentsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).DeleteAppointments(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentService/DeleteAppointments",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).DeleteAppointments(ctx, req.(*DeleteAppointmentsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_RestoreAppointments_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RestoreAppointmentsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).RestoreAppointments(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentService/RestoreAppointments",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).RestoreAppointments(ctx, req.(*RestoreAppointmentsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_RescheduleBoardingAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RescheduleBoardingAppointmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).RescheduleBoardingAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentService/RescheduleBoardingAppointment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).RescheduleBoardingAppointment(ctx, req.(*RescheduleBoardingAppointmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_SyncAppointmentToOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncAppointmentToOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).SyncAppointmentToOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentService/SyncAppointmentToOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).SyncAppointmentToOrder(ctx, req.(*SyncAppointmentToOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_PreviewOrderDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PreviewOrderDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).PreviewOrderDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentService/PreviewOrderDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).PreviewOrderDetail(ctx, req.(*PreviewOrderDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_PreviewOrderLineItems_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PreviewOrderLineItemsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).PreviewOrderLineItems(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentService/PreviewOrderLineItems",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).PreviewOrderLineItems(ctx, req.(*PreviewOrderLineItemsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_GetTimeOverlapAppointmentList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTimeOverlapAppointmentListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).GetTimeOverlapAppointmentList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentService/GetTimeOverlapAppointmentList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).GetTimeOverlapAppointmentList(ctx, req.(*GetTimeOverlapAppointmentListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_PreviewEstimateOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PreviewEstimateOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).PreviewEstimateOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentService/PreviewEstimateOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).PreviewEstimateOrder(ctx, req.(*PreviewEstimateOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_ListExtraInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListExtraInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).ListExtraInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentService/ListExtraInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).ListExtraInfo(ctx, req.(*ListExtraInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AppointmentService_ServiceDesc is the grpc.ServiceDesc for AppointmentService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AppointmentService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.appointment.v1.AppointmentService",
	HandlerType: (*AppointmentServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateAppointment",
			Handler:    _AppointmentService_CreateAppointment_Handler,
		},
		{
			MethodName: "UpdateAppointment",
			Handler:    _AppointmentService_UpdateAppointment_Handler,
		},
		{
			MethodName: "UpdateAppointmentSelective",
			Handler:    _AppointmentService_UpdateAppointmentSelective_Handler,
		},
		{
			MethodName: "GetAppointment",
			Handler:    _AppointmentService_GetAppointment_Handler,
		},
		{
			MethodName: "GetAppointmentList",
			Handler:    _AppointmentService_GetAppointmentList_Handler,
		},
		{
			MethodName: "GetCustomerLastAppointment",
			Handler:    _AppointmentService_GetCustomerLastAppointment_Handler,
		},
		{
			MethodName: "CalculateAppointmentInvoice",
			Handler:    _AppointmentService_CalculateAppointmentInvoice_Handler,
		},
		{
			MethodName: "GetInProgressAppointment",
			Handler:    _AppointmentService_GetInProgressAppointment_Handler,
		},
		{
			MethodName: "CreateBlock",
			Handler:    _AppointmentService_CreateBlock_Handler,
		},
		{
			MethodName: "ListAppointments",
			Handler:    _AppointmentService_ListAppointments_Handler,
		},
		{
			MethodName: "ListBlockTimes",
			Handler:    _AppointmentService_ListBlockTimes_Handler,
		},
		{
			MethodName: "CreateAppointmentForOnlineBooking",
			Handler:    _AppointmentService_CreateAppointmentForOnlineBooking_Handler,
		},
		{
			MethodName: "BatchQuickCheckIn",
			Handler:    _AppointmentService_BatchQuickCheckIn_Handler,
		},
		{
			MethodName: "ListAppointmentForPets",
			Handler:    _AppointmentService_ListAppointmentForPets_Handler,
		},
		{
			MethodName: "ListAppointmentsForCustomers",
			Handler:    _AppointmentService_ListAppointmentsForCustomers_Handler,
		},
		{
			MethodName: "CancelAppointment",
			Handler:    _AppointmentService_CancelAppointment_Handler,
		},
		{
			MethodName: "BatchBookAgainAppointment",
			Handler:    _AppointmentService_BatchBookAgainAppointment_Handler,
		},
		{
			MethodName: "BatchCancelAppointment",
			Handler:    _AppointmentService_BatchCancelAppointment_Handler,
		},
		{
			MethodName: "CountAppointmentForPets",
			Handler:    _AppointmentService_CountAppointmentForPets_Handler,
		},
		{
			MethodName: "DeleteAppointments",
			Handler:    _AppointmentService_DeleteAppointments_Handler,
		},
		{
			MethodName: "RestoreAppointments",
			Handler:    _AppointmentService_RestoreAppointments_Handler,
		},
		{
			MethodName: "RescheduleBoardingAppointment",
			Handler:    _AppointmentService_RescheduleBoardingAppointment_Handler,
		},
		{
			MethodName: "SyncAppointmentToOrder",
			Handler:    _AppointmentService_SyncAppointmentToOrder_Handler,
		},
		{
			MethodName: "PreviewOrderDetail",
			Handler:    _AppointmentService_PreviewOrderDetail_Handler,
		},
		{
			MethodName: "PreviewOrderLineItems",
			Handler:    _AppointmentService_PreviewOrderLineItems_Handler,
		},
		{
			MethodName: "GetTimeOverlapAppointmentList",
			Handler:    _AppointmentService_GetTimeOverlapAppointmentList_Handler,
		},
		{
			MethodName: "PreviewEstimateOrder",
			Handler:    _AppointmentService_PreviewEstimateOrder_Handler,
		},
		{
			MethodName: "ListExtraInfo",
			Handler:    _AppointmentService_ListExtraInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/appointment/v1/appointment_service.proto",
}

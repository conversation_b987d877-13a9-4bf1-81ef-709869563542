// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/customer/v1/customer_pet_metadata_models.proto

package customerpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// pet metadata category
type PetMetadataCategory int32

const (
	// unspecified
	PetMetadataCategory_PET_METADATA_CATEGORY_UNSPECIFIED PetMetadataCategory = 0
	// pet type
	PetMetadataCategory_PET_METADATA_CATEGORY_PET_TYPE PetMetadataCategory = 1
	// hair length
	PetMetadataCategory_PET_METADATA_CATEGORY_HAIR_LENGTH PetMetadataCategory = 2
	// behavior
	PetMetadataCategory_PET_METADATA_CATEGORY_BEHAVIOR PetMetadataCategory = 3
	// fixed
	PetMetadataCategory_PET_METADATA_CATEGORY_FIXED PetMetadataCategory = 4
	// vaccine
	PetMetadataCategory_PET_METADATA_CATEGORY_VACCINE PetMetadataCategory = 5
	// weight unit
	PetMetadataCategory_PET_METADATA_CATEGORY_WEIGHT_UNIT PetMetadataCategory = 6
)

// Enum value maps for PetMetadataCategory.
var (
	PetMetadataCategory_name = map[int32]string{
		0: "PET_METADATA_CATEGORY_UNSPECIFIED",
		1: "PET_METADATA_CATEGORY_PET_TYPE",
		2: "PET_METADATA_CATEGORY_HAIR_LENGTH",
		3: "PET_METADATA_CATEGORY_BEHAVIOR",
		4: "PET_METADATA_CATEGORY_FIXED",
		5: "PET_METADATA_CATEGORY_VACCINE",
		6: "PET_METADATA_CATEGORY_WEIGHT_UNIT",
	}
	PetMetadataCategory_value = map[string]int32{
		"PET_METADATA_CATEGORY_UNSPECIFIED": 0,
		"PET_METADATA_CATEGORY_PET_TYPE":    1,
		"PET_METADATA_CATEGORY_HAIR_LENGTH": 2,
		"PET_METADATA_CATEGORY_BEHAVIOR":    3,
		"PET_METADATA_CATEGORY_FIXED":       4,
		"PET_METADATA_CATEGORY_VACCINE":     5,
		"PET_METADATA_CATEGORY_WEIGHT_UNIT": 6,
	}
)

func (x PetMetadataCategory) Enum() *PetMetadataCategory {
	p := new(PetMetadataCategory)
	*p = x
	return p
}

func (x PetMetadataCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PetMetadataCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_customer_v1_customer_pet_metadata_models_proto_enumTypes[0].Descriptor()
}

func (PetMetadataCategory) Type() protoreflect.EnumType {
	return &file_moego_models_customer_v1_customer_pet_metadata_models_proto_enumTypes[0]
}

func (x PetMetadataCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PetMetadataCategory.Descriptor instead.
func (PetMetadataCategory) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_customer_v1_customer_pet_metadata_models_proto_rawDescGZIP(), []int{0}
}

// pet metadata model
type PetMetadataModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// category
	Category PetMetadataCategory `protobuf:"varint,2,opt,name=category,proto3,enum=moego.models.customer.v1.PetMetadataCategory" json:"category,omitempty"`
	// name
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// sort
	Sort int32 `protobuf:"varint,4,opt,name=sort,proto3" json:"sort,omitempty"`
	// is enable
	IsEnable bool `protobuf:"varint,5,opt,name=is_enable,json=isEnable,proto3" json:"is_enable,omitempty"`
}

func (x *PetMetadataModel) Reset() {
	*x = PetMetadataModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_customer_v1_customer_pet_metadata_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetMetadataModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetMetadataModel) ProtoMessage() {}

func (x *PetMetadataModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_customer_v1_customer_pet_metadata_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetMetadataModel.ProtoReflect.Descriptor instead.
func (*PetMetadataModel) Descriptor() ([]byte, []int) {
	return file_moego_models_customer_v1_customer_pet_metadata_models_proto_rawDescGZIP(), []int{0}
}

func (x *PetMetadataModel) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PetMetadataModel) GetCategory() PetMetadataCategory {
	if x != nil {
		return x.Category
	}
	return PetMetadataCategory_PET_METADATA_CATEGORY_UNSPECIFIED
}

func (x *PetMetadataModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PetMetadataModel) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *PetMetadataModel) GetIsEnable() bool {
	if x != nil {
		return x.IsEnable
	}
	return false
}

var File_moego_models_customer_v1_customer_pet_metadata_models_proto protoreflect.FileDescriptor

var file_moego_models_customer_v1_customer_pet_metadata_models_proto_rawDesc = []byte{
	0x0a, 0x3b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x22, 0xb2, 0x01, 0x0a, 0x10, 0x50, 0x65, 0x74, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x49, 0x0a, 0x08,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x08, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73,
	0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x12,
	0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x2a, 0x96, 0x02, 0x0a,
	0x13, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x12, 0x25, 0x0a, 0x21, 0x50, 0x45, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x41,
	0x44, 0x41, 0x54, 0x41, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x22, 0x0a, 0x1e, 0x50,
	0x45, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x41, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x43, 0x41, 0x54, 0x45,
	0x47, 0x4f, 0x52, 0x59, 0x5f, 0x50, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x01, 0x12,
	0x25, 0x0a, 0x21, 0x50, 0x45, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x41, 0x44, 0x41, 0x54, 0x41, 0x5f,
	0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x48, 0x41, 0x49, 0x52, 0x5f, 0x4c, 0x45,
	0x4e, 0x47, 0x54, 0x48, 0x10, 0x02, 0x12, 0x22, 0x0a, 0x1e, 0x50, 0x45, 0x54, 0x5f, 0x4d, 0x45,
	0x54, 0x41, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f,
	0x42, 0x45, 0x48, 0x41, 0x56, 0x49, 0x4f, 0x52, 0x10, 0x03, 0x12, 0x1f, 0x0a, 0x1b, 0x50, 0x45,
	0x54, 0x5f, 0x4d, 0x45, 0x54, 0x41, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47,
	0x4f, 0x52, 0x59, 0x5f, 0x46, 0x49, 0x58, 0x45, 0x44, 0x10, 0x04, 0x12, 0x21, 0x0a, 0x1d, 0x50,
	0x45, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x41, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x43, 0x41, 0x54, 0x45,
	0x47, 0x4f, 0x52, 0x59, 0x5f, 0x56, 0x41, 0x43, 0x43, 0x49, 0x4e, 0x45, 0x10, 0x05, 0x12, 0x25,
	0x0a, 0x21, 0x50, 0x45, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x41, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x57, 0x45, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x55,
	0x4e, 0x49, 0x54, 0x10, 0x06, 0x42, 0x7e, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x58, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62,
	0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_customer_v1_customer_pet_metadata_models_proto_rawDescOnce sync.Once
	file_moego_models_customer_v1_customer_pet_metadata_models_proto_rawDescData = file_moego_models_customer_v1_customer_pet_metadata_models_proto_rawDesc
)

func file_moego_models_customer_v1_customer_pet_metadata_models_proto_rawDescGZIP() []byte {
	file_moego_models_customer_v1_customer_pet_metadata_models_proto_rawDescOnce.Do(func() {
		file_moego_models_customer_v1_customer_pet_metadata_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_customer_v1_customer_pet_metadata_models_proto_rawDescData)
	})
	return file_moego_models_customer_v1_customer_pet_metadata_models_proto_rawDescData
}

var file_moego_models_customer_v1_customer_pet_metadata_models_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_customer_v1_customer_pet_metadata_models_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_customer_v1_customer_pet_metadata_models_proto_goTypes = []interface{}{
	(PetMetadataCategory)(0), // 0: moego.models.customer.v1.PetMetadataCategory
	(*PetMetadataModel)(nil), // 1: moego.models.customer.v1.PetMetadataModel
}
var file_moego_models_customer_v1_customer_pet_metadata_models_proto_depIdxs = []int32{
	0, // 0: moego.models.customer.v1.PetMetadataModel.category:type_name -> moego.models.customer.v1.PetMetadataCategory
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_models_customer_v1_customer_pet_metadata_models_proto_init() }
func file_moego_models_customer_v1_customer_pet_metadata_models_proto_init() {
	if File_moego_models_customer_v1_customer_pet_metadata_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_customer_v1_customer_pet_metadata_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetMetadataModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_customer_v1_customer_pet_metadata_models_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_customer_v1_customer_pet_metadata_models_proto_goTypes,
		DependencyIndexes: file_moego_models_customer_v1_customer_pet_metadata_models_proto_depIdxs,
		EnumInfos:         file_moego_models_customer_v1_customer_pet_metadata_models_proto_enumTypes,
		MessageInfos:      file_moego_models_customer_v1_customer_pet_metadata_models_proto_msgTypes,
	}.Build()
	File_moego_models_customer_v1_customer_pet_metadata_models_proto = out.File
	file_moego_models_customer_v1_customer_pet_metadata_models_proto_rawDesc = nil
	file_moego_models_customer_v1_customer_pet_metadata_models_proto_goTypes = nil
	file_moego_models_customer_v1_customer_pet_metadata_models_proto_depIdxs = nil
}

// @since 2023-07-03 12:43:02
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/ai_assistant/v1/business_conversation_models.proto

package aiassistantpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// BusinessConversation model
type BusinessConversationModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// conversation id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// creator id (staff id)
	CreatorId int64 `protobuf:"varint,3,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	// scenario
	Scenario string `protobuf:"bytes,4,opt,name=scenario,proto3" json:"scenario,omitempty"`
	// cost
	Cost float64 `protobuf:"fixed64,5,opt,name=cost,proto3" json:"cost,omitempty"`
	// total input token
	TotalInputToken int32 `protobuf:"varint,13,opt,name=total_input_token,json=totalInputToken,proto3" json:"total_input_token,omitempty"`
	// total output token
	TotalOutputToken int32 `protobuf:"varint,14,opt,name=total_output_token,json=totalOutputToken,proto3" json:"total_output_token,omitempty"`
	// total token
	TotalToken int32 `protobuf:"varint,15,opt,name=total_token,json=totalToken,proto3" json:"total_token,omitempty"`
	// question count
	QuestionCount int32 `protobuf:"varint,16,opt,name=question_count,json=questionCount,proto3" json:"question_count,omitempty"`
	// adopted question id
	AdoptedQuestionId int64 `protobuf:"varint,6,opt,name=adopted_question_id,json=adoptedQuestionId,proto3" json:"adopted_question_id,omitempty"`
	// prompt
	Prompt string `protobuf:"bytes,7,opt,name=prompt,proto3" json:"prompt,omitempty"`
	// trailer prompt
	TrailerPrompt string `protobuf:"bytes,8,opt,name=trailer_prompt,json=trailerPrompt,proto3" json:"trailer_prompt,omitempty"`
	// temperature
	Temperature float64 `protobuf:"fixed64,9,opt,name=temperature,proto3" json:"temperature,omitempty"`
	// status
	Status BusinessConversationStatus `protobuf:"varint,10,opt,name=status,proto3,enum=moego.models.ai_assistant.v1.BusinessConversationStatus" json:"status,omitempty"`
	// create time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// update time
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *BusinessConversationModel) Reset() {
	*x = BusinessConversationModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_ai_assistant_v1_business_conversation_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessConversationModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessConversationModel) ProtoMessage() {}

func (x *BusinessConversationModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_ai_assistant_v1_business_conversation_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessConversationModel.ProtoReflect.Descriptor instead.
func (*BusinessConversationModel) Descriptor() ([]byte, []int) {
	return file_moego_models_ai_assistant_v1_business_conversation_models_proto_rawDescGZIP(), []int{0}
}

func (x *BusinessConversationModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BusinessConversationModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BusinessConversationModel) GetCreatorId() int64 {
	if x != nil {
		return x.CreatorId
	}
	return 0
}

func (x *BusinessConversationModel) GetScenario() string {
	if x != nil {
		return x.Scenario
	}
	return ""
}

func (x *BusinessConversationModel) GetCost() float64 {
	if x != nil {
		return x.Cost
	}
	return 0
}

func (x *BusinessConversationModel) GetTotalInputToken() int32 {
	if x != nil {
		return x.TotalInputToken
	}
	return 0
}

func (x *BusinessConversationModel) GetTotalOutputToken() int32 {
	if x != nil {
		return x.TotalOutputToken
	}
	return 0
}

func (x *BusinessConversationModel) GetTotalToken() int32 {
	if x != nil {
		return x.TotalToken
	}
	return 0
}

func (x *BusinessConversationModel) GetQuestionCount() int32 {
	if x != nil {
		return x.QuestionCount
	}
	return 0
}

func (x *BusinessConversationModel) GetAdoptedQuestionId() int64 {
	if x != nil {
		return x.AdoptedQuestionId
	}
	return 0
}

func (x *BusinessConversationModel) GetPrompt() string {
	if x != nil {
		return x.Prompt
	}
	return ""
}

func (x *BusinessConversationModel) GetTrailerPrompt() string {
	if x != nil {
		return x.TrailerPrompt
	}
	return ""
}

func (x *BusinessConversationModel) GetTemperature() float64 {
	if x != nil {
		return x.Temperature
	}
	return 0
}

func (x *BusinessConversationModel) GetStatus() BusinessConversationStatus {
	if x != nil {
		return x.Status
	}
	return BusinessConversationStatus_BUSINESS_CONVERSATION_STATUS_UNSPECIFIED
}

func (x *BusinessConversationModel) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *BusinessConversationModel) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// BusinessConversationQuestion model
type BusinessConversationQuestionModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// question id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// conversation id
	ConversationId int64 `protobuf:"varint,2,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
	// sender id (staff id)
	SenderId int64 `protobuf:"varint,3,opt,name=sender_id,json=senderId,proto3" json:"sender_id,omitempty"`
	// is adopted
	Adopted bool `protobuf:"varint,4,opt,name=adopted,proto3" json:"adopted,omitempty"`
	// cost
	Cost float64 `protobuf:"fixed64,5,opt,name=cost,proto3" json:"cost,omitempty"`
	// input token
	InputToken int32 `protobuf:"varint,13,opt,name=input_token,json=inputToken,proto3" json:"input_token,omitempty"`
	// output token
	OutputToken int32 `protobuf:"varint,14,opt,name=output_token,json=outputToken,proto3" json:"output_token,omitempty"`
	// token
	Token int32 `protobuf:"varint,15,opt,name=token,proto3" json:"token,omitempty"`
	// question
	Question string `protobuf:"bytes,6,opt,name=question,proto3" json:"question,omitempty"`
	// answer
	Answer string `protobuf:"bytes,7,opt,name=answer,proto3" json:"answer,omitempty"`
	// status
	Status BusinessConversationQuestionStatus `protobuf:"varint,8,opt,name=status,proto3,enum=moego.models.ai_assistant.v1.BusinessConversationQuestionStatus" json:"status,omitempty"`
	// send time for question
	SentAt *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=sent_at,json=sentAt,proto3" json:"sent_at,omitempty"`
	// answer time for answer
	AnsweredAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=answered_at,json=answeredAt,proto3" json:"answered_at,omitempty"`
	// create time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// update time
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *BusinessConversationQuestionModel) Reset() {
	*x = BusinessConversationQuestionModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_ai_assistant_v1_business_conversation_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessConversationQuestionModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessConversationQuestionModel) ProtoMessage() {}

func (x *BusinessConversationQuestionModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_ai_assistant_v1_business_conversation_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessConversationQuestionModel.ProtoReflect.Descriptor instead.
func (*BusinessConversationQuestionModel) Descriptor() ([]byte, []int) {
	return file_moego_models_ai_assistant_v1_business_conversation_models_proto_rawDescGZIP(), []int{1}
}

func (x *BusinessConversationQuestionModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BusinessConversationQuestionModel) GetConversationId() int64 {
	if x != nil {
		return x.ConversationId
	}
	return 0
}

func (x *BusinessConversationQuestionModel) GetSenderId() int64 {
	if x != nil {
		return x.SenderId
	}
	return 0
}

func (x *BusinessConversationQuestionModel) GetAdopted() bool {
	if x != nil {
		return x.Adopted
	}
	return false
}

func (x *BusinessConversationQuestionModel) GetCost() float64 {
	if x != nil {
		return x.Cost
	}
	return 0
}

func (x *BusinessConversationQuestionModel) GetInputToken() int32 {
	if x != nil {
		return x.InputToken
	}
	return 0
}

func (x *BusinessConversationQuestionModel) GetOutputToken() int32 {
	if x != nil {
		return x.OutputToken
	}
	return 0
}

func (x *BusinessConversationQuestionModel) GetToken() int32 {
	if x != nil {
		return x.Token
	}
	return 0
}

func (x *BusinessConversationQuestionModel) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *BusinessConversationQuestionModel) GetAnswer() string {
	if x != nil {
		return x.Answer
	}
	return ""
}

func (x *BusinessConversationQuestionModel) GetStatus() BusinessConversationQuestionStatus {
	if x != nil {
		return x.Status
	}
	return BusinessConversationQuestionStatus_BUSINESS_CONVERSATION_QUESTION_STATUS_UNSPECIFIED
}

func (x *BusinessConversationQuestionModel) GetSentAt() *timestamppb.Timestamp {
	if x != nil {
		return x.SentAt
	}
	return nil
}

func (x *BusinessConversationQuestionModel) GetAnsweredAt() *timestamppb.Timestamp {
	if x != nil {
		return x.AnsweredAt
	}
	return nil
}

func (x *BusinessConversationQuestionModel) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *BusinessConversationQuestionModel) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// business conversation summary model
type BusinessConversationSummaryModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// conversation count
	ConversationCount int32 `protobuf:"varint,2,opt,name=conversation_count,json=conversationCount,proto3" json:"conversation_count,omitempty"`
	// nonempty conversation count
	NonemptyConversationCount int32 `protobuf:"varint,10,opt,name=nonempty_conversation_count,json=nonemptyConversationCount,proto3" json:"nonempty_conversation_count,omitempty"`
	// question count
	QuestionCount int32 `protobuf:"varint,3,opt,name=question_count,json=questionCount,proto3" json:"question_count,omitempty"`
	// adopted conversation count
	AdoptedConversationCount int32 `protobuf:"varint,4,opt,name=adopted_conversation_count,json=adoptedConversationCount,proto3" json:"adopted_conversation_count,omitempty"`
	// total cost
	TotalCost float64 `protobuf:"fixed64,5,opt,name=total_cost,json=totalCost,proto3" json:"total_cost,omitempty"`
	// total input token
	TotalInputToken int32 `protobuf:"varint,6,opt,name=total_input_token,json=totalInputToken,proto3" json:"total_input_token,omitempty"`
	// total output token
	TotalOutputToken int32 `protobuf:"varint,7,opt,name=total_output_token,json=totalOutputToken,proto3" json:"total_output_token,omitempty"`
	// total token
	TotalToken int32 `protobuf:"varint,8,opt,name=total_token,json=totalToken,proto3" json:"total_token,omitempty"`
	// last conversation created at
	LastConversationCreatedAt *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=last_conversation_created_at,json=lastConversationCreatedAt,proto3" json:"last_conversation_created_at,omitempty"`
}

func (x *BusinessConversationSummaryModel) Reset() {
	*x = BusinessConversationSummaryModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_ai_assistant_v1_business_conversation_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessConversationSummaryModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessConversationSummaryModel) ProtoMessage() {}

func (x *BusinessConversationSummaryModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_ai_assistant_v1_business_conversation_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessConversationSummaryModel.ProtoReflect.Descriptor instead.
func (*BusinessConversationSummaryModel) Descriptor() ([]byte, []int) {
	return file_moego_models_ai_assistant_v1_business_conversation_models_proto_rawDescGZIP(), []int{2}
}

func (x *BusinessConversationSummaryModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BusinessConversationSummaryModel) GetConversationCount() int32 {
	if x != nil {
		return x.ConversationCount
	}
	return 0
}

func (x *BusinessConversationSummaryModel) GetNonemptyConversationCount() int32 {
	if x != nil {
		return x.NonemptyConversationCount
	}
	return 0
}

func (x *BusinessConversationSummaryModel) GetQuestionCount() int32 {
	if x != nil {
		return x.QuestionCount
	}
	return 0
}

func (x *BusinessConversationSummaryModel) GetAdoptedConversationCount() int32 {
	if x != nil {
		return x.AdoptedConversationCount
	}
	return 0
}

func (x *BusinessConversationSummaryModel) GetTotalCost() float64 {
	if x != nil {
		return x.TotalCost
	}
	return 0
}

func (x *BusinessConversationSummaryModel) GetTotalInputToken() int32 {
	if x != nil {
		return x.TotalInputToken
	}
	return 0
}

func (x *BusinessConversationSummaryModel) GetTotalOutputToken() int32 {
	if x != nil {
		return x.TotalOutputToken
	}
	return 0
}

func (x *BusinessConversationSummaryModel) GetTotalToken() int32 {
	if x != nil {
		return x.TotalToken
	}
	return 0
}

func (x *BusinessConversationSummaryModel) GetLastConversationCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LastConversationCreatedAt
	}
	return nil
}

var File_moego_models_ai_assistant_v1_business_conversation_models_proto protoreflect.FileDescriptor

var file_moego_models_ai_assistant_v1_business_conversation_models_proto_rawDesc = []byte{
	0x0a, 0x3f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x3e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x96, 0x05, 0x0a, 0x19, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e,
	0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x73, 0x63, 0x65, 0x6e, 0x61, 0x72, 0x69, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x73, 0x63, 0x65, 0x6e, 0x61, 0x72, 0x69, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x73, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x04, 0x63, 0x6f, 0x73, 0x74, 0x12, 0x2a,
	0x0a, 0x11, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x49, 0x6e, 0x70, 0x75, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2c, 0x0a, 0x12, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x5f, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4f, 0x75, 0x74,
	0x70, 0x75, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0d, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x2e, 0x0a, 0x13, 0x61, 0x64, 0x6f, 0x70, 0x74, 0x65, 0x64, 0x5f, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x61,
	0x64, 0x6f, 0x70, 0x74, 0x65, 0x64, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x69,
	0x6c, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x74, 0x72, 0x61, 0x69, 0x6c, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x12,
	0x20, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x12, 0x50, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39,
	0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xf7, 0x04, 0x0a, 0x21, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x27, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72,
	0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x6f, 0x70, 0x74, 0x65, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x61, 0x64, 0x6f, 0x70, 0x74, 0x65, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x73, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x04, 0x63,
	0x6f, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6f, 0x75, 0x74, 0x70,
	0x75, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1a, 0x0a,
	0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6e, 0x73,
	0x77, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6e, 0x73, 0x77, 0x65,
	0x72, 0x12, 0x58, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x33, 0x0a, 0x07, 0x73,
	0x65, 0x6e, 0x74, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x06, 0x73, 0x65, 0x6e, 0x74, 0x41, 0x74,
	0x12, 0x3b, 0x0a, 0x0b, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x0a, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x22, 0x8e, 0x04, 0x0a, 0x20, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x12, 0x63, 0x6f, 0x6e,
	0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3e, 0x0a, 0x1b, 0x6e, 0x6f, 0x6e, 0x65,
	0x6d, 0x70, 0x74, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x19, 0x6e,
	0x6f, 0x6e, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0d, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x3c, 0x0a, 0x1a, 0x61, 0x64, 0x6f, 0x70, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65,
	0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x18, 0x61, 0x64, 0x6f, 0x70, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x73, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x11,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x49, 0x6e,
	0x70, 0x75, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2c, 0x0a, 0x12, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x5f, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x5b, 0x0a, 0x1c, 0x6c, 0x61, 0x73, 0x74, 0x5f,
	0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x19, 0x6c, 0x61, 0x73, 0x74, 0x43,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x42, 0x89, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x69,
	0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a,
	0x5f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47,
	0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61,
	0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f,
	0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2f,
	0x76, 0x31, 0x3b, 0x61, 0x69, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_ai_assistant_v1_business_conversation_models_proto_rawDescOnce sync.Once
	file_moego_models_ai_assistant_v1_business_conversation_models_proto_rawDescData = file_moego_models_ai_assistant_v1_business_conversation_models_proto_rawDesc
)

func file_moego_models_ai_assistant_v1_business_conversation_models_proto_rawDescGZIP() []byte {
	file_moego_models_ai_assistant_v1_business_conversation_models_proto_rawDescOnce.Do(func() {
		file_moego_models_ai_assistant_v1_business_conversation_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_ai_assistant_v1_business_conversation_models_proto_rawDescData)
	})
	return file_moego_models_ai_assistant_v1_business_conversation_models_proto_rawDescData
}

var file_moego_models_ai_assistant_v1_business_conversation_models_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_models_ai_assistant_v1_business_conversation_models_proto_goTypes = []interface{}{
	(*BusinessConversationModel)(nil),         // 0: moego.models.ai_assistant.v1.BusinessConversationModel
	(*BusinessConversationQuestionModel)(nil), // 1: moego.models.ai_assistant.v1.BusinessConversationQuestionModel
	(*BusinessConversationSummaryModel)(nil),  // 2: moego.models.ai_assistant.v1.BusinessConversationSummaryModel
	(BusinessConversationStatus)(0),           // 3: moego.models.ai_assistant.v1.BusinessConversationStatus
	(*timestamppb.Timestamp)(nil),             // 4: google.protobuf.Timestamp
	(BusinessConversationQuestionStatus)(0),   // 5: moego.models.ai_assistant.v1.BusinessConversationQuestionStatus
}
var file_moego_models_ai_assistant_v1_business_conversation_models_proto_depIdxs = []int32{
	3, // 0: moego.models.ai_assistant.v1.BusinessConversationModel.status:type_name -> moego.models.ai_assistant.v1.BusinessConversationStatus
	4, // 1: moego.models.ai_assistant.v1.BusinessConversationModel.created_at:type_name -> google.protobuf.Timestamp
	4, // 2: moego.models.ai_assistant.v1.BusinessConversationModel.updated_at:type_name -> google.protobuf.Timestamp
	5, // 3: moego.models.ai_assistant.v1.BusinessConversationQuestionModel.status:type_name -> moego.models.ai_assistant.v1.BusinessConversationQuestionStatus
	4, // 4: moego.models.ai_assistant.v1.BusinessConversationQuestionModel.sent_at:type_name -> google.protobuf.Timestamp
	4, // 5: moego.models.ai_assistant.v1.BusinessConversationQuestionModel.answered_at:type_name -> google.protobuf.Timestamp
	4, // 6: moego.models.ai_assistant.v1.BusinessConversationQuestionModel.created_at:type_name -> google.protobuf.Timestamp
	4, // 7: moego.models.ai_assistant.v1.BusinessConversationQuestionModel.updated_at:type_name -> google.protobuf.Timestamp
	4, // 8: moego.models.ai_assistant.v1.BusinessConversationSummaryModel.last_conversation_created_at:type_name -> google.protobuf.Timestamp
	9, // [9:9] is the sub-list for method output_type
	9, // [9:9] is the sub-list for method input_type
	9, // [9:9] is the sub-list for extension type_name
	9, // [9:9] is the sub-list for extension extendee
	0, // [0:9] is the sub-list for field type_name
}

func init() { file_moego_models_ai_assistant_v1_business_conversation_models_proto_init() }
func file_moego_models_ai_assistant_v1_business_conversation_models_proto_init() {
	if File_moego_models_ai_assistant_v1_business_conversation_models_proto != nil {
		return
	}
	file_moego_models_ai_assistant_v1_business_conversation_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_ai_assistant_v1_business_conversation_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessConversationModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_ai_assistant_v1_business_conversation_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessConversationQuestionModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_ai_assistant_v1_business_conversation_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessConversationSummaryModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_ai_assistant_v1_business_conversation_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_ai_assistant_v1_business_conversation_models_proto_goTypes,
		DependencyIndexes: file_moego_models_ai_assistant_v1_business_conversation_models_proto_depIdxs,
		MessageInfos:      file_moego_models_ai_assistant_v1_business_conversation_models_proto_msgTypes,
	}.Build()
	File_moego_models_ai_assistant_v1_business_conversation_models_proto = out.File
	file_moego_models_ai_assistant_v1_business_conversation_models_proto_rawDesc = nil
	file_moego_models_ai_assistant_v1_business_conversation_models_proto_goTypes = nil
	file_moego_models_ai_assistant_v1_business_conversation_models_proto_depIdxs = nil
}

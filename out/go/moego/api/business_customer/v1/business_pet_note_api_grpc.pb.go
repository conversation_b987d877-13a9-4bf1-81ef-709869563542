// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/business_customer/v1/business_pet_note_api.proto

package businesscustomerapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BusinessPetNoteServiceClient is the client API for BusinessPetNoteService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BusinessPetNoteServiceClient interface {
	// Pin or unpin a pet note
	PinPetNote(ctx context.Context, in *PinPetNoteParams, opts ...grpc.CallOption) (*PinPetNoteResult, error)
}

type businessPetNoteServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBusinessPetNoteServiceClient(cc grpc.ClientConnInterface) BusinessPetNoteServiceClient {
	return &businessPetNoteServiceClient{cc}
}

func (c *businessPetNoteServiceClient) PinPetNote(ctx context.Context, in *PinPetNoteParams, opts ...grpc.CallOption) (*PinPetNoteResult, error) {
	out := new(PinPetNoteResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessPetNoteService/PinPetNote", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BusinessPetNoteServiceServer is the server API for BusinessPetNoteService service.
// All implementations must embed UnimplementedBusinessPetNoteServiceServer
// for forward compatibility
type BusinessPetNoteServiceServer interface {
	// Pin or unpin a pet note
	PinPetNote(context.Context, *PinPetNoteParams) (*PinPetNoteResult, error)
	mustEmbedUnimplementedBusinessPetNoteServiceServer()
}

// UnimplementedBusinessPetNoteServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBusinessPetNoteServiceServer struct {
}

func (UnimplementedBusinessPetNoteServiceServer) PinPetNote(context.Context, *PinPetNoteParams) (*PinPetNoteResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PinPetNote not implemented")
}
func (UnimplementedBusinessPetNoteServiceServer) mustEmbedUnimplementedBusinessPetNoteServiceServer() {
}

// UnsafeBusinessPetNoteServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BusinessPetNoteServiceServer will
// result in compilation errors.
type UnsafeBusinessPetNoteServiceServer interface {
	mustEmbedUnimplementedBusinessPetNoteServiceServer()
}

func RegisterBusinessPetNoteServiceServer(s grpc.ServiceRegistrar, srv BusinessPetNoteServiceServer) {
	s.RegisterService(&BusinessPetNoteService_ServiceDesc, srv)
}

func _BusinessPetNoteService_PinPetNote_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PinPetNoteParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetNoteServiceServer).PinPetNote(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessPetNoteService/PinPetNote",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetNoteServiceServer).PinPetNote(ctx, req.(*PinPetNoteParams))
	}
	return interceptor(ctx, in, info, handler)
}

// BusinessPetNoteService_ServiceDesc is the grpc.ServiceDesc for BusinessPetNoteService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BusinessPetNoteService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.business_customer.v1.BusinessPetNoteService",
	HandlerType: (*BusinessPetNoteServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PinPetNote",
			Handler:    _BusinessPetNoteService_PinPetNote_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/business_customer/v1/business_pet_note_api.proto",
}

# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.admin.enterprise.v1 import enterprise_admin_pb2 as moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2


class EnterpriseServiceStub(object):
    """enterprise admin service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.DescribeEnterprises = channel.unary_unary(
                '/moego.admin.enterprise.v1.EnterpriseService/DescribeEnterprises',
                request_serializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.DescribeEnterprisesParams.SerializeToString,
                response_deserializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.DescribeEnterprisesResult.FromString,
                _registered_method=True)
        self.CreateDemoEnterprise = channel.unary_unary(
                '/moego.admin.enterprise.v1.EnterpriseService/CreateDemoEnterprise',
                request_serializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.CreateDemoEnterpriseParams.SerializeToString,
                response_deserializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.CreateDemoEnterpriseResult.FromString,
                _registered_method=True)
        self.CreateDemoFranchisee = channel.unary_unary(
                '/moego.admin.enterprise.v1.EnterpriseService/CreateDemoFranchisee',
                request_serializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.CreateDemoFranchiseeParams.SerializeToString,
                response_deserializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.CreateDemoFranchiseeResult.FromString,
                _registered_method=True)
        self.UpdateEnterprise = channel.unary_unary(
                '/moego.admin.enterprise.v1.EnterpriseService/UpdateEnterprise',
                request_serializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.UpdateEnterpriseParams.SerializeToString,
                response_deserializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.UpdateEnterpriseResult.FromString,
                _registered_method=True)
        self.DescribeCompaniesByEnterprise = channel.unary_unary(
                '/moego.admin.enterprise.v1.EnterpriseService/DescribeCompaniesByEnterprise',
                request_serializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.DescribeCompanyInfosByEnterpriseParams.SerializeToString,
                response_deserializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.DescribeCompaniesByEnterpriseResult.FromString,
                _registered_method=True)
        self.UpdateCompanyBillPayerSetting = channel.unary_unary(
                '/moego.admin.enterprise.v1.EnterpriseService/UpdateCompanyBillPayerSetting',
                request_serializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.UpdateCompanyBillPayerSettingParams.SerializeToString,
                response_deserializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.UpdateCompanyBillPayerSettingResult.FromString,
                _registered_method=True)
        self.LinkEnterpriseAndCompanies = channel.unary_unary(
                '/moego.admin.enterprise.v1.EnterpriseService/LinkEnterpriseAndCompanies',
                request_serializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.LinkEnterpriseAndCompaniesParams.SerializeToString,
                response_deserializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.LinkEnterpriseAndCompaniesResult.FromString,
                _registered_method=True)
        self.UnLinkEnterpriseAndCompanies = channel.unary_unary(
                '/moego.admin.enterprise.v1.EnterpriseService/UnLinkEnterpriseAndCompanies',
                request_serializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.UnLinkEnterpriseAndCompaniesParams.SerializeToString,
                response_deserializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.UnLinkEnterpriseAndCompaniesResult.FromString,
                _registered_method=True)
        self.SearchEnterprise = channel.unary_unary(
                '/moego.admin.enterprise.v1.EnterpriseService/SearchEnterprise',
                request_serializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.SearchEnterpriseParams.SerializeToString,
                response_deserializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.SearchEnterpriseResult.FromString,
                _registered_method=True)


class EnterpriseServiceServicer(object):
    """enterprise admin service
    """

    def DescribeEnterprises(self, request, context):
        """describe enterprises
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateDemoEnterprise(self, request, context):
        """create demo enterprise
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateDemoFranchisee(self, request, context):
        """create demo franchisee
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateEnterprise(self, request, context):
        """update enterprise
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DescribeCompaniesByEnterprise(self, request, context):
        """describe enterprise company infos
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateCompanyBillPayerSetting(self, request, context):
        """update company bill payer setting, will call server-payment
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def LinkEnterpriseAndCompanies(self, request, context):
        """link enterprise and companies
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UnLinkEnterpriseAndCompanies(self, request, context):
        """unlink enterprise and companies
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SearchEnterprise(self, request, context):
        """search enterprise
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_EnterpriseServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'DescribeEnterprises': grpc.unary_unary_rpc_method_handler(
                    servicer.DescribeEnterprises,
                    request_deserializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.DescribeEnterprisesParams.FromString,
                    response_serializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.DescribeEnterprisesResult.SerializeToString,
            ),
            'CreateDemoEnterprise': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateDemoEnterprise,
                    request_deserializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.CreateDemoEnterpriseParams.FromString,
                    response_serializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.CreateDemoEnterpriseResult.SerializeToString,
            ),
            'CreateDemoFranchisee': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateDemoFranchisee,
                    request_deserializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.CreateDemoFranchiseeParams.FromString,
                    response_serializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.CreateDemoFranchiseeResult.SerializeToString,
            ),
            'UpdateEnterprise': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateEnterprise,
                    request_deserializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.UpdateEnterpriseParams.FromString,
                    response_serializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.UpdateEnterpriseResult.SerializeToString,
            ),
            'DescribeCompaniesByEnterprise': grpc.unary_unary_rpc_method_handler(
                    servicer.DescribeCompaniesByEnterprise,
                    request_deserializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.DescribeCompanyInfosByEnterpriseParams.FromString,
                    response_serializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.DescribeCompaniesByEnterpriseResult.SerializeToString,
            ),
            'UpdateCompanyBillPayerSetting': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateCompanyBillPayerSetting,
                    request_deserializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.UpdateCompanyBillPayerSettingParams.FromString,
                    response_serializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.UpdateCompanyBillPayerSettingResult.SerializeToString,
            ),
            'LinkEnterpriseAndCompanies': grpc.unary_unary_rpc_method_handler(
                    servicer.LinkEnterpriseAndCompanies,
                    request_deserializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.LinkEnterpriseAndCompaniesParams.FromString,
                    response_serializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.LinkEnterpriseAndCompaniesResult.SerializeToString,
            ),
            'UnLinkEnterpriseAndCompanies': grpc.unary_unary_rpc_method_handler(
                    servicer.UnLinkEnterpriseAndCompanies,
                    request_deserializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.UnLinkEnterpriseAndCompaniesParams.FromString,
                    response_serializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.UnLinkEnterpriseAndCompaniesResult.SerializeToString,
            ),
            'SearchEnterprise': grpc.unary_unary_rpc_method_handler(
                    servicer.SearchEnterprise,
                    request_deserializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.SearchEnterpriseParams.FromString,
                    response_serializer=moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.SearchEnterpriseResult.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.admin.enterprise.v1.EnterpriseService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.admin.enterprise.v1.EnterpriseService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class EnterpriseService(object):
    """enterprise admin service
    """

    @staticmethod
    def DescribeEnterprises(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.admin.enterprise.v1.EnterpriseService/DescribeEnterprises',
            moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.DescribeEnterprisesParams.SerializeToString,
            moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.DescribeEnterprisesResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateDemoEnterprise(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.admin.enterprise.v1.EnterpriseService/CreateDemoEnterprise',
            moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.CreateDemoEnterpriseParams.SerializeToString,
            moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.CreateDemoEnterpriseResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateDemoFranchisee(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.admin.enterprise.v1.EnterpriseService/CreateDemoFranchisee',
            moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.CreateDemoFranchiseeParams.SerializeToString,
            moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.CreateDemoFranchiseeResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateEnterprise(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.admin.enterprise.v1.EnterpriseService/UpdateEnterprise',
            moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.UpdateEnterpriseParams.SerializeToString,
            moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.UpdateEnterpriseResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DescribeCompaniesByEnterprise(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.admin.enterprise.v1.EnterpriseService/DescribeCompaniesByEnterprise',
            moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.DescribeCompanyInfosByEnterpriseParams.SerializeToString,
            moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.DescribeCompaniesByEnterpriseResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateCompanyBillPayerSetting(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.admin.enterprise.v1.EnterpriseService/UpdateCompanyBillPayerSetting',
            moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.UpdateCompanyBillPayerSettingParams.SerializeToString,
            moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.UpdateCompanyBillPayerSettingResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def LinkEnterpriseAndCompanies(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.admin.enterprise.v1.EnterpriseService/LinkEnterpriseAndCompanies',
            moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.LinkEnterpriseAndCompaniesParams.SerializeToString,
            moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.LinkEnterpriseAndCompaniesResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UnLinkEnterpriseAndCompanies(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.admin.enterprise.v1.EnterpriseService/UnLinkEnterpriseAndCompanies',
            moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.UnLinkEnterpriseAndCompaniesParams.SerializeToString,
            moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.UnLinkEnterpriseAndCompaniesResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SearchEnterprise(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.admin.enterprise.v1.EnterpriseService/SearchEnterprise',
            moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.SearchEnterpriseParams.SerializeToString,
            moego_dot_admin_dot_enterprise_dot_v1_dot_enterprise__admin__pb2.SearchEnterpriseResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

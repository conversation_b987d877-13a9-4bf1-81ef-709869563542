# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from moego.admin.ai_assistant.v1 import conversation_template_admin_pb2 as moego_dot_admin_dot_ai__assistant_dot_v1_dot_conversation__template__admin__pb2
from moego.models.ai_assistant.v1 import conversation_template_models_pb2 as moego_dot_models_dot_ai__assistant_dot_v1_dot_conversation__template__models__pb2


class ConversationTemplateServiceStub(object):
    """the conversation_template service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.UpsertConversationTemplate = channel.unary_unary(
                '/moego.admin.ai_assistant.v1.ConversationTemplateService/UpsertConversationTemplate',
                request_serializer=moego_dot_admin_dot_ai__assistant_dot_v1_dot_conversation__template__admin__pb2.UpsertConversationTemplateParams.SerializeToString,
                response_deserializer=moego_dot_models_dot_ai__assistant_dot_v1_dot_conversation__template__models__pb2.ConversationTemplateModel.FromString,
                _registered_method=True)
        self.DescribeConversationTemplates = channel.unary_unary(
                '/moego.admin.ai_assistant.v1.ConversationTemplateService/DescribeConversationTemplates',
                request_serializer=moego_dot_admin_dot_ai__assistant_dot_v1_dot_conversation__template__admin__pb2.DescribeConversationTemplatesParams.SerializeToString,
                response_deserializer=moego_dot_admin_dot_ai__assistant_dot_v1_dot_conversation__template__admin__pb2.DescribeConversationTemplatesResult.FromString,
                _registered_method=True)
        self.DeleteConversationTemplate = channel.unary_unary(
                '/moego.admin.ai_assistant.v1.ConversationTemplateService/DeleteConversationTemplate',
                request_serializer=moego_dot_admin_dot_ai__assistant_dot_v1_dot_conversation__template__admin__pb2.DeleteConversationTemplateParams.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                _registered_method=True)


class ConversationTemplateServiceServicer(object):
    """the conversation_template service
    """

    def UpsertConversationTemplate(self, request, context):
        """insert or update conversation template
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DescribeConversationTemplates(self, request, context):
        """describe conversation templates
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteConversationTemplate(self, request, context):
        """delete conversation template
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ConversationTemplateServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'UpsertConversationTemplate': grpc.unary_unary_rpc_method_handler(
                    servicer.UpsertConversationTemplate,
                    request_deserializer=moego_dot_admin_dot_ai__assistant_dot_v1_dot_conversation__template__admin__pb2.UpsertConversationTemplateParams.FromString,
                    response_serializer=moego_dot_models_dot_ai__assistant_dot_v1_dot_conversation__template__models__pb2.ConversationTemplateModel.SerializeToString,
            ),
            'DescribeConversationTemplates': grpc.unary_unary_rpc_method_handler(
                    servicer.DescribeConversationTemplates,
                    request_deserializer=moego_dot_admin_dot_ai__assistant_dot_v1_dot_conversation__template__admin__pb2.DescribeConversationTemplatesParams.FromString,
                    response_serializer=moego_dot_admin_dot_ai__assistant_dot_v1_dot_conversation__template__admin__pb2.DescribeConversationTemplatesResult.SerializeToString,
            ),
            'DeleteConversationTemplate': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteConversationTemplate,
                    request_deserializer=moego_dot_admin_dot_ai__assistant_dot_v1_dot_conversation__template__admin__pb2.DeleteConversationTemplateParams.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.admin.ai_assistant.v1.ConversationTemplateService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.admin.ai_assistant.v1.ConversationTemplateService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class ConversationTemplateService(object):
    """the conversation_template service
    """

    @staticmethod
    def UpsertConversationTemplate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.admin.ai_assistant.v1.ConversationTemplateService/UpsertConversationTemplate',
            moego_dot_admin_dot_ai__assistant_dot_v1_dot_conversation__template__admin__pb2.UpsertConversationTemplateParams.SerializeToString,
            moego_dot_models_dot_ai__assistant_dot_v1_dot_conversation__template__models__pb2.ConversationTemplateModel.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DescribeConversationTemplates(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.admin.ai_assistant.v1.ConversationTemplateService/DescribeConversationTemplates',
            moego_dot_admin_dot_ai__assistant_dot_v1_dot_conversation__template__admin__pb2.DescribeConversationTemplatesParams.SerializeToString,
            moego_dot_admin_dot_ai__assistant_dot_v1_dot_conversation__template__admin__pb2.DescribeConversationTemplatesResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteConversationTemplate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.admin.ai_assistant.v1.ConversationTemplateService/DeleteConversationTemplate',
            moego_dot_admin_dot_ai__assistant_dot_v1_dot_conversation__template__admin__pb2.DeleteConversationTemplateParams.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

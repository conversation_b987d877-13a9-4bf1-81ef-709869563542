from moego.models.account.v1 import account_models_pb2 as _account_models_pb2
from moego.utils.v1 import condition_messages_pb2 as _condition_messages_pb2
from moego.utils.v1 import pagination_messages_pb2 as _pagination_messages_pb2
from moego.utils.v2 import condition_messages_pb2 as _condition_messages_pb2_1
from moego.utils.v2 import pagination_messages_pb2 as _pagination_messages_pb2_1
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class SearchAccountForAdminRequest(_message.Message):
    __slots__ = ("page", "source_condition", "email_condition", "name_condition", "account_id_condition")
    PAGE_FIELD_NUMBER: _ClassVar[int]
    SOURCE_CONDITION_FIELD_NUMBER: _ClassVar[int]
    EMAIL_CONDITION_FIELD_NUMBER: _ClassVar[int]
    NAME_CONDITION_FIELD_NUMBER: _ClassVar[int]
    ACCOUNT_ID_CONDITION_FIELD_NUMBER: _ClassVar[int]
    page: _pagination_messages_pb2.PaginationRequest
    source_condition: _condition_messages_pb2.StringCondition
    email_condition: _condition_messages_pb2.StringCondition
    name_condition: _condition_messages_pb2.StringCondition
    account_id_condition: _condition_messages_pb2.Int64Condition
    def __init__(self, page: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ..., source_condition: _Optional[_Union[_condition_messages_pb2.StringCondition, _Mapping]] = ..., email_condition: _Optional[_Union[_condition_messages_pb2.StringCondition, _Mapping]] = ..., name_condition: _Optional[_Union[_condition_messages_pb2.StringCondition, _Mapping]] = ..., account_id_condition: _Optional[_Union[_condition_messages_pb2.Int64Condition, _Mapping]] = ...) -> None: ...

class SearchAccountForAdminResponse(_message.Message):
    __slots__ = ("page", "accounts")
    PAGE_FIELD_NUMBER: _ClassVar[int]
    ACCOUNTS_FIELD_NUMBER: _ClassVar[int]
    page: _pagination_messages_pb2.PaginationResponse
    accounts: _containers.RepeatedCompositeFieldContainer[_account_models_pb2.AccountModel]
    def __init__(self, page: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ..., accounts: _Optional[_Iterable[_Union[_account_models_pb2.AccountModel, _Mapping]]] = ...) -> None: ...

class SearchAccountRequest(_message.Message):
    __slots__ = ("predicate", "order_bys", "pagination")
    PREDICATE_FIELD_NUMBER: _ClassVar[int]
    ORDER_BYS_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    predicate: _condition_messages_pb2_1.Predicate
    order_bys: _containers.RepeatedCompositeFieldContainer[_condition_messages_pb2_1.OrderBy]
    pagination: _pagination_messages_pb2_1.PaginationRequest
    def __init__(self, predicate: _Optional[_Union[_condition_messages_pb2_1.Predicate, _Mapping]] = ..., order_bys: _Optional[_Iterable[_Union[_condition_messages_pb2_1.OrderBy, _Mapping]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2_1.PaginationRequest, _Mapping]] = ...) -> None: ...

class SearchAccountResponse(_message.Message):
    __slots__ = ("pagination", "accounts")
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    ACCOUNTS_FIELD_NUMBER: _ClassVar[int]
    pagination: _pagination_messages_pb2_1.PaginationResponse
    accounts: _containers.RepeatedCompositeFieldContainer[_account_models_pb2.AccountModel]
    def __init__(self, pagination: _Optional[_Union[_pagination_messages_pb2_1.PaginationResponse, _Mapping]] = ..., accounts: _Optional[_Iterable[_Union[_account_models_pb2.AccountModel, _Mapping]]] = ...) -> None: ...

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/service/appointment/v1/appointment_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/service/appointment/v1/appointment_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.type import date_pb2 as google_dot_type_dot_date__pb2
from google.type import interval_pb2 as google_dot_type_dot_interval__pb2
from google.type import money_pb2 as google_dot_type_dot_money__pb2
from moego.models.appointment.v1 import appointment_defs_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_appointment__defs__pb2
from moego.models.appointment.v1 import appointment_enums_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_appointment__enums__pb2
from moego.models.appointment.v1 import appointment_models_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_appointment__models__pb2
from moego.models.appointment.v1 import appointment_note_defs_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_appointment__note__defs__pb2
from moego.models.appointment.v1 import appointment_extra_info_models_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_appointment__extra__info__models__pb2
from moego.models.appointment.v1 import block_time_models_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_block__time__models__pb2
from moego.models.appointment.v1 import pet_detail_defs_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_pet__detail__defs__pb2
from moego.models.appointment.v1 import pet_detail_enums_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_pet__detail__enums__pb2
from moego.models.appointment.v1 import wait_list_enums_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_wait__list__enums__pb2
from moego.models.fulfillment.v1 import fulfillment_defs_pb2 as moego_dot_models_dot_fulfillment_dot_v1_dot_fulfillment__defs__pb2
from moego.models.offering.v1 import service_enum_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__enum__pb2
from moego.models.order.v1 import order_detail_defs_pb2 as moego_dot_models_dot_order_dot_v1_dot_order__detail__defs__pb2
from moego.models.order.v1 import order_detail_models_pb2 as moego_dot_models_dot_order_dot_v1_dot_order__detail__models__pb2
from moego.models.payment.v1 import pre_auth_defs_pb2 as moego_dot_models_dot_payment_dot_v1_dot_pre__auth__defs__pb2
from moego.utils.v1 import struct_pb2 as moego_dot_utils_dot_v1_dot_struct__pb2
from moego.utils.v2 import condition_messages_pb2 as moego_dot_utils_dot_v2_dot_condition__messages__pb2
from moego.utils.v2 import pagination_messages_pb2 as moego_dot_utils_dot_v2_dot_pagination__messages__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n6moego/service/appointment/v1/appointment_service.proto\x12\x1cmoego.service.appointment.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x16google/type/date.proto\x1a\x1agoogle/type/interval.proto\x1a\x17google/type/money.proto\x1a\x32moego/models/appointment/v1/appointment_defs.proto\x1a\x33moego/models/appointment/v1/appointment_enums.proto\x1a\x34moego/models/appointment/v1/appointment_models.proto\x1a\x37moego/models/appointment/v1/appointment_note_defs.proto\x1a?moego/models/appointment/v1/appointment_extra_info_models.proto\x1a\x33moego/models/appointment/v1/block_time_models.proto\x1a\x31moego/models/appointment/v1/pet_detail_defs.proto\x1a\x32moego/models/appointment/v1/pet_detail_enums.proto\x1a\x31moego/models/appointment/v1/wait_list_enums.proto\x1a\x32moego/models/fulfillment/v1/fulfillment_defs.proto\x1a+moego/models/offering/v1/service_enum.proto\x1a-moego/models/order/v1/order_detail_defs.proto\x1a/moego/models/order/v1/order_detail_models.proto\x1a+moego/models/payment/v1/pre_auth_defs.proto\x1a\x1bmoego/utils/v1/struct.proto\x1a\'moego/utils/v2/condition_messages.proto\x1a(moego/utils/v2/pagination_messages.proto\x1a\x17validate/validate.proto\"\xdf\x04\n\x18\x43reateAppointmentRequest\x12]\n\x0b\x61ppointment\x18\x01 \x01(\x0b\x32\x31.moego.models.appointment.v1.AppointmentCreateDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x0b\x61ppointment\x12]\n\x0bpet_details\x18\x02 \x03(\x0b\x32).moego.models.appointment.v1.PetDetailDefB\x11\xfa\x42\x0e\x92\x01\x0b\x08\x01\x10\x64\"\x05\x8a\x01\x02\x10\x01R\npetDetails\x12S\n\x08pre_auth\x18\x03 \x01(\x0b\x32).moego.models.payment.v1.PreAuthEnableDefB\x08\xfa\x42\x05\x8a\x01\x02\x08\x01H\x00R\x07preAuth\x88\x01\x01\x12^\n\x05notes\x18\x04 \x03(\x0b\x32\x35.moego.models.appointment.v1.AppointmentNoteCreateDefB\x11\xfa\x42\x0e\x92\x01\x0b\x08\x00\x10\x64\"\x05\x8a\x01\x02\x10\x01R\x05notes\x12&\n\ncompany_id\x18\x05 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12(\n\x0b\x62usiness_id\x18\x06 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12\"\n\x08staff_id\x18\x07 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x07staffId\x12>\n\ncreated_at\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.TimestampH\x01R\tcreatedAt\x88\x01\x01\x42\x0b\n\t_pre_authB\r\n\x0b_created_at\"B\n\x19\x43reateAppointmentResponse\x12%\n\x0e\x61ppointment_id\x18\x01 \x01(\x03R\rappointmentId\"\x9f\x02\n\x18UpdateAppointmentRequest\x12.\n\x0e\x61ppointment_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\rappointmentId\x12]\n\x0b\x61ppointment\x18\x02 \x01(\x0b\x32\x31.moego.models.appointment.v1.AppointmentUpdateDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x0b\x61ppointment\x12&\n\ncompany_id\x18\x05 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12(\n\x0b\x62usiness_id\x18\x06 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12\"\n\x08staff_id\x18\x07 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x07staffId\"\x1b\n\x19UpdateAppointmentResponse\"\x9d\x01\n\x15GetAppointmentRequest\x12.\n\x0e\x61ppointment_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\rappointmentId\x12(\n\ncompany_id\x18\x02 \x01(\x03\x42\t\xfa\x42\x06\"\x04 \x00@\x01R\tcompanyId\x12*\n\x0b\x62usiness_id\x18\x03 \x01(\x03\x42\t\xfa\x42\x06\"\x04 \x00@\x01R\nbusinessId\"i\n\x16GetAppointmentResponse\x12O\n\x0b\x61ppointment\x18\x01 \x01(\x0b\x32-.moego.models.appointment.v1.AppointmentModelR\x0b\x61ppointment\"\xa4\x01\n\x19GetAppointmentListRequest\x12\x33\n\x0e\x61ppointment_id\x18\x01 \x03(\x03\x42\x0c\xfa\x42\t\x92\x01\x06\"\x04\"\x02 \x00R\rappointmentId\x12&\n\ncompany_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12*\n\x0b\x62usiness_id\x18\x03 \x01(\x03\x42\t\x18\x01\xfa\x42\x04\"\x02(\x00R\nbusinessId\"o\n\x1aGetAppointmentListResponse\x12Q\n\x0c\x61ppointments\x18\x01 \x03(\x0b\x32-.moego.models.appointment.v1.AppointmentModelR\x0c\x61ppointments\"\xfb\x05\n!GetCustomerLastAppointmentRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12-\n\x0b\x63ustomer_id\x18\x02 \x03(\x03\x42\x0c\xfa\x42\t\x92\x01\x06\"\x04\"\x02 \x00R\ncustomerId\x12K\n\x06status\x18\x03 \x01(\x0e\x32..moego.models.appointment.v1.AppointmentStatusH\x00R\x06status\x88\x01\x01\x12\x63\n\x06\x66ilter\x18\x04 \x01(\x0b\x32\x46.moego.service.appointment.v1.GetCustomerLastAppointmentRequest.FilterH\x01R\x06\x66ilter\x88\x01\x01\x1a\xb6\x03\n\x06\x46ilter\x12\x44\n\x10start_time_range\x18\x01 \x01(\x0b\x32\x15.google.type.IntervalH\x00R\x0estartTimeRange\x88\x01\x01\x12@\n\x0e\x65nd_time_range\x18\x02 \x01(\x0b\x32\x15.google.type.IntervalH\x01R\x0c\x65ndTimeRange\x88\x01\x01\x12W\n\x12service_item_types\x18\x03 \x03(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x10serviceItemTypes\x12\x34\n\x14\x66ilter_no_start_time\x18\x08 \x01(\x08H\x02R\x11\x66ilterNoStartTime\x88\x01\x01\x12\x39\n\x16\x66ilter_booking_request\x18\t \x01(\x08H\x03R\x14\x66ilterBookingRequest\x88\x01\x01\x42\x13\n\x11_start_time_rangeB\x11\n\x0f_end_time_rangeB\x17\n\x15_filter_no_start_timeB\x19\n\x17_filter_booking_requestB\t\n\x07_statusB\t\n\x07_filter\"\xbb\x02\n\"GetCustomerLastAppointmentResponse\x12\x99\x01\n\x19\x63ustomer_last_appointment\x18\x01 \x03(\x0b\x32].moego.service.appointment.v1.GetCustomerLastAppointmentResponse.CustomerLastAppointmentEntryR\x17\x63ustomerLastAppointment\x1ay\n\x1c\x43ustomerLastAppointmentEntry\x12\x10\n\x03key\x18\x01 \x01(\x03R\x03key\x12\x43\n\x05value\x18\x02 \x01(\x0b\x32-.moego.models.appointment.v1.AppointmentModelR\x05value:\x02\x38\x01\"\xf9\x01\n\"CalculateAppointmentInvoiceRequest\x12]\n\x0bpet_details\x18\x03 \x03(\x0b\x32).moego.models.appointment.v1.PetDetailDefB\x11\xfa\x42\x0e\x92\x01\x0b\x08\x01\x10\x64\"\x05\x8a\x01\x02\x10\x01R\npetDetails\x12&\n\ncompany_id\x18\x05 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12(\n\x0b\x62usiness_id\x18\x06 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12\"\n\x08staff_id\x18\x07 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x07staffId\"\\\n#CalculateAppointmentInvoiceResponse\x12\x35\n\x05order\x18\x01 \x01(\x0b\x32\x1f.moego.models.order.v1.OrderDefR\x05order\"\xce\x02\n\x1fGetInProgressAppointmentRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12U\n\x11service_item_type\x18\x02 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x0fserviceItemType\x12-\n\x0b\x62usiness_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\nbusinessId\x88\x01\x01\x12-\n\x0b\x63ustomer_id\x18\x04 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x01R\ncustomerId\x88\x01\x01\x12#\n\x06pet_id\x18\x05 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x02R\x05petId\x88\x01\x01\x42\x0e\n\x0c_business_idB\x0e\n\x0c_customer_idB\t\n\x07_pet_id\"a\n GetInProgressAppointmentResponse\x12*\n\x0e\x61ppointment_id\x18\x01 \x01(\x03H\x00R\rappointmentId\x88\x01\x01\x42\x11\n\x0f_appointment_id\"\xd6\x03\n\x12\x43reateBlockRequest\x12(\n\x0b\x62usiness_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12\"\n\x08staff_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x07staffId\x12&\n\ncompany_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12&\n\ncreated_by\x18\x04 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcreatedBy\x12\x43\n\nstart_time\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.TimestampB\x08\xfa\x42\x05\xb2\x01\x02\x08\x01R\tstartTime\x12?\n\x08\x65nd_time\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.TimestampB\x08\xfa\x42\x05\xb2\x01\x02\x08\x01R\x07\x65ndTime\x12&\n\ncolor_code\x18\x07 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x10R\tcolorCode\x12 \n\x0b\x64\x65scription\x18\x08 \x01(\tR\x0b\x64\x65scription\x12R\n\x06source\x18\t \x01(\x0e\x32..moego.models.appointment.v1.AppointmentSourceB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x06source\"%\n\x13\x43reateBlockResponse\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\"\x8d\x10\n\x17ListAppointmentsRequest\x12\x46\n\npagination\x18\x01 \x01(\x0b\x32!.moego.utils.v2.PaginationRequestH\x00R\npagination\x88\x01\x01\x12&\n\ncompany_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12\x33\n\x0c\x62usiness_ids\x18\x03 \x03(\x03\x42\x10\xfa\x42\r\x92\x01\n\x08\x00\x18\x01\"\x04\"\x02 \x00R\x0b\x62usinessIds\x12T\n\x06\x66ilter\x18\x04 \x01(\x0b\x32<.moego.service.appointment.v1.ListAppointmentsRequest.FilterR\x06\x66ilter\x12\x34\n\torder_bys\x18\x05 \x03(\x0b\x32\x17.moego.utils.v2.OrderByR\x08orderBys\x12\x88\x01\n\x13priority_order_type\x18\x06 \x01(\x0e\x32G.moego.service.appointment.v1.ListAppointmentsRequest.PriorityOrderTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x01R\x11priorityOrderType\x88\x01\x01\x1a\xcd\x0b\n\x06\x46ilter\x12?\n\x10start_time_range\x18\x01 \x01(\x0b\x32\x15.google.type.IntervalR\x0estartTimeRange\x12;\n\x0e\x65nd_time_range\x18\x02 \x01(\x0b\x32\x15.google.type.IntervalR\x0c\x65ndTimeRange\x12L\n\x17last_updated_time_range\x18\x03 \x01(\x0b\x32\x15.google.type.IntervalR\x14lastUpdatedTimeRange\x12\x46\n\x06status\x18\x04 \x03(\x0e\x32..moego.models.appointment.v1.AppointmentStatusR\x06status\x12j\n\x12wait_list_statuses\x18\x06 \x03(\x0e\x32+.moego.models.appointment.v1.WaitListStatusB\x0f\xfa\x42\x0c\x92\x01\t\x18\x01\"\x05\x82\x01\x02\x10\x01R\x10waitListStatuses\x12\x34\n\x14\x66ilter_no_start_time\x18\x08 \x01(\x08H\x00R\x11\x66ilterNoStartTime\x88\x01\x01\x12\x39\n\x16\x66ilter_booking_request\x18\t \x01(\x08H\x01R\x14\x66ilterBookingRequest\x88\x01\x01\x12>\n\x15service_type_includes\x18\n \x03(\x05\x42\n\xfa\x42\x07\x92\x01\x04\x08\x00\x18\x01R\x13serviceTypeIncludes\x12\x33\n\x0c\x63ustomer_ids\x18\x05 \x03(\x03\x42\x10\xfa\x42\r\x92\x01\n\x10\x64\x18\x01\"\x04\"\x02 \x00R\x0b\x63ustomerIds\x12_\n\tdate_type\x18\x0b \x01(\x0e\x32\x31.moego.service.appointment.v1.AppointmentDateTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x02R\x08\x64\x61teType\x88\x01\x01\x12.\n\tstaff_ids\x18\x0c \x03(\x03\x42\x11\xfa\x42\x0e\x92\x01\x0b\x10\xf4\x03\x18\x01\"\x04\"\x02 \x00R\x08staffIds\x12\x41\n\x10\x61ppointment_date\x18\r \x01(\x0b\x32\x11.google.type.DateH\x03R\x0f\x61ppointmentDate\x88\x01\x01\x12?\n\x19include_service_operation\x18\x0e \x01(\x08H\x04R\x17includeServiceOperation\x88\x01\x01\x12)\n\x07pet_ids\x18\x0f \x03(\x03\x42\x10\xfa\x42\r\x92\x01\n\x10\x64\x18\x01\"\x04\"\x02 \x00R\x06petIds\x12\x31\n\x0bservice_ids\x18\x10 \x03(\x03\x42\x10\xfa\x42\r\x92\x01\n\x10\x64\x18\x01\"\x04\"\x02 \x00R\nserviceIds\x12(\n\rinclude_block\x18\x11 \x01(\x08H\x05R\x0cincludeBlock\x88\x01\x01\x12+\n\x0fis_waiting_list\x18\x12 \x01(\x08H\x06R\risWaitingList\x88\x01\x01\x12\x44\n\x13\x63heck_in_time_range\x18\x13 \x01(\x0b\x32\x15.google.type.IntervalR\x10\x63heckInTimeRange\x12\x46\n\x14\x63heck_out_time_range\x18\x14 \x01(\x0b\x32\x15.google.type.IntervalR\x11\x63heckOutTimeRange\x12j\n\x10payment_statuses\x18\x15 \x03(\x0e\x32\x35.moego.models.appointment.v1.AppointmentPaymentStatusB\x08\xfa\x42\x05\x92\x01\x02\x18\x01R\x0fpaymentStatusesB\x17\n\x15_filter_no_start_timeB\x19\n\x17_filter_booking_requestB\x0c\n\n_date_typeB\x13\n\x11_appointment_dateB\x1c\n\x1a_include_service_operationB\x10\n\x0e_include_blockB\x12\n\x10_is_waiting_list\"?\n\x11PriorityOrderType\x12\x0f\n\x0bUNSPECIFIED\x10\x00\x12\x19\n\x15UNEXPIRED_UNCONFIRMED\x10\x01\x42\r\n\x0b_paginationB\x16\n\x14_priority_order_type\"\xc5\x01\n\x18ListAppointmentsResponse\x12G\n\npagination\x18\x01 \x01(\x0b\x32\".moego.utils.v2.PaginationResponseH\x00R\npagination\x88\x01\x01\x12Q\n\x0c\x61ppointments\x18\x02 \x03(\x0b\x32-.moego.models.appointment.v1.AppointmentModelR\x0c\x61ppointmentsB\r\n\x0b_pagination\"\xcd\x04\n(CreateAppointmentForOnlineBookingRequest\x12m\n\x0b\x61ppointment\x18\x01 \x01(\x0b\x32\x41.moego.models.appointment.v1.AppointmentCreateForOnlineBookingDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x0b\x61ppointment\x12]\n\x0bpet_details\x18\x02 \x03(\x0b\x32).moego.models.appointment.v1.PetDetailDefB\x11\xfa\x42\x0e\x92\x01\x0b\x08\x01\x10\x64\"\x05\x8a\x01\x02\x10\x01R\npetDetails\x12^\n\x05notes\x18\x04 \x03(\x0b\x32\x35.moego.models.appointment.v1.AppointmentNoteCreateDefB\x11\xfa\x42\x0e\x92\x01\x0b\x08\x00\x10\x64\"\x05\x8a\x01\x02\x10\x01R\x05notes\x12&\n\ncompany_id\x18\x05 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12(\n\x0b\x62usiness_id\x18\x06 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12\"\n\x08staff_id\x18\x07 \x01(\x03\x42\x07\xfa\x42\x04\"\x02(\x00R\x07staffId\x12\x35\n\x12\x62ooking_request_id\x18\x08 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x10\x62ookingRequestId\x12\x46\n\x1a\x62ooking_request_identifier\x18\t \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01R\x18\x62ookingRequestIdentifier\"R\n)CreateAppointmentForOnlineBookingResponse\x12%\n\x0e\x61ppointment_id\x18\x01 \x01(\x03R\rappointmentId\"\xd1\x02\n\x15ListBlockTimesRequest\x12&\n\ncompany_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12\x33\n\x0c\x62usiness_ids\x18\x03 \x03(\x03\x42\x10\xfa\x42\r\x92\x01\n\x08\x01\x18\x01\"\x04\"\x02 \x00R\x0b\x62usinessIds\x12R\n\x06\x66ilter\x18\x04 \x01(\x0b\x32:.moego.service.appointment.v1.ListBlockTimesRequest.FilterR\x06\x66ilter\x1a\x86\x01\n\x06\x46ilter\x12?\n\x10start_time_range\x18\x01 \x01(\x0b\x32\x15.google.type.IntervalR\x0estartTimeRange\x12;\n\x0e\x65nd_time_range\x18\x02 \x01(\x0b\x32\x15.google.type.IntervalR\x0c\x65ndTimeRange\"f\n\x16ListBlockTimesResponse\x12L\n\x0b\x62lock_times\x18\x01 \x03(\x0b\x32+.moego.models.appointment.v1.BlockTimeModelR\nblockTimes\"\xea\x02\n\x18\x42\x61tchQuickCheckInRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12(\n\x0b\x62usiness_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12&\n\nservice_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tserviceId\x12+\n\x07pet_ids\x18\x04 \x03(\x03\x42\x12\xfa\x42\x0f\x92\x01\x0c\x08\x01\x10\x64\x18\x01\"\x04\"\x02 \x00R\x06petIds\x12/\n\x04\x64\x61te\x18\x05 \x01(\x0b\x32\x11.google.type.DateB\x08\xfa\x42\x05\xb2\x01\x02\x08\x01R\x04\x64\x61te\x12\"\n\x08staff_id\x18\x06 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x07staffId\x12R\n\x06source\x18\x07 \x01(\x0e\x32..moego.models.appointment.v1.AppointmentSourceB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x06source\"\x8b\x01\n\x19\x42\x61tchQuickCheckInResponse\x12\x36\n\x17\x63reated_appointment_ids\x18\x01 \x03(\x03R\x15\x63reatedAppointmentIds\x12\x36\n\x17updated_appointment_ids\x18\x02 \x03(\x03R\x15updatedAppointmentIds\"\x97\x11\n!UpdateAppointmentSelectiveRequest\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12H\n\x10\x61ppointment_date\x18\x02 \x01(\tB\x18\xfa\x42\x15r\x13\x32\x11\\d{4}-\\d{2}-\\d{2}H\x00R\x0f\x61ppointmentDate\x88\x01\x01\x12\x45\n\x16\x61ppointment_start_time\x18\x03 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00H\x01R\x14\x61ppointmentStartTime\x88\x01\x01\x12\x41\n\x14\x61ppointment_end_time\x18\x04 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00H\x02R\x12\x61ppointmentEndTime\x88\x01\x01\x12\x33\n\x0e\x63onfirmed_time\x18\x05 \x01(\x03\x42\x07\xfa\x42\x04\"\x02(\x00H\x03R\rconfirmedTime\x88\x01\x01\x12\x30\n\rcheck_in_time\x18\x06 \x01(\x03\x42\x07\xfa\x42\x04\"\x02(\x00H\x04R\x0b\x63heckInTime\x88\x01\x01\x12\x32\n\x0e\x63heck_out_time\x18\x07 \x01(\x03\x42\x07\xfa\x42\x04\"\x02(\x00H\x05R\x0c\x63heckOutTime\x88\x01\x01\x12\x31\n\rcanceled_time\x18\x08 \x01(\x03\x42\x07\xfa\x42\x04\"\x02(\x00H\x06R\x0c\x63\x61nceledTime\x88\x01\x01\x12+\n\ncolor_code\x18\t \x01(\tB\x07\xfa\x42\x04r\x02\x18\x14H\x07R\tcolorCode\x88\x01\x01\x12\x32\n\x0e\x63\x61ncel_by_type\x18\n \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02(\x00H\x08R\x0c\x63\x61ncelByType\x88\x01\x01\x12)\n\tcancel_by\x18\x0b \x01(\x03\x42\x07\xfa\x42\x04\"\x02(\x00H\tR\x08\x63\x61ncelBy\x88\x01\x01\x12\x34\n\x0f\x63onfirm_by_type\x18\x0c \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02(\x00H\nR\rconfirmByType\x88\x01\x01\x12\"\n\nconfirm_by\x18\r \x01(\x03H\x0bR\tconfirmBy\x88\x01\x01\x12q\n\x15status_before_checkin\x18\x0e \x01(\x0e\x32..moego.models.appointment.v1.AppointmentStatusB\x08\xfa\x42\x05\x82\x01\x02\x10\x01H\x0cR\x13statusBeforeCheckin\x88\x01\x01\x12m\n\x13status_before_ready\x18\x0f \x01(\x0e\x32..moego.models.appointment.v1.AppointmentStatusB\x08\xfa\x42\x05\x82\x01\x02\x10\x01H\rR\x11statusBeforeReady\x88\x01\x01\x12o\n\x14status_before_finish\x18\x10 \x01(\x0e\x32..moego.models.appointment.v1.AppointmentStatusB\x08\xfa\x42\x05\x82\x01\x02\x10\x01H\x0eR\x12statusBeforeFinish\x88\x01\x01\x12@\n\x0bupdate_time\x18\x11 \x01(\x0b\x32\x1a.google.protobuf.TimestampH\x0fR\nupdateTime\x88\x01\x01\x12\'\n\rupdated_by_id\x18\x12 \x01(\x03H\x10R\x0bupdatedById\x88\x01\x01\x12W\n\x06status\x18\x13 \x01(\x0e\x32..moego.models.appointment.v1.AppointmentStatusB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x11R\x06status\x88\x01\x01\x12+\n\nready_time\x18\x14 \x01(\x03\x42\x07\xfa\x42\x04\"\x02(\x00H\x12R\treadyTime\x88\x01\x01\x12k\n\x0epayment_status\x18\x15 \x01(\x0e\x32\x35.moego.models.appointment.v1.AppointmentPaymentStatusB\x08\xfa\x42\x05\x82\x01\x02\x10\x01H\x13R\rpaymentStatus\x88\x01\x01\x12u\n\x12\x62ook_online_status\x18\x16 \x01(\x0e\x32\x38.moego.models.appointment.v1.AppointmentBookOnlineStatusB\x08\xfa\x42\x05\x82\x01\x02\x10\x01H\x14R\x10\x62ookOnlineStatus\x88\x01\x01\x12Z\n\x0bpet_details\x18\x17 \x03(\x0b\x32/.moego.models.appointment.v1.UpdatePetDetailDefB\x08\xfa\x42\x05\x92\x01\x02\x08\x00R\npetDetails\x12\x66\n\x0eupdate_by_type\x18\x18 \x01(\x0e\x32\x31.moego.models.appointment.v1.AppointmentUpdatedByB\x08\xfa\x42\x05\x82\x01\x02\x10\x01H\x15R\x0cupdateByType\x88\x01\x01\x12*\n\x0cis_deprecate\x18\x19 \x01(\x08\x42\x02\x18\x01H\x16R\x0bisDeprecate\x88\x01\x01\x42\x13\n\x11_appointment_dateB\x19\n\x17_appointment_start_timeB\x17\n\x15_appointment_end_timeB\x11\n\x0f_confirmed_timeB\x10\n\x0e_check_in_timeB\x11\n\x0f_check_out_timeB\x10\n\x0e_canceled_timeB\r\n\x0b_color_codeB\x11\n\x0f_cancel_by_typeB\x0c\n\n_cancel_byB\x12\n\x10_confirm_by_typeB\r\n\x0b_confirm_byB\x18\n\x16_status_before_checkinB\x16\n\x14_status_before_readyB\x17\n\x15_status_before_finishB\x0e\n\x0c_update_timeB\x10\n\x0e_updated_by_idB\t\n\x07_statusB\r\n\x0b_ready_timeB\x11\n\x0f_payment_statusB\x15\n\x13_book_online_statusB\x11\n\x0f_update_by_typeB\x0f\n\r_is_deprecate\"I\n\"UpdateAppointmentSelectiveResponse\x12#\n\raffected_rows\x18\x01 \x01(\x05R\x0c\x61\x66\x66\x65\x63tedRows\"\xcb\x03\n\x1dListAppointmentForPetsRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12+\n\x07pet_ids\x18\x02 \x03(\x03\x42\x12\xfa\x42\x0f\x92\x01\x0c\x08\x01\x10\x64\x18\x01\"\x04\"\x02 \x00R\x06petIds\x12\x64\n\x06\x66ilter\x18\x03 \x01(\x0b\x32\x42.moego.service.appointment.v1.ListAppointmentForPetsRequest.FilterB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x06\x66ilter\x1a\xee\x01\n\x06\x46ilter\x12(\n\nservice_id\x18\x01 \x01(\x03\x42\t\xfa\x42\x06\"\x04 \x00@\x01R\tserviceId\x12/\n\x04\x64\x61te\x18\x02 \x01(\x0b\x32\x11.google.type.DateB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x04\x64\x61te\x12-\n\x0b\x62usiness_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\nbusinessId\x88\x01\x01\x12J\n\x08statuses\x18\x04 \x03(\x0e\x32..moego.models.appointment.v1.AppointmentStatusR\x08statusesB\x0e\n\x0c_business_id\"\xfe\x02\n\x1eListAppointmentForPetsResponse\x12Q\n\x0c\x61ppointments\x18\x01 \x03(\x0b\x32-.moego.models.appointment.v1.AppointmentModelR\x0c\x61ppointments\x12\x9b\x01\n\x1dpet_id_to_appointment_id_list\x18\x02 \x03(\x0b\x32Z.moego.service.appointment.v1.ListAppointmentForPetsResponse.PetIdToAppointmentIdListEntryR\x18petIdToAppointmentIdList\x1ak\n\x1dPetIdToAppointmentIdListEntry\x12\x10\n\x03key\x18\x01 \x01(\x03R\x03key\x12\x34\n\x05value\x18\x02 \x01(\x0b\x32\x1e.moego.utils.v1.Int64ListValueR\x05value:\x02\x38\x01\"\xf8\x03\n#ListAppointmentsForCustomersRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12\x35\n\x0c\x63ustomer_ids\x18\x02 \x03(\x03\x42\x12\xfa\x42\x0f\x92\x01\x0c\x08\x01\x10\x64\x18\x01\"\x04\"\x02 \x00R\x0b\x63ustomerIds\x12j\n\x06\x66ilter\x18\x03 \x01(\x0b\x32H.moego.service.appointment.v1.ListAppointmentsForCustomersRequest.FilterB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x06\x66ilter\x1a\x85\x02\n\x06\x46ilter\x12Z\n\tdate_type\x18\x01 \x01(\x0e\x32\x31.moego.service.appointment.v1.AppointmentDateTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x08\x64\x61teType\x12_\n\x08statuses\x18\x02 \x03(\x0e\x32..moego.models.appointment.v1.AppointmentStatusB\x13\xfa\x42\x10\x92\x01\r\x10\n\x18\x01\"\x07\x82\x01\x04\x10\x01 \x00R\x08statuses\x12>\n\x15service_type_includes\x18\n \x03(\x05\x42\n\xfa\x42\x07\x92\x01\x04\x08\x00\x18\x01R\x13serviceTypeIncludes\"y\n$ListAppointmentsForCustomersResponse\x12Q\n\x0c\x61ppointments\x18\x01 \x03(\x0b\x32-.moego.models.appointment.v1.AppointmentModelR\x0c\x61ppointments\"\xad\x04\n\x18\x43\x61ncelAppointmentRequest\x12.\n\x0e\x61ppointment_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\rappointmentId\x12\x61\n\x0e\x63\x61ncel_by_type\x18\x02 \x01(\x0e\x32\x31.moego.models.appointment.v1.AppointmentUpdatedByB\x08\xfa\x42\x05\x82\x01\x02\x10\x01R\x0c\x63\x61ncelByType\x12$\n\tcancel_by\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x08\x63\x61ncelBy\x12\x33\n\rcancel_reason\x18\x04 \x01(\tB\t\xfa\x42\x06r\x04\x18\x80\x80@H\x00R\x0c\x63\x61ncelReason\x88\x01\x01\x12Y\n\x07no_show\x18\x05 \x01(\x0e\x32\x34.moego.models.appointment.v1.AppointmentNoShowStatusB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x06noShow\x12\x91\x01\n\x1frepeat_appointment_modify_scope\x18\x06 \x01(\x0e\x32\x39.moego.models.appointment.v1.RepeatAppointmentModifyScopeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x01R\x1crepeatAppointmentModifyScope\x88\x01\x01\x42\x10\n\x0e_cancel_reasonB\"\n _repeat_appointment_modify_scope\"\x1b\n\x19\x43\x61ncelAppointmentResponse\"\xe0\x01\n BatchBookAgainAppointmentRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12:\n\x0f\x61ppointment_ids\x18\x02 \x03(\x03\x42\x11\xfa\x42\x0e\x92\x01\x0b\x08\x01\x10\xe8\x07\"\x04\"\x02 \x00R\x0e\x61ppointmentIds\x12\x32\n\x0btarget_date\x18\x03 \x01(\x0b\x32\x11.google.type.DateR\ntargetDate\x12$\n\trebook_by\x18\x04 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x08rebookBy\"v\n!BatchBookAgainAppointmentResponse\x12Q\n\x0c\x61ppointments\x18\x01 \x03(\x0b\x32-.moego.models.appointment.v1.AppointmentModelR\x0c\x61ppointments\"\xef\x01\n\x1d\x42\x61tchCancelAppointmentRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12:\n\x0f\x61ppointment_ids\x18\x02 \x03(\x03\x42\x11\xfa\x42\x0e\x92\x01\x0b\x08\x01\x10\xe8\x07\"\x04\"\x02 \x00R\x0e\x61ppointmentIds\x12$\n\tcancel_by\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x08\x63\x61ncelBy\x12\x32\n\rcancel_reason\x18\x04 \x01(\tB\x08\xfa\x42\x05r\x03\x18\x80\x08H\x00R\x0c\x63\x61ncelReason\x88\x01\x01\x42\x10\n\x0e_cancel_reason\"s\n\x1e\x42\x61tchCancelAppointmentResponse\x12Q\n\x0c\x61ppointments\x18\x01 \x03(\x0b\x32-.moego.models.appointment.v1.AppointmentModelR\x0c\x61ppointments\"J\n\x1e\x43ountAppointmentForPetsRequest\x12(\n\x07pet_ids\x18\x02 \x03(\x03\x42\x0f\xfa\x42\x0c\x92\x01\t\x10\x90N\"\x04\"\x02 \x00R\x06petIds\"\x88\x02\n\x1f\x43ountAppointmentForPetsResponse\x12\x98\x01\n\x1bpet_id_to_appointment_count\x18\x01 \x03(\x0b\x32Z.moego.service.appointment.v1.CountAppointmentForPetsResponse.PetIdToAppointmentCountEntryR\x17petIdToAppointmentCount\x1aJ\n\x1cPetIdToAppointmentCountEntry\x12\x10\n\x03key\x18\x01 \x01(\x03R\x03key\x12\x14\n\x05value\x18\x02 \x01(\x05R\x05value:\x02\x38\x01\"\xb9\x01\n\x19\x44\x65leteAppointmentsRequest\x12+\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\tcompanyId\x88\x01\x01\x12-\n\x0b\x62usiness_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x01R\nbusinessId\x88\x01\x01\x12!\n\x03ids\x18\x03 \x03(\x03\x42\x0f\xfa\x42\x0c\x92\x01\t\x10\x90N\"\x04\"\x02 \x00R\x03idsB\r\n\x0b_company_idB\x0e\n\x0c_business_id\"A\n\x1a\x44\x65leteAppointmentsResponse\x12#\n\raffected_rows\x18\x01 \x01(\x05R\x0c\x61\x66\x66\x65\x63tedRows\"\xba\x01\n\x1aRestoreAppointmentsRequest\x12+\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\tcompanyId\x88\x01\x01\x12-\n\x0b\x62usiness_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x01R\nbusinessId\x88\x01\x01\x12!\n\x03ids\x18\x03 \x03(\x03\x42\x0f\xfa\x42\x0c\x92\x01\t\x10\x90N\"\x04\"\x02 \x00R\x03idsB\r\n\x0b_company_idB\x0e\n\x0c_business_id\"B\n\x1bRestoreAppointmentsResponse\x12#\n\raffected_rows\x18\x01 \x01(\x05R\x0c\x61\x66\x66\x65\x63tedRows\"\xa6\x02\n$RescheduleBoardingAppointmentRequest\x12+\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\tcompanyId\x88\x01\x01\x12.\n\x0e\x61ppointment_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\rappointmentId\x12<\n\nstart_date\x18\x03 \x01(\tB\x18\xfa\x42\x15r\x13\x32\x11\\d{4}-\\d{2}-\\d{2}H\x01R\tstartDate\x88\x01\x01\x12\x38\n\x08\x65nd_date\x18\x04 \x01(\tB\x18\xfa\x42\x15r\x13\x32\x11\\d{4}-\\d{2}-\\d{2}H\x02R\x07\x65ndDate\x88\x01\x01\x42\r\n\x0b_company_idB\r\n\x0b_start_dateB\x0b\n\t_end_date\"O\n\x1dSyncAppointmentToOrderRequest\x12.\n\x0e\x61ppointment_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\rappointmentId\" \n\x1eSyncAppointmentToOrderResponse\"\'\n%RescheduleBoardingAppointmentResponse\"W\n\x19PreviewOrderDetailRequest\x12:\n\x0f\x61ppointment_ids\x18\x01 \x03(\x03\x42\x11\xfa\x42\x0e\x92\x01\x0b\x08\x01\x10\xe8\x07\"\x04\"\x02 \x00R\x0e\x61ppointmentIds\"j\n\x1aPreviewOrderDetailResponse\x12L\n\rorder_details\x18\x01 \x03(\x0b\x32\'.moego.models.order.v1.OrderDetailModelR\x0corderDetails\"N\n\x1cPreviewOrderLineItemsRequest\x12.\n\x0e\x61ppointment_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\rappointmentId\"\xb7\x01\n\x1dPreviewOrderLineItemsResponse\x12J\n\x0cpet_services\x18\x01 \x03(\x0b\x32\'.moego.models.fulfillment.v1.PetServiceR\x0bpetServices\x12J\n\nsurcharges\x18\x02 \x03(\x0b\x32*.moego.models.fulfillment.v1.SurchargeItemR\nsurcharges\"\xc7\x02\n$GetTimeOverlapAppointmentListRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12-\n\x0b\x63ustomer_id\x18\x02 \x03(\x03\x42\x0c\xfa\x42\t\x92\x01\x06\"\x04\"\x02 \x00R\ncustomerId\x12%\n\x07pet_ids\x18\x03 \x03(\x03\x42\x0c\xfa\x42\t\x92\x01\x06\"\x04\"\x02 \x00R\x06petIds\x12\x61\n\x06\x66ilter\x18\x04 \x01(\x0b\x32I.moego.service.appointment.v1.GetTimeOverlapAppointmentListRequest.FilterR\x06\x66ilter\x1a>\n\x06\x46ilter\x12\x34\n\ndate_range\x18\x01 \x01(\x0b\x32\x15.google.type.IntervalR\tdateRange\"\xa0\x02\n%GetTimeOverlapAppointmentListResponse\x12\x83\x01\n\x10\x61ppointment_list\x18\x01 \x03(\x0b\x32X.moego.service.appointment.v1.GetTimeOverlapAppointmentListResponse.AppointmentListEntryR\x0f\x61ppointmentList\x1aq\n\x14\x41ppointmentListEntry\x12\x10\n\x03key\x18\x01 \x01(\x03R\x03key\x12\x43\n\x05value\x18\x02 \x01(\x0b\x32-.moego.service.appointment.v1.AppointmentListR\x05value:\x02\x38\x01\"d\n\x0f\x41ppointmentList\x12Q\n\x0c\x61ppointments\x18\x01 \x03(\x0b\x32-.moego.models.appointment.v1.AppointmentModelR\x0c\x61ppointments\"\xab\x01\n\x1bPreviewEstimateOrderRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12(\n\x0b\x62usiness_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12:\n\x0f\x61ppointment_ids\x18\x03 \x03(\x03\x42\x11\xfa\x42\x0e\x92\x01\x0b\x08\x01\x10\xe8\x07\"\x04\"\x02 \x00R\x0e\x61ppointmentIds\"\x96\x03\n\x1cPreviewEstimateOrderResponse\x12t\n\x10\x65stimated_orders\x18\x01 \x03(\x0b\x32I.moego.service.appointment.v1.PreviewEstimateOrderResponse.EstimatedOrderR\x0f\x65stimatedOrders\x1a\xff\x01\n\x0e\x45stimatedOrder\x12%\n\x0e\x61ppointment_id\x18\x01 \x01(\x03R\rappointmentId\x12?\n\x11services_subtotal\x18\x02 \x01(\x0b\x32\x12.google.type.MoneyR\x10servicesSubtotal\x12H\n\x16services_charges_total\x18\x03 \x01(\x0b\x32\x12.google.type.MoneyR\x14servicesChargesTotal\x12;\n\x0f\x65stimated_total\x18\x04 \x01(\x0b\x32\x12.google.type.MoneyR\x0e\x65stimatedTotal\"R\n\x14ListExtraInfoRequest\x12:\n\x0f\x61ppointment_ids\x18\x01 \x03(\x03\x42\x11\xfa\x42\x0e\x92\x01\x0b\x08\x01\x10\xe8\x07\"\x04\"\x02 \x00R\x0e\x61ppointmentIds\"n\n\x15ListExtraInfoResponse\x12U\n\nextra_info\x18\x01 \x03(\x0b\x32\x36.moego.models.appointment.v1.AppointmentExtraInfoModelR\textraInfo*\x83\x01\n\x13\x41ppointmentDateType\x12%\n!APPOINTMENT_DATE_TYPE_UNSPECIFIED\x10\x00\x12\x08\n\x04LAST\x10\x01\x12\x08\n\x04NEXT\x10\x02\x12\t\n\x05TODAY\x10\x03\x12\x10\n\x0cNEXT_NOT_END\x10\x04\x12\x14\n\x10NEXT_AFTER_TODAY\x10\x05\x32\x9e \n\x12\x41ppointmentService\x12\x84\x01\n\x11\x43reateAppointment\x12\x36.moego.service.appointment.v1.CreateAppointmentRequest\x1a\x37.moego.service.appointment.v1.CreateAppointmentResponse\x12\x84\x01\n\x11UpdateAppointment\x12\x36.moego.service.appointment.v1.UpdateAppointmentRequest\x1a\x37.moego.service.appointment.v1.UpdateAppointmentResponse\x12\x9f\x01\n\x1aUpdateAppointmentSelective\x12?.moego.service.appointment.v1.UpdateAppointmentSelectiveRequest\<EMAIL>\x12{\n\x0eGetAppointment\x12\x33.moego.service.appointment.v1.GetAppointmentRequest\x1a\x34.moego.service.appointment.v1.GetAppointmentResponse\x12\x87\x01\n\x12GetAppointmentList\x12\x37.moego.service.appointment.v1.GetAppointmentListRequest\x1a\x38.moego.service.appointment.v1.GetAppointmentListResponse\x12\x9f\x01\n\x1aGetCustomerLastAppointment\x12?.moego.service.appointment.v1.GetCustomerLastAppointmentRequest\<EMAIL>\x12\xa2\x01\n\x1b\x43\x61lculateAppointmentInvoice\<EMAIL>\x1a\x41.moego.service.appointment.v1.CalculateAppointmentInvoiceResponse\x12\x99\x01\n\x18GetInProgressAppointment\x12=.moego.service.appointment.v1.GetInProgressAppointmentRequest\x1a>.moego.service.appointment.v1.GetInProgressAppointmentResponse\x12r\n\x0b\x43reateBlock\x12\x30.moego.service.appointment.v1.CreateBlockRequest\x1a\x31.moego.service.appointment.v1.CreateBlockResponse\x12\x81\x01\n\x10ListAppointments\x12\x35.moego.service.appointment.v1.ListAppointmentsRequest\x1a\x36.moego.service.appointment.v1.ListAppointmentsResponse\x12{\n\x0eListBlockTimes\x12\x33.moego.service.appointment.v1.ListBlockTimesRequest\x1a\x34.moego.service.appointment.v1.ListBlockTimesResponse\x12\xb4\x01\n!CreateAppointmentForOnlineBooking\x12\x46.moego.service.appointment.v1.CreateAppointmentForOnlineBookingRequest\x1aG.moego.service.appointment.v1.CreateAppointmentForOnlineBookingResponse\x12\x84\x01\n\x11\x42\x61tchQuickCheckIn\x12\x36.moego.service.appointment.v1.BatchQuickCheckInRequest\x1a\x37.moego.service.appointment.v1.BatchQuickCheckInResponse\x12\x93\x01\n\x16ListAppointmentForPets\x12;.moego.service.appointment.v1.ListAppointmentForPetsRequest\x1a<.moego.service.appointment.v1.ListAppointmentForPetsResponse\x12\xa5\x01\n\x1cListAppointmentsForCustomers\x12\x41.moego.service.appointment.v1.ListAppointmentsForCustomersRequest\x1a\x42.moego.service.appointment.v1.ListAppointmentsForCustomersResponse\x12\x84\x01\n\x11\x43\x61ncelAppointment\x12\x36.moego.service.appointment.v1.CancelAppointmentRequest\x1a\x37.moego.service.appointment.v1.CancelAppointmentResponse\x12\x9c\x01\n\x19\x42\x61tchBookAgainAppointment\x12>.moego.service.appointment.v1.BatchBookAgainAppointmentRequest\x1a?.moego.service.appointment.v1.BatchBookAgainAppointmentResponse\x12\x93\x01\n\x16\x42\x61tchCancelAppointment\x12;.moego.service.appointment.v1.BatchCancelAppointmentRequest\x1a<.moego.service.appointment.v1.BatchCancelAppointmentResponse\x12\x96\x01\n\x17\x43ountAppointmentForPets\x12<.moego.service.appointment.v1.CountAppointmentForPetsRequest\x1a=.moego.service.appointment.v1.CountAppointmentForPetsResponse\x12\x87\x01\n\x12\x44\x65leteAppointments\x12\x37.moego.service.appointment.v1.DeleteAppointmentsRequest\x1a\x38.moego.service.appointment.v1.DeleteAppointmentsResponse\x12\x8a\x01\n\x13RestoreAppointments\x12\x38.moego.service.appointment.v1.RestoreAppointmentsRequest\x1a\x39.moego.service.appointment.v1.RestoreAppointmentsResponse\x12\xa8\x01\n\x1dRescheduleBoardingAppointment\x12\x42.moego.service.appointment.v1.RescheduleBoardingAppointmentRequest\x1a\x43.moego.service.appointment.v1.RescheduleBoardingAppointmentResponse\x12\x93\x01\n\x16SyncAppointmentToOrder\x12;.moego.service.appointment.v1.SyncAppointmentToOrderRequest\x1a<.moego.service.appointment.v1.SyncAppointmentToOrderResponse\x12\x87\x01\n\x12PreviewOrderDetail\x12\x37.moego.service.appointment.v1.PreviewOrderDetailRequest\x1a\x38.moego.service.appointment.v1.PreviewOrderDetailResponse\x12\x90\x01\n\x15PreviewOrderLineItems\x12:.moego.service.appointment.v1.PreviewOrderLineItemsRequest\x1a;.moego.service.appointment.v1.PreviewOrderLineItemsResponse\x12\xa8\x01\n\x1dGetTimeOverlapAppointmentList\x12\x42.moego.service.appointment.v1.GetTimeOverlapAppointmentListRequest\x1a\x43.moego.service.appointment.v1.GetTimeOverlapAppointmentListResponse\x12\x8d\x01\n\x14PreviewEstimateOrder\x12\x39.moego.service.appointment.v1.PreviewEstimateOrderRequest\x1a:.moego.service.appointment.v1.PreviewEstimateOrderResponse\x12x\n\rListExtraInfo\x12\x32.moego.service.appointment.v1.ListExtraInfoRequest\x1a\x33.moego.service.appointment.v1.ListExtraInfoResponseB\x8c\x01\n$com.moego.idl.service.appointment.v1P\x01Zbgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1;appointmentsvcpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.service.appointment.v1.appointment_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n$com.moego.idl.service.appointment.v1P\001Zbgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1;appointmentsvcpb'
  _globals['_CREATEAPPOINTMENTREQUEST'].fields_by_name['appointment']._loaded_options = None
  _globals['_CREATEAPPOINTMENTREQUEST'].fields_by_name['appointment']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_CREATEAPPOINTMENTREQUEST'].fields_by_name['pet_details']._loaded_options = None
  _globals['_CREATEAPPOINTMENTREQUEST'].fields_by_name['pet_details']._serialized_options = b'\372B\016\222\001\013\010\001\020d\"\005\212\001\002\020\001'
  _globals['_CREATEAPPOINTMENTREQUEST'].fields_by_name['pre_auth']._loaded_options = None
  _globals['_CREATEAPPOINTMENTREQUEST'].fields_by_name['pre_auth']._serialized_options = b'\372B\005\212\001\002\010\001'
  _globals['_CREATEAPPOINTMENTREQUEST'].fields_by_name['notes']._loaded_options = None
  _globals['_CREATEAPPOINTMENTREQUEST'].fields_by_name['notes']._serialized_options = b'\372B\016\222\001\013\010\000\020d\"\005\212\001\002\020\001'
  _globals['_CREATEAPPOINTMENTREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_CREATEAPPOINTMENTREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATEAPPOINTMENTREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_CREATEAPPOINTMENTREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATEAPPOINTMENTREQUEST'].fields_by_name['staff_id']._loaded_options = None
  _globals['_CREATEAPPOINTMENTREQUEST'].fields_by_name['staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEAPPOINTMENTREQUEST'].fields_by_name['appointment_id']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTREQUEST'].fields_by_name['appointment_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEAPPOINTMENTREQUEST'].fields_by_name['appointment']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTREQUEST'].fields_by_name['appointment']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_UPDATEAPPOINTMENTREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEAPPOINTMENTREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEAPPOINTMENTREQUEST'].fields_by_name['staff_id']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTREQUEST'].fields_by_name['staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETAPPOINTMENTREQUEST'].fields_by_name['appointment_id']._loaded_options = None
  _globals['_GETAPPOINTMENTREQUEST'].fields_by_name['appointment_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETAPPOINTMENTREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_GETAPPOINTMENTREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\006\"\004 \000@\001'
  _globals['_GETAPPOINTMENTREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_GETAPPOINTMENTREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\006\"\004 \000@\001'
  _globals['_GETAPPOINTMENTLISTREQUEST'].fields_by_name['appointment_id']._loaded_options = None
  _globals['_GETAPPOINTMENTLISTREQUEST'].fields_by_name['appointment_id']._serialized_options = b'\372B\t\222\001\006\"\004\"\002 \000'
  _globals['_GETAPPOINTMENTLISTREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_GETAPPOINTMENTLISTREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETAPPOINTMENTLISTREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_GETAPPOINTMENTLISTREQUEST'].fields_by_name['business_id']._serialized_options = b'\030\001\372B\004\"\002(\000'
  _globals['_GETCUSTOMERLASTAPPOINTMENTREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_GETCUSTOMERLASTAPPOINTMENTREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETCUSTOMERLASTAPPOINTMENTREQUEST'].fields_by_name['customer_id']._loaded_options = None
  _globals['_GETCUSTOMERLASTAPPOINTMENTREQUEST'].fields_by_name['customer_id']._serialized_options = b'\372B\t\222\001\006\"\004\"\002 \000'
  _globals['_GETCUSTOMERLASTAPPOINTMENTRESPONSE_CUSTOMERLASTAPPOINTMENTENTRY']._loaded_options = None
  _globals['_GETCUSTOMERLASTAPPOINTMENTRESPONSE_CUSTOMERLASTAPPOINTMENTENTRY']._serialized_options = b'8\001'
  _globals['_CALCULATEAPPOINTMENTINVOICEREQUEST'].fields_by_name['pet_details']._loaded_options = None
  _globals['_CALCULATEAPPOINTMENTINVOICEREQUEST'].fields_by_name['pet_details']._serialized_options = b'\372B\016\222\001\013\010\001\020d\"\005\212\001\002\020\001'
  _globals['_CALCULATEAPPOINTMENTINVOICEREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_CALCULATEAPPOINTMENTINVOICEREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CALCULATEAPPOINTMENTINVOICEREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_CALCULATEAPPOINTMENTINVOICEREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CALCULATEAPPOINTMENTINVOICEREQUEST'].fields_by_name['staff_id']._loaded_options = None
  _globals['_CALCULATEAPPOINTMENTINVOICEREQUEST'].fields_by_name['staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETINPROGRESSAPPOINTMENTREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_GETINPROGRESSAPPOINTMENTREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETINPROGRESSAPPOINTMENTREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_GETINPROGRESSAPPOINTMENTREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETINPROGRESSAPPOINTMENTREQUEST'].fields_by_name['customer_id']._loaded_options = None
  _globals['_GETINPROGRESSAPPOINTMENTREQUEST'].fields_by_name['customer_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETINPROGRESSAPPOINTMENTREQUEST'].fields_by_name['pet_id']._loaded_options = None
  _globals['_GETINPROGRESSAPPOINTMENTREQUEST'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATEBLOCKREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_CREATEBLOCKREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATEBLOCKREQUEST'].fields_by_name['staff_id']._loaded_options = None
  _globals['_CREATEBLOCKREQUEST'].fields_by_name['staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATEBLOCKREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_CREATEBLOCKREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATEBLOCKREQUEST'].fields_by_name['created_by']._loaded_options = None
  _globals['_CREATEBLOCKREQUEST'].fields_by_name['created_by']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATEBLOCKREQUEST'].fields_by_name['start_time']._loaded_options = None
  _globals['_CREATEBLOCKREQUEST'].fields_by_name['start_time']._serialized_options = b'\372B\005\262\001\002\010\001'
  _globals['_CREATEBLOCKREQUEST'].fields_by_name['end_time']._loaded_options = None
  _globals['_CREATEBLOCKREQUEST'].fields_by_name['end_time']._serialized_options = b'\372B\005\262\001\002\010\001'
  _globals['_CREATEBLOCKREQUEST'].fields_by_name['color_code']._loaded_options = None
  _globals['_CREATEBLOCKREQUEST'].fields_by_name['color_code']._serialized_options = b'\372B\004r\002\030\020'
  _globals['_CREATEBLOCKREQUEST'].fields_by_name['source']._loaded_options = None
  _globals['_CREATEBLOCKREQUEST'].fields_by_name['source']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_LISTAPPOINTMENTSREQUEST_FILTER'].fields_by_name['wait_list_statuses']._loaded_options = None
  _globals['_LISTAPPOINTMENTSREQUEST_FILTER'].fields_by_name['wait_list_statuses']._serialized_options = b'\372B\014\222\001\t\030\001\"\005\202\001\002\020\001'
  _globals['_LISTAPPOINTMENTSREQUEST_FILTER'].fields_by_name['service_type_includes']._loaded_options = None
  _globals['_LISTAPPOINTMENTSREQUEST_FILTER'].fields_by_name['service_type_includes']._serialized_options = b'\372B\007\222\001\004\010\000\030\001'
  _globals['_LISTAPPOINTMENTSREQUEST_FILTER'].fields_by_name['customer_ids']._loaded_options = None
  _globals['_LISTAPPOINTMENTSREQUEST_FILTER'].fields_by_name['customer_ids']._serialized_options = b'\372B\r\222\001\n\020d\030\001\"\004\"\002 \000'
  _globals['_LISTAPPOINTMENTSREQUEST_FILTER'].fields_by_name['date_type']._loaded_options = None
  _globals['_LISTAPPOINTMENTSREQUEST_FILTER'].fields_by_name['date_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_LISTAPPOINTMENTSREQUEST_FILTER'].fields_by_name['staff_ids']._loaded_options = None
  _globals['_LISTAPPOINTMENTSREQUEST_FILTER'].fields_by_name['staff_ids']._serialized_options = b'\372B\016\222\001\013\020\364\003\030\001\"\004\"\002 \000'
  _globals['_LISTAPPOINTMENTSREQUEST_FILTER'].fields_by_name['pet_ids']._loaded_options = None
  _globals['_LISTAPPOINTMENTSREQUEST_FILTER'].fields_by_name['pet_ids']._serialized_options = b'\372B\r\222\001\n\020d\030\001\"\004\"\002 \000'
  _globals['_LISTAPPOINTMENTSREQUEST_FILTER'].fields_by_name['service_ids']._loaded_options = None
  _globals['_LISTAPPOINTMENTSREQUEST_FILTER'].fields_by_name['service_ids']._serialized_options = b'\372B\r\222\001\n\020d\030\001\"\004\"\002 \000'
  _globals['_LISTAPPOINTMENTSREQUEST_FILTER'].fields_by_name['payment_statuses']._loaded_options = None
  _globals['_LISTAPPOINTMENTSREQUEST_FILTER'].fields_by_name['payment_statuses']._serialized_options = b'\372B\005\222\001\002\030\001'
  _globals['_LISTAPPOINTMENTSREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_LISTAPPOINTMENTSREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTAPPOINTMENTSREQUEST'].fields_by_name['business_ids']._loaded_options = None
  _globals['_LISTAPPOINTMENTSREQUEST'].fields_by_name['business_ids']._serialized_options = b'\372B\r\222\001\n\010\000\030\001\"\004\"\002 \000'
  _globals['_LISTAPPOINTMENTSREQUEST'].fields_by_name['priority_order_type']._loaded_options = None
  _globals['_LISTAPPOINTMENTSREQUEST'].fields_by_name['priority_order_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_CREATEAPPOINTMENTFORONLINEBOOKINGREQUEST'].fields_by_name['appointment']._loaded_options = None
  _globals['_CREATEAPPOINTMENTFORONLINEBOOKINGREQUEST'].fields_by_name['appointment']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_CREATEAPPOINTMENTFORONLINEBOOKINGREQUEST'].fields_by_name['pet_details']._loaded_options = None
  _globals['_CREATEAPPOINTMENTFORONLINEBOOKINGREQUEST'].fields_by_name['pet_details']._serialized_options = b'\372B\016\222\001\013\010\001\020d\"\005\212\001\002\020\001'
  _globals['_CREATEAPPOINTMENTFORONLINEBOOKINGREQUEST'].fields_by_name['notes']._loaded_options = None
  _globals['_CREATEAPPOINTMENTFORONLINEBOOKINGREQUEST'].fields_by_name['notes']._serialized_options = b'\372B\016\222\001\013\010\000\020d\"\005\212\001\002\020\001'
  _globals['_CREATEAPPOINTMENTFORONLINEBOOKINGREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_CREATEAPPOINTMENTFORONLINEBOOKINGREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATEAPPOINTMENTFORONLINEBOOKINGREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_CREATEAPPOINTMENTFORONLINEBOOKINGREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATEAPPOINTMENTFORONLINEBOOKINGREQUEST'].fields_by_name['staff_id']._loaded_options = None
  _globals['_CREATEAPPOINTMENTFORONLINEBOOKINGREQUEST'].fields_by_name['staff_id']._serialized_options = b'\372B\004\"\002(\000'
  _globals['_CREATEAPPOINTMENTFORONLINEBOOKINGREQUEST'].fields_by_name['booking_request_id']._loaded_options = None
  _globals['_CREATEAPPOINTMENTFORONLINEBOOKINGREQUEST'].fields_by_name['booking_request_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATEAPPOINTMENTFORONLINEBOOKINGREQUEST'].fields_by_name['booking_request_identifier']._loaded_options = None
  _globals['_CREATEAPPOINTMENTFORONLINEBOOKINGREQUEST'].fields_by_name['booking_request_identifier']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_LISTBLOCKTIMESREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_LISTBLOCKTIMESREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTBLOCKTIMESREQUEST'].fields_by_name['business_ids']._loaded_options = None
  _globals['_LISTBLOCKTIMESREQUEST'].fields_by_name['business_ids']._serialized_options = b'\372B\r\222\001\n\010\001\030\001\"\004\"\002 \000'
  _globals['_BATCHQUICKCHECKINREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_BATCHQUICKCHECKINREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_BATCHQUICKCHECKINREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_BATCHQUICKCHECKINREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_BATCHQUICKCHECKINREQUEST'].fields_by_name['service_id']._loaded_options = None
  _globals['_BATCHQUICKCHECKINREQUEST'].fields_by_name['service_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_BATCHQUICKCHECKINREQUEST'].fields_by_name['pet_ids']._loaded_options = None
  _globals['_BATCHQUICKCHECKINREQUEST'].fields_by_name['pet_ids']._serialized_options = b'\372B\017\222\001\014\010\001\020d\030\001\"\004\"\002 \000'
  _globals['_BATCHQUICKCHECKINREQUEST'].fields_by_name['date']._loaded_options = None
  _globals['_BATCHQUICKCHECKINREQUEST'].fields_by_name['date']._serialized_options = b'\372B\005\262\001\002\010\001'
  _globals['_BATCHQUICKCHECKINREQUEST'].fields_by_name['staff_id']._loaded_options = None
  _globals['_BATCHQUICKCHECKINREQUEST'].fields_by_name['staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_BATCHQUICKCHECKINREQUEST'].fields_by_name['source']._loaded_options = None
  _globals['_BATCHQUICKCHECKINREQUEST'].fields_by_name['source']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['id']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['appointment_date']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['appointment_date']._serialized_options = b'\372B\025r\0232\021\\d{4}-\\d{2}-\\d{2}'
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['appointment_start_time']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['appointment_start_time']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['appointment_end_time']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['appointment_end_time']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['confirmed_time']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['confirmed_time']._serialized_options = b'\372B\004\"\002(\000'
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['check_in_time']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['check_in_time']._serialized_options = b'\372B\004\"\002(\000'
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['check_out_time']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['check_out_time']._serialized_options = b'\372B\004\"\002(\000'
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['canceled_time']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['canceled_time']._serialized_options = b'\372B\004\"\002(\000'
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['color_code']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['color_code']._serialized_options = b'\372B\004r\002\030\024'
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['cancel_by_type']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['cancel_by_type']._serialized_options = b'\372B\004\032\002(\000'
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['cancel_by']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['cancel_by']._serialized_options = b'\372B\004\"\002(\000'
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['confirm_by_type']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['confirm_by_type']._serialized_options = b'\372B\004\032\002(\000'
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['status_before_checkin']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['status_before_checkin']._serialized_options = b'\372B\005\202\001\002\020\001'
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['status_before_ready']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['status_before_ready']._serialized_options = b'\372B\005\202\001\002\020\001'
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['status_before_finish']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['status_before_finish']._serialized_options = b'\372B\005\202\001\002\020\001'
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['status']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['status']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['ready_time']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['ready_time']._serialized_options = b'\372B\004\"\002(\000'
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['payment_status']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['payment_status']._serialized_options = b'\372B\005\202\001\002\020\001'
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['book_online_status']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['book_online_status']._serialized_options = b'\372B\005\202\001\002\020\001'
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['pet_details']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['pet_details']._serialized_options = b'\372B\005\222\001\002\010\000'
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['update_by_type']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['update_by_type']._serialized_options = b'\372B\005\202\001\002\020\001'
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['is_deprecate']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST'].fields_by_name['is_deprecate']._serialized_options = b'\030\001'
  _globals['_LISTAPPOINTMENTFORPETSREQUEST_FILTER'].fields_by_name['service_id']._loaded_options = None
  _globals['_LISTAPPOINTMENTFORPETSREQUEST_FILTER'].fields_by_name['service_id']._serialized_options = b'\372B\006\"\004 \000@\001'
  _globals['_LISTAPPOINTMENTFORPETSREQUEST_FILTER'].fields_by_name['date']._loaded_options = None
  _globals['_LISTAPPOINTMENTFORPETSREQUEST_FILTER'].fields_by_name['date']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_LISTAPPOINTMENTFORPETSREQUEST_FILTER'].fields_by_name['business_id']._loaded_options = None
  _globals['_LISTAPPOINTMENTFORPETSREQUEST_FILTER'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTAPPOINTMENTFORPETSREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_LISTAPPOINTMENTFORPETSREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTAPPOINTMENTFORPETSREQUEST'].fields_by_name['pet_ids']._loaded_options = None
  _globals['_LISTAPPOINTMENTFORPETSREQUEST'].fields_by_name['pet_ids']._serialized_options = b'\372B\017\222\001\014\010\001\020d\030\001\"\004\"\002 \000'
  _globals['_LISTAPPOINTMENTFORPETSREQUEST'].fields_by_name['filter']._loaded_options = None
  _globals['_LISTAPPOINTMENTFORPETSREQUEST'].fields_by_name['filter']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_LISTAPPOINTMENTFORPETSRESPONSE_PETIDTOAPPOINTMENTIDLISTENTRY']._loaded_options = None
  _globals['_LISTAPPOINTMENTFORPETSRESPONSE_PETIDTOAPPOINTMENTIDLISTENTRY']._serialized_options = b'8\001'
  _globals['_LISTAPPOINTMENTSFORCUSTOMERSREQUEST_FILTER'].fields_by_name['date_type']._loaded_options = None
  _globals['_LISTAPPOINTMENTSFORCUSTOMERSREQUEST_FILTER'].fields_by_name['date_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_LISTAPPOINTMENTSFORCUSTOMERSREQUEST_FILTER'].fields_by_name['statuses']._loaded_options = None
  _globals['_LISTAPPOINTMENTSFORCUSTOMERSREQUEST_FILTER'].fields_by_name['statuses']._serialized_options = b'\372B\020\222\001\r\020\n\030\001\"\007\202\001\004\020\001 \000'
  _globals['_LISTAPPOINTMENTSFORCUSTOMERSREQUEST_FILTER'].fields_by_name['service_type_includes']._loaded_options = None
  _globals['_LISTAPPOINTMENTSFORCUSTOMERSREQUEST_FILTER'].fields_by_name['service_type_includes']._serialized_options = b'\372B\007\222\001\004\010\000\030\001'
  _globals['_LISTAPPOINTMENTSFORCUSTOMERSREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_LISTAPPOINTMENTSFORCUSTOMERSREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTAPPOINTMENTSFORCUSTOMERSREQUEST'].fields_by_name['customer_ids']._loaded_options = None
  _globals['_LISTAPPOINTMENTSFORCUSTOMERSREQUEST'].fields_by_name['customer_ids']._serialized_options = b'\372B\017\222\001\014\010\001\020d\030\001\"\004\"\002 \000'
  _globals['_LISTAPPOINTMENTSFORCUSTOMERSREQUEST'].fields_by_name['filter']._loaded_options = None
  _globals['_LISTAPPOINTMENTSFORCUSTOMERSREQUEST'].fields_by_name['filter']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_CANCELAPPOINTMENTREQUEST'].fields_by_name['appointment_id']._loaded_options = None
  _globals['_CANCELAPPOINTMENTREQUEST'].fields_by_name['appointment_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CANCELAPPOINTMENTREQUEST'].fields_by_name['cancel_by_type']._loaded_options = None
  _globals['_CANCELAPPOINTMENTREQUEST'].fields_by_name['cancel_by_type']._serialized_options = b'\372B\005\202\001\002\020\001'
  _globals['_CANCELAPPOINTMENTREQUEST'].fields_by_name['cancel_by']._loaded_options = None
  _globals['_CANCELAPPOINTMENTREQUEST'].fields_by_name['cancel_by']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CANCELAPPOINTMENTREQUEST'].fields_by_name['cancel_reason']._loaded_options = None
  _globals['_CANCELAPPOINTMENTREQUEST'].fields_by_name['cancel_reason']._serialized_options = b'\372B\006r\004\030\200\200@'
  _globals['_CANCELAPPOINTMENTREQUEST'].fields_by_name['no_show']._loaded_options = None
  _globals['_CANCELAPPOINTMENTREQUEST'].fields_by_name['no_show']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_CANCELAPPOINTMENTREQUEST'].fields_by_name['repeat_appointment_modify_scope']._loaded_options = None
  _globals['_CANCELAPPOINTMENTREQUEST'].fields_by_name['repeat_appointment_modify_scope']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_BATCHBOOKAGAINAPPOINTMENTREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_BATCHBOOKAGAINAPPOINTMENTREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_BATCHBOOKAGAINAPPOINTMENTREQUEST'].fields_by_name['appointment_ids']._loaded_options = None
  _globals['_BATCHBOOKAGAINAPPOINTMENTREQUEST'].fields_by_name['appointment_ids']._serialized_options = b'\372B\016\222\001\013\010\001\020\350\007\"\004\"\002 \000'
  _globals['_BATCHBOOKAGAINAPPOINTMENTREQUEST'].fields_by_name['rebook_by']._loaded_options = None
  _globals['_BATCHBOOKAGAINAPPOINTMENTREQUEST'].fields_by_name['rebook_by']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_BATCHCANCELAPPOINTMENTREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_BATCHCANCELAPPOINTMENTREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_BATCHCANCELAPPOINTMENTREQUEST'].fields_by_name['appointment_ids']._loaded_options = None
  _globals['_BATCHCANCELAPPOINTMENTREQUEST'].fields_by_name['appointment_ids']._serialized_options = b'\372B\016\222\001\013\010\001\020\350\007\"\004\"\002 \000'
  _globals['_BATCHCANCELAPPOINTMENTREQUEST'].fields_by_name['cancel_by']._loaded_options = None
  _globals['_BATCHCANCELAPPOINTMENTREQUEST'].fields_by_name['cancel_by']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_BATCHCANCELAPPOINTMENTREQUEST'].fields_by_name['cancel_reason']._loaded_options = None
  _globals['_BATCHCANCELAPPOINTMENTREQUEST'].fields_by_name['cancel_reason']._serialized_options = b'\372B\005r\003\030\200\010'
  _globals['_COUNTAPPOINTMENTFORPETSREQUEST'].fields_by_name['pet_ids']._loaded_options = None
  _globals['_COUNTAPPOINTMENTFORPETSREQUEST'].fields_by_name['pet_ids']._serialized_options = b'\372B\014\222\001\t\020\220N\"\004\"\002 \000'
  _globals['_COUNTAPPOINTMENTFORPETSRESPONSE_PETIDTOAPPOINTMENTCOUNTENTRY']._loaded_options = None
  _globals['_COUNTAPPOINTMENTFORPETSRESPONSE_PETIDTOAPPOINTMENTCOUNTENTRY']._serialized_options = b'8\001'
  _globals['_DELETEAPPOINTMENTSREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_DELETEAPPOINTMENTSREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_DELETEAPPOINTMENTSREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_DELETEAPPOINTMENTSREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_DELETEAPPOINTMENTSREQUEST'].fields_by_name['ids']._loaded_options = None
  _globals['_DELETEAPPOINTMENTSREQUEST'].fields_by_name['ids']._serialized_options = b'\372B\014\222\001\t\020\220N\"\004\"\002 \000'
  _globals['_RESTOREAPPOINTMENTSREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_RESTOREAPPOINTMENTSREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_RESTOREAPPOINTMENTSREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_RESTOREAPPOINTMENTSREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_RESTOREAPPOINTMENTSREQUEST'].fields_by_name['ids']._loaded_options = None
  _globals['_RESTOREAPPOINTMENTSREQUEST'].fields_by_name['ids']._serialized_options = b'\372B\014\222\001\t\020\220N\"\004\"\002 \000'
  _globals['_RESCHEDULEBOARDINGAPPOINTMENTREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_RESCHEDULEBOARDINGAPPOINTMENTREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_RESCHEDULEBOARDINGAPPOINTMENTREQUEST'].fields_by_name['appointment_id']._loaded_options = None
  _globals['_RESCHEDULEBOARDINGAPPOINTMENTREQUEST'].fields_by_name['appointment_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_RESCHEDULEBOARDINGAPPOINTMENTREQUEST'].fields_by_name['start_date']._loaded_options = None
  _globals['_RESCHEDULEBOARDINGAPPOINTMENTREQUEST'].fields_by_name['start_date']._serialized_options = b'\372B\025r\0232\021\\d{4}-\\d{2}-\\d{2}'
  _globals['_RESCHEDULEBOARDINGAPPOINTMENTREQUEST'].fields_by_name['end_date']._loaded_options = None
  _globals['_RESCHEDULEBOARDINGAPPOINTMENTREQUEST'].fields_by_name['end_date']._serialized_options = b'\372B\025r\0232\021\\d{4}-\\d{2}-\\d{2}'
  _globals['_SYNCAPPOINTMENTTOORDERREQUEST'].fields_by_name['appointment_id']._loaded_options = None
  _globals['_SYNCAPPOINTMENTTOORDERREQUEST'].fields_by_name['appointment_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_PREVIEWORDERDETAILREQUEST'].fields_by_name['appointment_ids']._loaded_options = None
  _globals['_PREVIEWORDERDETAILREQUEST'].fields_by_name['appointment_ids']._serialized_options = b'\372B\016\222\001\013\010\001\020\350\007\"\004\"\002 \000'
  _globals['_PREVIEWORDERLINEITEMSREQUEST'].fields_by_name['appointment_id']._loaded_options = None
  _globals['_PREVIEWORDERLINEITEMSREQUEST'].fields_by_name['appointment_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETTIMEOVERLAPAPPOINTMENTLISTREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_GETTIMEOVERLAPAPPOINTMENTLISTREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETTIMEOVERLAPAPPOINTMENTLISTREQUEST'].fields_by_name['customer_id']._loaded_options = None
  _globals['_GETTIMEOVERLAPAPPOINTMENTLISTREQUEST'].fields_by_name['customer_id']._serialized_options = b'\372B\t\222\001\006\"\004\"\002 \000'
  _globals['_GETTIMEOVERLAPAPPOINTMENTLISTREQUEST'].fields_by_name['pet_ids']._loaded_options = None
  _globals['_GETTIMEOVERLAPAPPOINTMENTLISTREQUEST'].fields_by_name['pet_ids']._serialized_options = b'\372B\t\222\001\006\"\004\"\002 \000'
  _globals['_GETTIMEOVERLAPAPPOINTMENTLISTRESPONSE_APPOINTMENTLISTENTRY']._loaded_options = None
  _globals['_GETTIMEOVERLAPAPPOINTMENTLISTRESPONSE_APPOINTMENTLISTENTRY']._serialized_options = b'8\001'
  _globals['_PREVIEWESTIMATEORDERREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_PREVIEWESTIMATEORDERREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_PREVIEWESTIMATEORDERREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_PREVIEWESTIMATEORDERREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_PREVIEWESTIMATEORDERREQUEST'].fields_by_name['appointment_ids']._loaded_options = None
  _globals['_PREVIEWESTIMATEORDERREQUEST'].fields_by_name['appointment_ids']._serialized_options = b'\372B\016\222\001\013\010\001\020\350\007\"\004\"\002 \000'
  _globals['_LISTEXTRAINFOREQUEST'].fields_by_name['appointment_ids']._loaded_options = None
  _globals['_LISTEXTRAINFOREQUEST'].fields_by_name['appointment_ids']._serialized_options = b'\372B\016\222\001\013\010\001\020\350\007\"\004\"\002 \000'
  _globals['_APPOINTMENTDATETYPE']._serialized_start=17197
  _globals['_APPOINTMENTDATETYPE']._serialized_end=17328
  _globals['_CREATEAPPOINTMENTREQUEST']._serialized_start=1062
  _globals['_CREATEAPPOINTMENTREQUEST']._serialized_end=1669
  _globals['_CREATEAPPOINTMENTRESPONSE']._serialized_start=1671
  _globals['_CREATEAPPOINTMENTRESPONSE']._serialized_end=1737
  _globals['_UPDATEAPPOINTMENTREQUEST']._serialized_start=1740
  _globals['_UPDATEAPPOINTMENTREQUEST']._serialized_end=2027
  _globals['_UPDATEAPPOINTMENTRESPONSE']._serialized_start=2029
  _globals['_UPDATEAPPOINTMENTRESPONSE']._serialized_end=2056
  _globals['_GETAPPOINTMENTREQUEST']._serialized_start=2059
  _globals['_GETAPPOINTMENTREQUEST']._serialized_end=2216
  _globals['_GETAPPOINTMENTRESPONSE']._serialized_start=2218
  _globals['_GETAPPOINTMENTRESPONSE']._serialized_end=2323
  _globals['_GETAPPOINTMENTLISTREQUEST']._serialized_start=2326
  _globals['_GETAPPOINTMENTLISTREQUEST']._serialized_end=2490
  _globals['_GETAPPOINTMENTLISTRESPONSE']._serialized_start=2492
  _globals['_GETAPPOINTMENTLISTRESPONSE']._serialized_end=2603
  _globals['_GETCUSTOMERLASTAPPOINTMENTREQUEST']._serialized_start=2606
  _globals['_GETCUSTOMERLASTAPPOINTMENTREQUEST']._serialized_end=3369
  _globals['_GETCUSTOMERLASTAPPOINTMENTREQUEST_FILTER']._serialized_start=2909
  _globals['_GETCUSTOMERLASTAPPOINTMENTREQUEST_FILTER']._serialized_end=3347
  _globals['_GETCUSTOMERLASTAPPOINTMENTRESPONSE']._serialized_start=3372
  _globals['_GETCUSTOMERLASTAPPOINTMENTRESPONSE']._serialized_end=3687
  _globals['_GETCUSTOMERLASTAPPOINTMENTRESPONSE_CUSTOMERLASTAPPOINTMENTENTRY']._serialized_start=3566
  _globals['_GETCUSTOMERLASTAPPOINTMENTRESPONSE_CUSTOMERLASTAPPOINTMENTENTRY']._serialized_end=3687
  _globals['_CALCULATEAPPOINTMENTINVOICEREQUEST']._serialized_start=3690
  _globals['_CALCULATEAPPOINTMENTINVOICEREQUEST']._serialized_end=3939
  _globals['_CALCULATEAPPOINTMENTINVOICERESPONSE']._serialized_start=3941
  _globals['_CALCULATEAPPOINTMENTINVOICERESPONSE']._serialized_end=4033
  _globals['_GETINPROGRESSAPPOINTMENTREQUEST']._serialized_start=4036
  _globals['_GETINPROGRESSAPPOINTMENTREQUEST']._serialized_end=4370
  _globals['_GETINPROGRESSAPPOINTMENTRESPONSE']._serialized_start=4372
  _globals['_GETINPROGRESSAPPOINTMENTRESPONSE']._serialized_end=4469
  _globals['_CREATEBLOCKREQUEST']._serialized_start=4472
  _globals['_CREATEBLOCKREQUEST']._serialized_end=4942
  _globals['_CREATEBLOCKRESPONSE']._serialized_start=4944
  _globals['_CREATEBLOCKRESPONSE']._serialized_end=4981
  _globals['_LISTAPPOINTMENTSREQUEST']._serialized_start=4984
  _globals['_LISTAPPOINTMENTSREQUEST']._serialized_end=7045
  _globals['_LISTAPPOINTMENTSREQUEST_FILTER']._serialized_start=5456
  _globals['_LISTAPPOINTMENTSREQUEST_FILTER']._serialized_end=6941
  _globals['_LISTAPPOINTMENTSREQUEST_PRIORITYORDERTYPE']._serialized_start=6943
  _globals['_LISTAPPOINTMENTSREQUEST_PRIORITYORDERTYPE']._serialized_end=7006
  _globals['_LISTAPPOINTMENTSRESPONSE']._serialized_start=7048
  _globals['_LISTAPPOINTMENTSRESPONSE']._serialized_end=7245
  _globals['_CREATEAPPOINTMENTFORONLINEBOOKINGREQUEST']._serialized_start=7248
  _globals['_CREATEAPPOINTMENTFORONLINEBOOKINGREQUEST']._serialized_end=7837
  _globals['_CREATEAPPOINTMENTFORONLINEBOOKINGRESPONSE']._serialized_start=7839
  _globals['_CREATEAPPOINTMENTFORONLINEBOOKINGRESPONSE']._serialized_end=7921
  _globals['_LISTBLOCKTIMESREQUEST']._serialized_start=7924
  _globals['_LISTBLOCKTIMESREQUEST']._serialized_end=8261
  _globals['_LISTBLOCKTIMESREQUEST_FILTER']._serialized_start=5456
  _globals['_LISTBLOCKTIMESREQUEST_FILTER']._serialized_end=5590
  _globals['_LISTBLOCKTIMESRESPONSE']._serialized_start=8263
  _globals['_LISTBLOCKTIMESRESPONSE']._serialized_end=8365
  _globals['_BATCHQUICKCHECKINREQUEST']._serialized_start=8368
  _globals['_BATCHQUICKCHECKINREQUEST']._serialized_end=8730
  _globals['_BATCHQUICKCHECKINRESPONSE']._serialized_start=8733
  _globals['_BATCHQUICKCHECKINRESPONSE']._serialized_end=8872
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST']._serialized_start=8875
  _globals['_UPDATEAPPOINTMENTSELECTIVEREQUEST']._serialized_end=11074
  _globals['_UPDATEAPPOINTMENTSELECTIVERESPONSE']._serialized_start=11076
  _globals['_UPDATEAPPOINTMENTSELECTIVERESPONSE']._serialized_end=11149
  _globals['_LISTAPPOINTMENTFORPETSREQUEST']._serialized_start=11152
  _globals['_LISTAPPOINTMENTFORPETSREQUEST']._serialized_end=11611
  _globals['_LISTAPPOINTMENTFORPETSREQUEST_FILTER']._serialized_start=11373
  _globals['_LISTAPPOINTMENTFORPETSREQUEST_FILTER']._serialized_end=11611
  _globals['_LISTAPPOINTMENTFORPETSRESPONSE']._serialized_start=11614
  _globals['_LISTAPPOINTMENTFORPETSRESPONSE']._serialized_end=11996
  _globals['_LISTAPPOINTMENTFORPETSRESPONSE_PETIDTOAPPOINTMENTIDLISTENTRY']._serialized_start=11889
  _globals['_LISTAPPOINTMENTFORPETSRESPONSE_PETIDTOAPPOINTMENTIDLISTENTRY']._serialized_end=11996
  _globals['_LISTAPPOINTMENTSFORCUSTOMERSREQUEST']._serialized_start=11999
  _globals['_LISTAPPOINTMENTSFORCUSTOMERSREQUEST']._serialized_end=12503
  _globals['_LISTAPPOINTMENTSFORCUSTOMERSREQUEST_FILTER']._serialized_start=12242
  _globals['_LISTAPPOINTMENTSFORCUSTOMERSREQUEST_FILTER']._serialized_end=12503
  _globals['_LISTAPPOINTMENTSFORCUSTOMERSRESPONSE']._serialized_start=12505
  _globals['_LISTAPPOINTMENTSFORCUSTOMERSRESPONSE']._serialized_end=12626
  _globals['_CANCELAPPOINTMENTREQUEST']._serialized_start=12629
  _globals['_CANCELAPPOINTMENTREQUEST']._serialized_end=13186
  _globals['_CANCELAPPOINTMENTRESPONSE']._serialized_start=13188
  _globals['_CANCELAPPOINTMENTRESPONSE']._serialized_end=13215
  _globals['_BATCHBOOKAGAINAPPOINTMENTREQUEST']._serialized_start=13218
  _globals['_BATCHBOOKAGAINAPPOINTMENTREQUEST']._serialized_end=13442
  _globals['_BATCHBOOKAGAINAPPOINTMENTRESPONSE']._serialized_start=13444
  _globals['_BATCHBOOKAGAINAPPOINTMENTRESPONSE']._serialized_end=13562
  _globals['_BATCHCANCELAPPOINTMENTREQUEST']._serialized_start=13565
  _globals['_BATCHCANCELAPPOINTMENTREQUEST']._serialized_end=13804
  _globals['_BATCHCANCELAPPOINTMENTRESPONSE']._serialized_start=13806
  _globals['_BATCHCANCELAPPOINTMENTRESPONSE']._serialized_end=13921
  _globals['_COUNTAPPOINTMENTFORPETSREQUEST']._serialized_start=13923
  _globals['_COUNTAPPOINTMENTFORPETSREQUEST']._serialized_end=13997
  _globals['_COUNTAPPOINTMENTFORPETSRESPONSE']._serialized_start=14000
  _globals['_COUNTAPPOINTMENTFORPETSRESPONSE']._serialized_end=14264
  _globals['_COUNTAPPOINTMENTFORPETSRESPONSE_PETIDTOAPPOINTMENTCOUNTENTRY']._serialized_start=14190
  _globals['_COUNTAPPOINTMENTFORPETSRESPONSE_PETIDTOAPPOINTMENTCOUNTENTRY']._serialized_end=14264
  _globals['_DELETEAPPOINTMENTSREQUEST']._serialized_start=14267
  _globals['_DELETEAPPOINTMENTSREQUEST']._serialized_end=14452
  _globals['_DELETEAPPOINTMENTSRESPONSE']._serialized_start=14454
  _globals['_DELETEAPPOINTMENTSRESPONSE']._serialized_end=14519
  _globals['_RESTOREAPPOINTMENTSREQUEST']._serialized_start=14522
  _globals['_RESTOREAPPOINTMENTSREQUEST']._serialized_end=14708
  _globals['_RESTOREAPPOINTMENTSRESPONSE']._serialized_start=14710
  _globals['_RESTOREAPPOINTMENTSRESPONSE']._serialized_end=14776
  _globals['_RESCHEDULEBOARDINGAPPOINTMENTREQUEST']._serialized_start=14779
  _globals['_RESCHEDULEBOARDINGAPPOINTMENTREQUEST']._serialized_end=15073
  _globals['_SYNCAPPOINTMENTTOORDERREQUEST']._serialized_start=15075
  _globals['_SYNCAPPOINTMENTTOORDERREQUEST']._serialized_end=15154
  _globals['_SYNCAPPOINTMENTTOORDERRESPONSE']._serialized_start=15156
  _globals['_SYNCAPPOINTMENTTOORDERRESPONSE']._serialized_end=15188
  _globals['_RESCHEDULEBOARDINGAPPOINTMENTRESPONSE']._serialized_start=15190
  _globals['_RESCHEDULEBOARDINGAPPOINTMENTRESPONSE']._serialized_end=15229
  _globals['_PREVIEWORDERDETAILREQUEST']._serialized_start=15231
  _globals['_PREVIEWORDERDETAILREQUEST']._serialized_end=15318
  _globals['_PREVIEWORDERDETAILRESPONSE']._serialized_start=15320
  _globals['_PREVIEWORDERDETAILRESPONSE']._serialized_end=15426
  _globals['_PREVIEWORDERLINEITEMSREQUEST']._serialized_start=15428
  _globals['_PREVIEWORDERLINEITEMSREQUEST']._serialized_end=15506
  _globals['_PREVIEWORDERLINEITEMSRESPONSE']._serialized_start=15509
  _globals['_PREVIEWORDERLINEITEMSRESPONSE']._serialized_end=15692
  _globals['_GETTIMEOVERLAPAPPOINTMENTLISTREQUEST']._serialized_start=15695
  _globals['_GETTIMEOVERLAPAPPOINTMENTLISTREQUEST']._serialized_end=16022
  _globals['_GETTIMEOVERLAPAPPOINTMENTLISTREQUEST_FILTER']._serialized_start=15960
  _globals['_GETTIMEOVERLAPAPPOINTMENTLISTREQUEST_FILTER']._serialized_end=16022
  _globals['_GETTIMEOVERLAPAPPOINTMENTLISTRESPONSE']._serialized_start=16025
  _globals['_GETTIMEOVERLAPAPPOINTMENTLISTRESPONSE']._serialized_end=16313
  _globals['_GETTIMEOVERLAPAPPOINTMENTLISTRESPONSE_APPOINTMENTLISTENTRY']._serialized_start=16200
  _globals['_GETTIMEOVERLAPAPPOINTMENTLISTRESPONSE_APPOINTMENTLISTENTRY']._serialized_end=16313
  _globals['_APPOINTMENTLIST']._serialized_start=16315
  _globals['_APPOINTMENTLIST']._serialized_end=16415
  _globals['_PREVIEWESTIMATEORDERREQUEST']._serialized_start=16418
  _globals['_PREVIEWESTIMATEORDERREQUEST']._serialized_end=16589
  _globals['_PREVIEWESTIMATEORDERRESPONSE']._serialized_start=16592
  _globals['_PREVIEWESTIMATEORDERRESPONSE']._serialized_end=16998
  _globals['_PREVIEWESTIMATEORDERRESPONSE_ESTIMATEDORDER']._serialized_start=16743
  _globals['_PREVIEWESTIMATEORDERRESPONSE_ESTIMATEDORDER']._serialized_end=16998
  _globals['_LISTEXTRAINFOREQUEST']._serialized_start=17000
  _globals['_LISTEXTRAINFOREQUEST']._serialized_end=17082
  _globals['_LISTEXTRAINFORESPONSE']._serialized_start=17084
  _globals['_LISTEXTRAINFORESPONSE']._serialized_end=17194
  _globals['_APPOINTMENTSERVICE']._serialized_start=17331
  _globals['_APPOINTMENTSERVICE']._serialized_end=21457
# @@protoc_insertion_point(module_scope)

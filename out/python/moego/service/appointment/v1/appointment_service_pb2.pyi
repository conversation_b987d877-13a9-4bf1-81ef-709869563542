from google.protobuf import timestamp_pb2 as _timestamp_pb2
from google.type import date_pb2 as _date_pb2
from google.type import interval_pb2 as _interval_pb2
from google.type import money_pb2 as _money_pb2
from moego.models.appointment.v1 import appointment_defs_pb2 as _appointment_defs_pb2
from moego.models.appointment.v1 import appointment_enums_pb2 as _appointment_enums_pb2
from moego.models.appointment.v1 import appointment_models_pb2 as _appointment_models_pb2
from moego.models.appointment.v1 import appointment_note_defs_pb2 as _appointment_note_defs_pb2
from moego.models.appointment.v1 import appointment_extra_info_models_pb2 as _appointment_extra_info_models_pb2
from moego.models.appointment.v1 import block_time_models_pb2 as _block_time_models_pb2
from moego.models.appointment.v1 import pet_detail_defs_pb2 as _pet_detail_defs_pb2
from moego.models.appointment.v1 import pet_detail_enums_pb2 as _pet_detail_enums_pb2
from moego.models.appointment.v1 import wait_list_enums_pb2 as _wait_list_enums_pb2
from moego.models.fulfillment.v1 import fulfillment_defs_pb2 as _fulfillment_defs_pb2
from moego.models.offering.v1 import service_enum_pb2 as _service_enum_pb2
from moego.models.order.v1 import order_detail_defs_pb2 as _order_detail_defs_pb2
from moego.models.order.v1 import order_detail_models_pb2 as _order_detail_models_pb2
from moego.models.payment.v1 import pre_auth_defs_pb2 as _pre_auth_defs_pb2
from moego.utils.v1 import struct_pb2 as _struct_pb2
from moego.utils.v2 import condition_messages_pb2 as _condition_messages_pb2
from moego.utils.v2 import pagination_messages_pb2 as _pagination_messages_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class AppointmentDateType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    APPOINTMENT_DATE_TYPE_UNSPECIFIED: _ClassVar[AppointmentDateType]
    LAST: _ClassVar[AppointmentDateType]
    NEXT: _ClassVar[AppointmentDateType]
    TODAY: _ClassVar[AppointmentDateType]
    NEXT_NOT_END: _ClassVar[AppointmentDateType]
    NEXT_AFTER_TODAY: _ClassVar[AppointmentDateType]
APPOINTMENT_DATE_TYPE_UNSPECIFIED: AppointmentDateType
LAST: AppointmentDateType
NEXT: AppointmentDateType
TODAY: AppointmentDateType
NEXT_NOT_END: AppointmentDateType
NEXT_AFTER_TODAY: AppointmentDateType

class CreateAppointmentRequest(_message.Message):
    __slots__ = ("appointment", "pet_details", "pre_auth", "notes", "company_id", "business_id", "staff_id", "created_at")
    APPOINTMENT_FIELD_NUMBER: _ClassVar[int]
    PET_DETAILS_FIELD_NUMBER: _ClassVar[int]
    PRE_AUTH_FIELD_NUMBER: _ClassVar[int]
    NOTES_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    CREATED_AT_FIELD_NUMBER: _ClassVar[int]
    appointment: _appointment_defs_pb2.AppointmentCreateDef
    pet_details: _containers.RepeatedCompositeFieldContainer[_pet_detail_defs_pb2.PetDetailDef]
    pre_auth: _pre_auth_defs_pb2.PreAuthEnableDef
    notes: _containers.RepeatedCompositeFieldContainer[_appointment_note_defs_pb2.AppointmentNoteCreateDef]
    company_id: int
    business_id: int
    staff_id: int
    created_at: _timestamp_pb2.Timestamp
    def __init__(self, appointment: _Optional[_Union[_appointment_defs_pb2.AppointmentCreateDef, _Mapping]] = ..., pet_details: _Optional[_Iterable[_Union[_pet_detail_defs_pb2.PetDetailDef, _Mapping]]] = ..., pre_auth: _Optional[_Union[_pre_auth_defs_pb2.PreAuthEnableDef, _Mapping]] = ..., notes: _Optional[_Iterable[_Union[_appointment_note_defs_pb2.AppointmentNoteCreateDef, _Mapping]]] = ..., company_id: _Optional[int] = ..., business_id: _Optional[int] = ..., staff_id: _Optional[int] = ..., created_at: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ...) -> None: ...

class CreateAppointmentResponse(_message.Message):
    __slots__ = ("appointment_id",)
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    appointment_id: int
    def __init__(self, appointment_id: _Optional[int] = ...) -> None: ...

class UpdateAppointmentRequest(_message.Message):
    __slots__ = ("appointment_id", "appointment", "company_id", "business_id", "staff_id")
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    appointment_id: int
    appointment: _appointment_defs_pb2.AppointmentUpdateDef
    company_id: int
    business_id: int
    staff_id: int
    def __init__(self, appointment_id: _Optional[int] = ..., appointment: _Optional[_Union[_appointment_defs_pb2.AppointmentUpdateDef, _Mapping]] = ..., company_id: _Optional[int] = ..., business_id: _Optional[int] = ..., staff_id: _Optional[int] = ...) -> None: ...

class UpdateAppointmentResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class GetAppointmentRequest(_message.Message):
    __slots__ = ("appointment_id", "company_id", "business_id")
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    appointment_id: int
    company_id: int
    business_id: int
    def __init__(self, appointment_id: _Optional[int] = ..., company_id: _Optional[int] = ..., business_id: _Optional[int] = ...) -> None: ...

class GetAppointmentResponse(_message.Message):
    __slots__ = ("appointment",)
    APPOINTMENT_FIELD_NUMBER: _ClassVar[int]
    appointment: _appointment_models_pb2.AppointmentModel
    def __init__(self, appointment: _Optional[_Union[_appointment_models_pb2.AppointmentModel, _Mapping]] = ...) -> None: ...

class GetAppointmentListRequest(_message.Message):
    __slots__ = ("appointment_id", "company_id", "business_id")
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    appointment_id: _containers.RepeatedScalarFieldContainer[int]
    company_id: int
    business_id: int
    def __init__(self, appointment_id: _Optional[_Iterable[int]] = ..., company_id: _Optional[int] = ..., business_id: _Optional[int] = ...) -> None: ...

class GetAppointmentListResponse(_message.Message):
    __slots__ = ("appointments",)
    APPOINTMENTS_FIELD_NUMBER: _ClassVar[int]
    appointments: _containers.RepeatedCompositeFieldContainer[_appointment_models_pb2.AppointmentModel]
    def __init__(self, appointments: _Optional[_Iterable[_Union[_appointment_models_pb2.AppointmentModel, _Mapping]]] = ...) -> None: ...

class GetCustomerLastAppointmentRequest(_message.Message):
    __slots__ = ("company_id", "customer_id", "status", "filter")
    class Filter(_message.Message):
        __slots__ = ("start_time_range", "end_time_range", "service_item_types", "filter_no_start_time", "filter_booking_request")
        START_TIME_RANGE_FIELD_NUMBER: _ClassVar[int]
        END_TIME_RANGE_FIELD_NUMBER: _ClassVar[int]
        SERVICE_ITEM_TYPES_FIELD_NUMBER: _ClassVar[int]
        FILTER_NO_START_TIME_FIELD_NUMBER: _ClassVar[int]
        FILTER_BOOKING_REQUEST_FIELD_NUMBER: _ClassVar[int]
        start_time_range: _interval_pb2.Interval
        end_time_range: _interval_pb2.Interval
        service_item_types: _containers.RepeatedScalarFieldContainer[_service_enum_pb2.ServiceItemType]
        filter_no_start_time: bool
        filter_booking_request: bool
        def __init__(self, start_time_range: _Optional[_Union[_interval_pb2.Interval, _Mapping]] = ..., end_time_range: _Optional[_Union[_interval_pb2.Interval, _Mapping]] = ..., service_item_types: _Optional[_Iterable[_Union[_service_enum_pb2.ServiceItemType, str]]] = ..., filter_no_start_time: bool = ..., filter_booking_request: bool = ...) -> None: ...
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    FILTER_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    customer_id: _containers.RepeatedScalarFieldContainer[int]
    status: _appointment_enums_pb2.AppointmentStatus
    filter: GetCustomerLastAppointmentRequest.Filter
    def __init__(self, company_id: _Optional[int] = ..., customer_id: _Optional[_Iterable[int]] = ..., status: _Optional[_Union[_appointment_enums_pb2.AppointmentStatus, str]] = ..., filter: _Optional[_Union[GetCustomerLastAppointmentRequest.Filter, _Mapping]] = ...) -> None: ...

class GetCustomerLastAppointmentResponse(_message.Message):
    __slots__ = ("customer_last_appointment",)
    class CustomerLastAppointmentEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: int
        value: _appointment_models_pb2.AppointmentModel
        def __init__(self, key: _Optional[int] = ..., value: _Optional[_Union[_appointment_models_pb2.AppointmentModel, _Mapping]] = ...) -> None: ...
    CUSTOMER_LAST_APPOINTMENT_FIELD_NUMBER: _ClassVar[int]
    customer_last_appointment: _containers.MessageMap[int, _appointment_models_pb2.AppointmentModel]
    def __init__(self, customer_last_appointment: _Optional[_Mapping[int, _appointment_models_pb2.AppointmentModel]] = ...) -> None: ...

class CalculateAppointmentInvoiceRequest(_message.Message):
    __slots__ = ("pet_details", "company_id", "business_id", "staff_id")
    PET_DETAILS_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    pet_details: _containers.RepeatedCompositeFieldContainer[_pet_detail_defs_pb2.PetDetailDef]
    company_id: int
    business_id: int
    staff_id: int
    def __init__(self, pet_details: _Optional[_Iterable[_Union[_pet_detail_defs_pb2.PetDetailDef, _Mapping]]] = ..., company_id: _Optional[int] = ..., business_id: _Optional[int] = ..., staff_id: _Optional[int] = ...) -> None: ...

class CalculateAppointmentInvoiceResponse(_message.Message):
    __slots__ = ("order",)
    ORDER_FIELD_NUMBER: _ClassVar[int]
    order: _order_detail_defs_pb2.OrderDef
    def __init__(self, order: _Optional[_Union[_order_detail_defs_pb2.OrderDef, _Mapping]] = ...) -> None: ...

class GetInProgressAppointmentRequest(_message.Message):
    __slots__ = ("company_id", "service_item_type", "business_id", "customer_id", "pet_id")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    service_item_type: _service_enum_pb2.ServiceItemType
    business_id: int
    customer_id: int
    pet_id: int
    def __init__(self, company_id: _Optional[int] = ..., service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., business_id: _Optional[int] = ..., customer_id: _Optional[int] = ..., pet_id: _Optional[int] = ...) -> None: ...

class GetInProgressAppointmentResponse(_message.Message):
    __slots__ = ("appointment_id",)
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    appointment_id: int
    def __init__(self, appointment_id: _Optional[int] = ...) -> None: ...

class CreateBlockRequest(_message.Message):
    __slots__ = ("business_id", "staff_id", "company_id", "created_by", "start_time", "end_time", "color_code", "description", "source")
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    CREATED_BY_FIELD_NUMBER: _ClassVar[int]
    START_TIME_FIELD_NUMBER: _ClassVar[int]
    END_TIME_FIELD_NUMBER: _ClassVar[int]
    COLOR_CODE_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    SOURCE_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    staff_id: int
    company_id: int
    created_by: int
    start_time: _timestamp_pb2.Timestamp
    end_time: _timestamp_pb2.Timestamp
    color_code: str
    description: str
    source: _appointment_enums_pb2.AppointmentSource
    def __init__(self, business_id: _Optional[int] = ..., staff_id: _Optional[int] = ..., company_id: _Optional[int] = ..., created_by: _Optional[int] = ..., start_time: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., end_time: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., color_code: _Optional[str] = ..., description: _Optional[str] = ..., source: _Optional[_Union[_appointment_enums_pb2.AppointmentSource, str]] = ...) -> None: ...

class CreateBlockResponse(_message.Message):
    __slots__ = ("id",)
    ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    def __init__(self, id: _Optional[int] = ...) -> None: ...

class ListAppointmentsRequest(_message.Message):
    __slots__ = ("pagination", "company_id", "business_ids", "filter", "order_bys", "priority_order_type")
    class PriorityOrderType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        UNSPECIFIED: _ClassVar[ListAppointmentsRequest.PriorityOrderType]
        UNEXPIRED_UNCONFIRMED: _ClassVar[ListAppointmentsRequest.PriorityOrderType]
    UNSPECIFIED: ListAppointmentsRequest.PriorityOrderType
    UNEXPIRED_UNCONFIRMED: ListAppointmentsRequest.PriorityOrderType
    class Filter(_message.Message):
        __slots__ = ("start_time_range", "end_time_range", "last_updated_time_range", "status", "wait_list_statuses", "filter_no_start_time", "filter_booking_request", "service_type_includes", "customer_ids", "date_type", "staff_ids", "appointment_date", "include_service_operation", "pet_ids", "service_ids", "include_block", "is_waiting_list", "check_in_time_range", "check_out_time_range", "payment_statuses")
        START_TIME_RANGE_FIELD_NUMBER: _ClassVar[int]
        END_TIME_RANGE_FIELD_NUMBER: _ClassVar[int]
        LAST_UPDATED_TIME_RANGE_FIELD_NUMBER: _ClassVar[int]
        STATUS_FIELD_NUMBER: _ClassVar[int]
        WAIT_LIST_STATUSES_FIELD_NUMBER: _ClassVar[int]
        FILTER_NO_START_TIME_FIELD_NUMBER: _ClassVar[int]
        FILTER_BOOKING_REQUEST_FIELD_NUMBER: _ClassVar[int]
        SERVICE_TYPE_INCLUDES_FIELD_NUMBER: _ClassVar[int]
        CUSTOMER_IDS_FIELD_NUMBER: _ClassVar[int]
        DATE_TYPE_FIELD_NUMBER: _ClassVar[int]
        STAFF_IDS_FIELD_NUMBER: _ClassVar[int]
        APPOINTMENT_DATE_FIELD_NUMBER: _ClassVar[int]
        INCLUDE_SERVICE_OPERATION_FIELD_NUMBER: _ClassVar[int]
        PET_IDS_FIELD_NUMBER: _ClassVar[int]
        SERVICE_IDS_FIELD_NUMBER: _ClassVar[int]
        INCLUDE_BLOCK_FIELD_NUMBER: _ClassVar[int]
        IS_WAITING_LIST_FIELD_NUMBER: _ClassVar[int]
        CHECK_IN_TIME_RANGE_FIELD_NUMBER: _ClassVar[int]
        CHECK_OUT_TIME_RANGE_FIELD_NUMBER: _ClassVar[int]
        PAYMENT_STATUSES_FIELD_NUMBER: _ClassVar[int]
        start_time_range: _interval_pb2.Interval
        end_time_range: _interval_pb2.Interval
        last_updated_time_range: _interval_pb2.Interval
        status: _containers.RepeatedScalarFieldContainer[_appointment_enums_pb2.AppointmentStatus]
        wait_list_statuses: _containers.RepeatedScalarFieldContainer[_wait_list_enums_pb2.WaitListStatus]
        filter_no_start_time: bool
        filter_booking_request: bool
        service_type_includes: _containers.RepeatedScalarFieldContainer[int]
        customer_ids: _containers.RepeatedScalarFieldContainer[int]
        date_type: AppointmentDateType
        staff_ids: _containers.RepeatedScalarFieldContainer[int]
        appointment_date: _date_pb2.Date
        include_service_operation: bool
        pet_ids: _containers.RepeatedScalarFieldContainer[int]
        service_ids: _containers.RepeatedScalarFieldContainer[int]
        include_block: bool
        is_waiting_list: bool
        check_in_time_range: _interval_pb2.Interval
        check_out_time_range: _interval_pb2.Interval
        payment_statuses: _containers.RepeatedScalarFieldContainer[_appointment_enums_pb2.AppointmentPaymentStatus]
        def __init__(self, start_time_range: _Optional[_Union[_interval_pb2.Interval, _Mapping]] = ..., end_time_range: _Optional[_Union[_interval_pb2.Interval, _Mapping]] = ..., last_updated_time_range: _Optional[_Union[_interval_pb2.Interval, _Mapping]] = ..., status: _Optional[_Iterable[_Union[_appointment_enums_pb2.AppointmentStatus, str]]] = ..., wait_list_statuses: _Optional[_Iterable[_Union[_wait_list_enums_pb2.WaitListStatus, str]]] = ..., filter_no_start_time: bool = ..., filter_booking_request: bool = ..., service_type_includes: _Optional[_Iterable[int]] = ..., customer_ids: _Optional[_Iterable[int]] = ..., date_type: _Optional[_Union[AppointmentDateType, str]] = ..., staff_ids: _Optional[_Iterable[int]] = ..., appointment_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., include_service_operation: bool = ..., pet_ids: _Optional[_Iterable[int]] = ..., service_ids: _Optional[_Iterable[int]] = ..., include_block: bool = ..., is_waiting_list: bool = ..., check_in_time_range: _Optional[_Union[_interval_pb2.Interval, _Mapping]] = ..., check_out_time_range: _Optional[_Union[_interval_pb2.Interval, _Mapping]] = ..., payment_statuses: _Optional[_Iterable[_Union[_appointment_enums_pb2.AppointmentPaymentStatus, str]]] = ...) -> None: ...
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_IDS_FIELD_NUMBER: _ClassVar[int]
    FILTER_FIELD_NUMBER: _ClassVar[int]
    ORDER_BYS_FIELD_NUMBER: _ClassVar[int]
    PRIORITY_ORDER_TYPE_FIELD_NUMBER: _ClassVar[int]
    pagination: _pagination_messages_pb2.PaginationRequest
    company_id: int
    business_ids: _containers.RepeatedScalarFieldContainer[int]
    filter: ListAppointmentsRequest.Filter
    order_bys: _containers.RepeatedCompositeFieldContainer[_condition_messages_pb2.OrderBy]
    priority_order_type: ListAppointmentsRequest.PriorityOrderType
    def __init__(self, pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ..., company_id: _Optional[int] = ..., business_ids: _Optional[_Iterable[int]] = ..., filter: _Optional[_Union[ListAppointmentsRequest.Filter, _Mapping]] = ..., order_bys: _Optional[_Iterable[_Union[_condition_messages_pb2.OrderBy, _Mapping]]] = ..., priority_order_type: _Optional[_Union[ListAppointmentsRequest.PriorityOrderType, str]] = ...) -> None: ...

class ListAppointmentsResponse(_message.Message):
    __slots__ = ("pagination", "appointments")
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENTS_FIELD_NUMBER: _ClassVar[int]
    pagination: _pagination_messages_pb2.PaginationResponse
    appointments: _containers.RepeatedCompositeFieldContainer[_appointment_models_pb2.AppointmentModel]
    def __init__(self, pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ..., appointments: _Optional[_Iterable[_Union[_appointment_models_pb2.AppointmentModel, _Mapping]]] = ...) -> None: ...

class CreateAppointmentForOnlineBookingRequest(_message.Message):
    __slots__ = ("appointment", "pet_details", "notes", "company_id", "business_id", "staff_id", "booking_request_id", "booking_request_identifier")
    APPOINTMENT_FIELD_NUMBER: _ClassVar[int]
    PET_DETAILS_FIELD_NUMBER: _ClassVar[int]
    NOTES_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    BOOKING_REQUEST_ID_FIELD_NUMBER: _ClassVar[int]
    BOOKING_REQUEST_IDENTIFIER_FIELD_NUMBER: _ClassVar[int]
    appointment: _appointment_defs_pb2.AppointmentCreateForOnlineBookingDef
    pet_details: _containers.RepeatedCompositeFieldContainer[_pet_detail_defs_pb2.PetDetailDef]
    notes: _containers.RepeatedCompositeFieldContainer[_appointment_note_defs_pb2.AppointmentNoteCreateDef]
    company_id: int
    business_id: int
    staff_id: int
    booking_request_id: int
    booking_request_identifier: str
    def __init__(self, appointment: _Optional[_Union[_appointment_defs_pb2.AppointmentCreateForOnlineBookingDef, _Mapping]] = ..., pet_details: _Optional[_Iterable[_Union[_pet_detail_defs_pb2.PetDetailDef, _Mapping]]] = ..., notes: _Optional[_Iterable[_Union[_appointment_note_defs_pb2.AppointmentNoteCreateDef, _Mapping]]] = ..., company_id: _Optional[int] = ..., business_id: _Optional[int] = ..., staff_id: _Optional[int] = ..., booking_request_id: _Optional[int] = ..., booking_request_identifier: _Optional[str] = ...) -> None: ...

class CreateAppointmentForOnlineBookingResponse(_message.Message):
    __slots__ = ("appointment_id",)
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    appointment_id: int
    def __init__(self, appointment_id: _Optional[int] = ...) -> None: ...

class ListBlockTimesRequest(_message.Message):
    __slots__ = ("company_id", "business_ids", "filter")
    class Filter(_message.Message):
        __slots__ = ("start_time_range", "end_time_range")
        START_TIME_RANGE_FIELD_NUMBER: _ClassVar[int]
        END_TIME_RANGE_FIELD_NUMBER: _ClassVar[int]
        start_time_range: _interval_pb2.Interval
        end_time_range: _interval_pb2.Interval
        def __init__(self, start_time_range: _Optional[_Union[_interval_pb2.Interval, _Mapping]] = ..., end_time_range: _Optional[_Union[_interval_pb2.Interval, _Mapping]] = ...) -> None: ...
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_IDS_FIELD_NUMBER: _ClassVar[int]
    FILTER_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    business_ids: _containers.RepeatedScalarFieldContainer[int]
    filter: ListBlockTimesRequest.Filter
    def __init__(self, company_id: _Optional[int] = ..., business_ids: _Optional[_Iterable[int]] = ..., filter: _Optional[_Union[ListBlockTimesRequest.Filter, _Mapping]] = ...) -> None: ...

class ListBlockTimesResponse(_message.Message):
    __slots__ = ("block_times",)
    BLOCK_TIMES_FIELD_NUMBER: _ClassVar[int]
    block_times: _containers.RepeatedCompositeFieldContainer[_block_time_models_pb2.BlockTimeModel]
    def __init__(self, block_times: _Optional[_Iterable[_Union[_block_time_models_pb2.BlockTimeModel, _Mapping]]] = ...) -> None: ...

class BatchQuickCheckInRequest(_message.Message):
    __slots__ = ("company_id", "business_id", "service_id", "pet_ids", "date", "staff_id", "source")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
    PET_IDS_FIELD_NUMBER: _ClassVar[int]
    DATE_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    SOURCE_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    business_id: int
    service_id: int
    pet_ids: _containers.RepeatedScalarFieldContainer[int]
    date: _date_pb2.Date
    staff_id: int
    source: _appointment_enums_pb2.AppointmentSource
    def __init__(self, company_id: _Optional[int] = ..., business_id: _Optional[int] = ..., service_id: _Optional[int] = ..., pet_ids: _Optional[_Iterable[int]] = ..., date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., staff_id: _Optional[int] = ..., source: _Optional[_Union[_appointment_enums_pb2.AppointmentSource, str]] = ...) -> None: ...

class BatchQuickCheckInResponse(_message.Message):
    __slots__ = ("created_appointment_ids", "updated_appointment_ids")
    CREATED_APPOINTMENT_IDS_FIELD_NUMBER: _ClassVar[int]
    UPDATED_APPOINTMENT_IDS_FIELD_NUMBER: _ClassVar[int]
    created_appointment_ids: _containers.RepeatedScalarFieldContainer[int]
    updated_appointment_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, created_appointment_ids: _Optional[_Iterable[int]] = ..., updated_appointment_ids: _Optional[_Iterable[int]] = ...) -> None: ...

class UpdateAppointmentSelectiveRequest(_message.Message):
    __slots__ = ("id", "appointment_date", "appointment_start_time", "appointment_end_time", "confirmed_time", "check_in_time", "check_out_time", "canceled_time", "color_code", "cancel_by_type", "cancel_by", "confirm_by_type", "confirm_by", "status_before_checkin", "status_before_ready", "status_before_finish", "update_time", "updated_by_id", "status", "ready_time", "payment_status", "book_online_status", "pet_details", "update_by_type", "is_deprecate")
    ID_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_DATE_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_START_TIME_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_END_TIME_FIELD_NUMBER: _ClassVar[int]
    CONFIRMED_TIME_FIELD_NUMBER: _ClassVar[int]
    CHECK_IN_TIME_FIELD_NUMBER: _ClassVar[int]
    CHECK_OUT_TIME_FIELD_NUMBER: _ClassVar[int]
    CANCELED_TIME_FIELD_NUMBER: _ClassVar[int]
    COLOR_CODE_FIELD_NUMBER: _ClassVar[int]
    CANCEL_BY_TYPE_FIELD_NUMBER: _ClassVar[int]
    CANCEL_BY_FIELD_NUMBER: _ClassVar[int]
    CONFIRM_BY_TYPE_FIELD_NUMBER: _ClassVar[int]
    CONFIRM_BY_FIELD_NUMBER: _ClassVar[int]
    STATUS_BEFORE_CHECKIN_FIELD_NUMBER: _ClassVar[int]
    STATUS_BEFORE_READY_FIELD_NUMBER: _ClassVar[int]
    STATUS_BEFORE_FINISH_FIELD_NUMBER: _ClassVar[int]
    UPDATE_TIME_FIELD_NUMBER: _ClassVar[int]
    UPDATED_BY_ID_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    READY_TIME_FIELD_NUMBER: _ClassVar[int]
    PAYMENT_STATUS_FIELD_NUMBER: _ClassVar[int]
    BOOK_ONLINE_STATUS_FIELD_NUMBER: _ClassVar[int]
    PET_DETAILS_FIELD_NUMBER: _ClassVar[int]
    UPDATE_BY_TYPE_FIELD_NUMBER: _ClassVar[int]
    IS_DEPRECATE_FIELD_NUMBER: _ClassVar[int]
    id: int
    appointment_date: str
    appointment_start_time: int
    appointment_end_time: int
    confirmed_time: int
    check_in_time: int
    check_out_time: int
    canceled_time: int
    color_code: str
    cancel_by_type: int
    cancel_by: int
    confirm_by_type: int
    confirm_by: int
    status_before_checkin: _appointment_enums_pb2.AppointmentStatus
    status_before_ready: _appointment_enums_pb2.AppointmentStatus
    status_before_finish: _appointment_enums_pb2.AppointmentStatus
    update_time: _timestamp_pb2.Timestamp
    updated_by_id: int
    status: _appointment_enums_pb2.AppointmentStatus
    ready_time: int
    payment_status: _appointment_enums_pb2.AppointmentPaymentStatus
    book_online_status: _appointment_enums_pb2.AppointmentBookOnlineStatus
    pet_details: _containers.RepeatedCompositeFieldContainer[_pet_detail_defs_pb2.UpdatePetDetailDef]
    update_by_type: _appointment_enums_pb2.AppointmentUpdatedBy
    is_deprecate: bool
    def __init__(self, id: _Optional[int] = ..., appointment_date: _Optional[str] = ..., appointment_start_time: _Optional[int] = ..., appointment_end_time: _Optional[int] = ..., confirmed_time: _Optional[int] = ..., check_in_time: _Optional[int] = ..., check_out_time: _Optional[int] = ..., canceled_time: _Optional[int] = ..., color_code: _Optional[str] = ..., cancel_by_type: _Optional[int] = ..., cancel_by: _Optional[int] = ..., confirm_by_type: _Optional[int] = ..., confirm_by: _Optional[int] = ..., status_before_checkin: _Optional[_Union[_appointment_enums_pb2.AppointmentStatus, str]] = ..., status_before_ready: _Optional[_Union[_appointment_enums_pb2.AppointmentStatus, str]] = ..., status_before_finish: _Optional[_Union[_appointment_enums_pb2.AppointmentStatus, str]] = ..., update_time: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., updated_by_id: _Optional[int] = ..., status: _Optional[_Union[_appointment_enums_pb2.AppointmentStatus, str]] = ..., ready_time: _Optional[int] = ..., payment_status: _Optional[_Union[_appointment_enums_pb2.AppointmentPaymentStatus, str]] = ..., book_online_status: _Optional[_Union[_appointment_enums_pb2.AppointmentBookOnlineStatus, str]] = ..., pet_details: _Optional[_Iterable[_Union[_pet_detail_defs_pb2.UpdatePetDetailDef, _Mapping]]] = ..., update_by_type: _Optional[_Union[_appointment_enums_pb2.AppointmentUpdatedBy, str]] = ..., is_deprecate: bool = ...) -> None: ...

class UpdateAppointmentSelectiveResponse(_message.Message):
    __slots__ = ("affected_rows",)
    AFFECTED_ROWS_FIELD_NUMBER: _ClassVar[int]
    affected_rows: int
    def __init__(self, affected_rows: _Optional[int] = ...) -> None: ...

class ListAppointmentForPetsRequest(_message.Message):
    __slots__ = ("company_id", "pet_ids", "filter")
    class Filter(_message.Message):
        __slots__ = ("service_id", "date", "business_id", "statuses")
        SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
        DATE_FIELD_NUMBER: _ClassVar[int]
        BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
        STATUSES_FIELD_NUMBER: _ClassVar[int]
        service_id: int
        date: _date_pb2.Date
        business_id: int
        statuses: _containers.RepeatedScalarFieldContainer[_appointment_enums_pb2.AppointmentStatus]
        def __init__(self, service_id: _Optional[int] = ..., date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., business_id: _Optional[int] = ..., statuses: _Optional[_Iterable[_Union[_appointment_enums_pb2.AppointmentStatus, str]]] = ...) -> None: ...
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    PET_IDS_FIELD_NUMBER: _ClassVar[int]
    FILTER_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    pet_ids: _containers.RepeatedScalarFieldContainer[int]
    filter: ListAppointmentForPetsRequest.Filter
    def __init__(self, company_id: _Optional[int] = ..., pet_ids: _Optional[_Iterable[int]] = ..., filter: _Optional[_Union[ListAppointmentForPetsRequest.Filter, _Mapping]] = ...) -> None: ...

class ListAppointmentForPetsResponse(_message.Message):
    __slots__ = ("appointments", "pet_id_to_appointment_id_list")
    class PetIdToAppointmentIdListEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: int
        value: _struct_pb2.Int64ListValue
        def __init__(self, key: _Optional[int] = ..., value: _Optional[_Union[_struct_pb2.Int64ListValue, _Mapping]] = ...) -> None: ...
    APPOINTMENTS_FIELD_NUMBER: _ClassVar[int]
    PET_ID_TO_APPOINTMENT_ID_LIST_FIELD_NUMBER: _ClassVar[int]
    appointments: _containers.RepeatedCompositeFieldContainer[_appointment_models_pb2.AppointmentModel]
    pet_id_to_appointment_id_list: _containers.MessageMap[int, _struct_pb2.Int64ListValue]
    def __init__(self, appointments: _Optional[_Iterable[_Union[_appointment_models_pb2.AppointmentModel, _Mapping]]] = ..., pet_id_to_appointment_id_list: _Optional[_Mapping[int, _struct_pb2.Int64ListValue]] = ...) -> None: ...

class ListAppointmentsForCustomersRequest(_message.Message):
    __slots__ = ("company_id", "customer_ids", "filter")
    class Filter(_message.Message):
        __slots__ = ("date_type", "statuses", "service_type_includes")
        DATE_TYPE_FIELD_NUMBER: _ClassVar[int]
        STATUSES_FIELD_NUMBER: _ClassVar[int]
        SERVICE_TYPE_INCLUDES_FIELD_NUMBER: _ClassVar[int]
        date_type: AppointmentDateType
        statuses: _containers.RepeatedScalarFieldContainer[_appointment_enums_pb2.AppointmentStatus]
        service_type_includes: _containers.RepeatedScalarFieldContainer[int]
        def __init__(self, date_type: _Optional[_Union[AppointmentDateType, str]] = ..., statuses: _Optional[_Iterable[_Union[_appointment_enums_pb2.AppointmentStatus, str]]] = ..., service_type_includes: _Optional[_Iterable[int]] = ...) -> None: ...
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_IDS_FIELD_NUMBER: _ClassVar[int]
    FILTER_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    customer_ids: _containers.RepeatedScalarFieldContainer[int]
    filter: ListAppointmentsForCustomersRequest.Filter
    def __init__(self, company_id: _Optional[int] = ..., customer_ids: _Optional[_Iterable[int]] = ..., filter: _Optional[_Union[ListAppointmentsForCustomersRequest.Filter, _Mapping]] = ...) -> None: ...

class ListAppointmentsForCustomersResponse(_message.Message):
    __slots__ = ("appointments",)
    APPOINTMENTS_FIELD_NUMBER: _ClassVar[int]
    appointments: _containers.RepeatedCompositeFieldContainer[_appointment_models_pb2.AppointmentModel]
    def __init__(self, appointments: _Optional[_Iterable[_Union[_appointment_models_pb2.AppointmentModel, _Mapping]]] = ...) -> None: ...

class CancelAppointmentRequest(_message.Message):
    __slots__ = ("appointment_id", "cancel_by_type", "cancel_by", "cancel_reason", "no_show", "repeat_appointment_modify_scope")
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    CANCEL_BY_TYPE_FIELD_NUMBER: _ClassVar[int]
    CANCEL_BY_FIELD_NUMBER: _ClassVar[int]
    CANCEL_REASON_FIELD_NUMBER: _ClassVar[int]
    NO_SHOW_FIELD_NUMBER: _ClassVar[int]
    REPEAT_APPOINTMENT_MODIFY_SCOPE_FIELD_NUMBER: _ClassVar[int]
    appointment_id: int
    cancel_by_type: _appointment_enums_pb2.AppointmentUpdatedBy
    cancel_by: int
    cancel_reason: str
    no_show: _appointment_enums_pb2.AppointmentNoShowStatus
    repeat_appointment_modify_scope: _pet_detail_enums_pb2.RepeatAppointmentModifyScope
    def __init__(self, appointment_id: _Optional[int] = ..., cancel_by_type: _Optional[_Union[_appointment_enums_pb2.AppointmentUpdatedBy, str]] = ..., cancel_by: _Optional[int] = ..., cancel_reason: _Optional[str] = ..., no_show: _Optional[_Union[_appointment_enums_pb2.AppointmentNoShowStatus, str]] = ..., repeat_appointment_modify_scope: _Optional[_Union[_pet_detail_enums_pb2.RepeatAppointmentModifyScope, str]] = ...) -> None: ...

class CancelAppointmentResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class BatchBookAgainAppointmentRequest(_message.Message):
    __slots__ = ("company_id", "appointment_ids", "target_date", "rebook_by")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_IDS_FIELD_NUMBER: _ClassVar[int]
    TARGET_DATE_FIELD_NUMBER: _ClassVar[int]
    REBOOK_BY_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    appointment_ids: _containers.RepeatedScalarFieldContainer[int]
    target_date: _date_pb2.Date
    rebook_by: int
    def __init__(self, company_id: _Optional[int] = ..., appointment_ids: _Optional[_Iterable[int]] = ..., target_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., rebook_by: _Optional[int] = ...) -> None: ...

class BatchBookAgainAppointmentResponse(_message.Message):
    __slots__ = ("appointments",)
    APPOINTMENTS_FIELD_NUMBER: _ClassVar[int]
    appointments: _containers.RepeatedCompositeFieldContainer[_appointment_models_pb2.AppointmentModel]
    def __init__(self, appointments: _Optional[_Iterable[_Union[_appointment_models_pb2.AppointmentModel, _Mapping]]] = ...) -> None: ...

class BatchCancelAppointmentRequest(_message.Message):
    __slots__ = ("company_id", "appointment_ids", "cancel_by", "cancel_reason")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_IDS_FIELD_NUMBER: _ClassVar[int]
    CANCEL_BY_FIELD_NUMBER: _ClassVar[int]
    CANCEL_REASON_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    appointment_ids: _containers.RepeatedScalarFieldContainer[int]
    cancel_by: int
    cancel_reason: str
    def __init__(self, company_id: _Optional[int] = ..., appointment_ids: _Optional[_Iterable[int]] = ..., cancel_by: _Optional[int] = ..., cancel_reason: _Optional[str] = ...) -> None: ...

class BatchCancelAppointmentResponse(_message.Message):
    __slots__ = ("appointments",)
    APPOINTMENTS_FIELD_NUMBER: _ClassVar[int]
    appointments: _containers.RepeatedCompositeFieldContainer[_appointment_models_pb2.AppointmentModel]
    def __init__(self, appointments: _Optional[_Iterable[_Union[_appointment_models_pb2.AppointmentModel, _Mapping]]] = ...) -> None: ...

class CountAppointmentForPetsRequest(_message.Message):
    __slots__ = ("pet_ids",)
    PET_IDS_FIELD_NUMBER: _ClassVar[int]
    pet_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, pet_ids: _Optional[_Iterable[int]] = ...) -> None: ...

class CountAppointmentForPetsResponse(_message.Message):
    __slots__ = ("pet_id_to_appointment_count",)
    class PetIdToAppointmentCountEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: int
        value: int
        def __init__(self, key: _Optional[int] = ..., value: _Optional[int] = ...) -> None: ...
    PET_ID_TO_APPOINTMENT_COUNT_FIELD_NUMBER: _ClassVar[int]
    pet_id_to_appointment_count: _containers.ScalarMap[int, int]
    def __init__(self, pet_id_to_appointment_count: _Optional[_Mapping[int, int]] = ...) -> None: ...

class DeleteAppointmentsRequest(_message.Message):
    __slots__ = ("company_id", "business_id", "ids")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    IDS_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    business_id: int
    ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, company_id: _Optional[int] = ..., business_id: _Optional[int] = ..., ids: _Optional[_Iterable[int]] = ...) -> None: ...

class DeleteAppointmentsResponse(_message.Message):
    __slots__ = ("affected_rows",)
    AFFECTED_ROWS_FIELD_NUMBER: _ClassVar[int]
    affected_rows: int
    def __init__(self, affected_rows: _Optional[int] = ...) -> None: ...

class RestoreAppointmentsRequest(_message.Message):
    __slots__ = ("company_id", "business_id", "ids")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    IDS_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    business_id: int
    ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, company_id: _Optional[int] = ..., business_id: _Optional[int] = ..., ids: _Optional[_Iterable[int]] = ...) -> None: ...

class RestoreAppointmentsResponse(_message.Message):
    __slots__ = ("affected_rows",)
    AFFECTED_ROWS_FIELD_NUMBER: _ClassVar[int]
    affected_rows: int
    def __init__(self, affected_rows: _Optional[int] = ...) -> None: ...

class RescheduleBoardingAppointmentRequest(_message.Message):
    __slots__ = ("company_id", "appointment_id", "start_date", "end_date")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    appointment_id: int
    start_date: str
    end_date: str
    def __init__(self, company_id: _Optional[int] = ..., appointment_id: _Optional[int] = ..., start_date: _Optional[str] = ..., end_date: _Optional[str] = ...) -> None: ...

class SyncAppointmentToOrderRequest(_message.Message):
    __slots__ = ("appointment_id",)
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    appointment_id: int
    def __init__(self, appointment_id: _Optional[int] = ...) -> None: ...

class SyncAppointmentToOrderResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class RescheduleBoardingAppointmentResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class PreviewOrderDetailRequest(_message.Message):
    __slots__ = ("appointment_ids",)
    APPOINTMENT_IDS_FIELD_NUMBER: _ClassVar[int]
    appointment_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, appointment_ids: _Optional[_Iterable[int]] = ...) -> None: ...

class PreviewOrderDetailResponse(_message.Message):
    __slots__ = ("order_details",)
    ORDER_DETAILS_FIELD_NUMBER: _ClassVar[int]
    order_details: _containers.RepeatedCompositeFieldContainer[_order_detail_models_pb2.OrderDetailModel]
    def __init__(self, order_details: _Optional[_Iterable[_Union[_order_detail_models_pb2.OrderDetailModel, _Mapping]]] = ...) -> None: ...

class PreviewOrderLineItemsRequest(_message.Message):
    __slots__ = ("appointment_id",)
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    appointment_id: int
    def __init__(self, appointment_id: _Optional[int] = ...) -> None: ...

class PreviewOrderLineItemsResponse(_message.Message):
    __slots__ = ("pet_services", "surcharges")
    PET_SERVICES_FIELD_NUMBER: _ClassVar[int]
    SURCHARGES_FIELD_NUMBER: _ClassVar[int]
    pet_services: _containers.RepeatedCompositeFieldContainer[_fulfillment_defs_pb2.PetService]
    surcharges: _containers.RepeatedCompositeFieldContainer[_fulfillment_defs_pb2.SurchargeItem]
    def __init__(self, pet_services: _Optional[_Iterable[_Union[_fulfillment_defs_pb2.PetService, _Mapping]]] = ..., surcharges: _Optional[_Iterable[_Union[_fulfillment_defs_pb2.SurchargeItem, _Mapping]]] = ...) -> None: ...

class GetTimeOverlapAppointmentListRequest(_message.Message):
    __slots__ = ("company_id", "customer_id", "pet_ids", "filter")
    class Filter(_message.Message):
        __slots__ = ("date_range",)
        DATE_RANGE_FIELD_NUMBER: _ClassVar[int]
        date_range: _interval_pb2.Interval
        def __init__(self, date_range: _Optional[_Union[_interval_pb2.Interval, _Mapping]] = ...) -> None: ...
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    PET_IDS_FIELD_NUMBER: _ClassVar[int]
    FILTER_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    customer_id: _containers.RepeatedScalarFieldContainer[int]
    pet_ids: _containers.RepeatedScalarFieldContainer[int]
    filter: GetTimeOverlapAppointmentListRequest.Filter
    def __init__(self, company_id: _Optional[int] = ..., customer_id: _Optional[_Iterable[int]] = ..., pet_ids: _Optional[_Iterable[int]] = ..., filter: _Optional[_Union[GetTimeOverlapAppointmentListRequest.Filter, _Mapping]] = ...) -> None: ...

class GetTimeOverlapAppointmentListResponse(_message.Message):
    __slots__ = ("appointment_list",)
    class AppointmentListEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: int
        value: AppointmentList
        def __init__(self, key: _Optional[int] = ..., value: _Optional[_Union[AppointmentList, _Mapping]] = ...) -> None: ...
    APPOINTMENT_LIST_FIELD_NUMBER: _ClassVar[int]
    appointment_list: _containers.MessageMap[int, AppointmentList]
    def __init__(self, appointment_list: _Optional[_Mapping[int, AppointmentList]] = ...) -> None: ...

class AppointmentList(_message.Message):
    __slots__ = ("appointments",)
    APPOINTMENTS_FIELD_NUMBER: _ClassVar[int]
    appointments: _containers.RepeatedCompositeFieldContainer[_appointment_models_pb2.AppointmentModel]
    def __init__(self, appointments: _Optional[_Iterable[_Union[_appointment_models_pb2.AppointmentModel, _Mapping]]] = ...) -> None: ...

class PreviewEstimateOrderRequest(_message.Message):
    __slots__ = ("company_id", "business_id", "appointment_ids")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_IDS_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    business_id: int
    appointment_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, company_id: _Optional[int] = ..., business_id: _Optional[int] = ..., appointment_ids: _Optional[_Iterable[int]] = ...) -> None: ...

class PreviewEstimateOrderResponse(_message.Message):
    __slots__ = ("estimated_orders",)
    class EstimatedOrder(_message.Message):
        __slots__ = ("appointment_id", "services_subtotal", "services_charges_total", "estimated_total")
        APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
        SERVICES_SUBTOTAL_FIELD_NUMBER: _ClassVar[int]
        SERVICES_CHARGES_TOTAL_FIELD_NUMBER: _ClassVar[int]
        ESTIMATED_TOTAL_FIELD_NUMBER: _ClassVar[int]
        appointment_id: int
        services_subtotal: _money_pb2.Money
        services_charges_total: _money_pb2.Money
        estimated_total: _money_pb2.Money
        def __init__(self, appointment_id: _Optional[int] = ..., services_subtotal: _Optional[_Union[_money_pb2.Money, _Mapping]] = ..., services_charges_total: _Optional[_Union[_money_pb2.Money, _Mapping]] = ..., estimated_total: _Optional[_Union[_money_pb2.Money, _Mapping]] = ...) -> None: ...
    ESTIMATED_ORDERS_FIELD_NUMBER: _ClassVar[int]
    estimated_orders: _containers.RepeatedCompositeFieldContainer[PreviewEstimateOrderResponse.EstimatedOrder]
    def __init__(self, estimated_orders: _Optional[_Iterable[_Union[PreviewEstimateOrderResponse.EstimatedOrder, _Mapping]]] = ...) -> None: ...

class ListExtraInfoRequest(_message.Message):
    __slots__ = ("appointment_ids",)
    APPOINTMENT_IDS_FIELD_NUMBER: _ClassVar[int]
    appointment_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, appointment_ids: _Optional[_Iterable[int]] = ...) -> None: ...

class ListExtraInfoResponse(_message.Message):
    __slots__ = ("extra_info",)
    EXTRA_INFO_FIELD_NUMBER: _ClassVar[int]
    extra_info: _containers.RepeatedCompositeFieldContainer[_appointment_extra_info_models_pb2.AppointmentExtraInfoModel]
    def __init__(self, extra_info: _Optional[_Iterable[_Union[_appointment_extra_info_models_pb2.AppointmentExtraInfoModel, _Mapping]]] = ...) -> None: ...

# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.service.appointment.v1 import appointment_service_pb2 as moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2


class AppointmentServiceStub(object):
    """the appointment service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CreateAppointment = channel.unary_unary(
                '/moego.service.appointment.v1.AppointmentService/CreateAppointment',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CreateAppointmentRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CreateAppointmentResponse.FromString,
                _registered_method=True)
        self.UpdateAppointment = channel.unary_unary(
                '/moego.service.appointment.v1.AppointmentService/UpdateAppointment',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.UpdateAppointmentRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.UpdateAppointmentResponse.FromString,
                _registered_method=True)
        self.UpdateAppointmentSelective = channel.unary_unary(
                '/moego.service.appointment.v1.AppointmentService/UpdateAppointmentSelective',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.UpdateAppointmentSelectiveRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.UpdateAppointmentSelectiveResponse.FromString,
                _registered_method=True)
        self.GetAppointment = channel.unary_unary(
                '/moego.service.appointment.v1.AppointmentService/GetAppointment',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.GetAppointmentRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.GetAppointmentResponse.FromString,
                _registered_method=True)
        self.GetAppointmentList = channel.unary_unary(
                '/moego.service.appointment.v1.AppointmentService/GetAppointmentList',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.GetAppointmentListRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.GetAppointmentListResponse.FromString,
                _registered_method=True)
        self.GetCustomerLastAppointment = channel.unary_unary(
                '/moego.service.appointment.v1.AppointmentService/GetCustomerLastAppointment',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.GetCustomerLastAppointmentRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.GetCustomerLastAppointmentResponse.FromString,
                _registered_method=True)
        self.CalculateAppointmentInvoice = channel.unary_unary(
                '/moego.service.appointment.v1.AppointmentService/CalculateAppointmentInvoice',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CalculateAppointmentInvoiceRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CalculateAppointmentInvoiceResponse.FromString,
                _registered_method=True)
        self.GetInProgressAppointment = channel.unary_unary(
                '/moego.service.appointment.v1.AppointmentService/GetInProgressAppointment',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.GetInProgressAppointmentRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.GetInProgressAppointmentResponse.FromString,
                _registered_method=True)
        self.CreateBlock = channel.unary_unary(
                '/moego.service.appointment.v1.AppointmentService/CreateBlock',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CreateBlockRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CreateBlockResponse.FromString,
                _registered_method=True)
        self.ListAppointments = channel.unary_unary(
                '/moego.service.appointment.v1.AppointmentService/ListAppointments',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.ListAppointmentsRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.ListAppointmentsResponse.FromString,
                _registered_method=True)
        self.ListBlockTimes = channel.unary_unary(
                '/moego.service.appointment.v1.AppointmentService/ListBlockTimes',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.ListBlockTimesRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.ListBlockTimesResponse.FromString,
                _registered_method=True)
        self.CreateAppointmentForOnlineBooking = channel.unary_unary(
                '/moego.service.appointment.v1.AppointmentService/CreateAppointmentForOnlineBooking',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CreateAppointmentForOnlineBookingRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CreateAppointmentForOnlineBookingResponse.FromString,
                _registered_method=True)
        self.BatchQuickCheckIn = channel.unary_unary(
                '/moego.service.appointment.v1.AppointmentService/BatchQuickCheckIn',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.BatchQuickCheckInRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.BatchQuickCheckInResponse.FromString,
                _registered_method=True)
        self.ListAppointmentForPets = channel.unary_unary(
                '/moego.service.appointment.v1.AppointmentService/ListAppointmentForPets',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.ListAppointmentForPetsRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.ListAppointmentForPetsResponse.FromString,
                _registered_method=True)
        self.ListAppointmentsForCustomers = channel.unary_unary(
                '/moego.service.appointment.v1.AppointmentService/ListAppointmentsForCustomers',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.ListAppointmentsForCustomersRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.ListAppointmentsForCustomersResponse.FromString,
                _registered_method=True)
        self.CancelAppointment = channel.unary_unary(
                '/moego.service.appointment.v1.AppointmentService/CancelAppointment',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CancelAppointmentRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CancelAppointmentResponse.FromString,
                _registered_method=True)
        self.BatchBookAgainAppointment = channel.unary_unary(
                '/moego.service.appointment.v1.AppointmentService/BatchBookAgainAppointment',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.BatchBookAgainAppointmentRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.BatchBookAgainAppointmentResponse.FromString,
                _registered_method=True)
        self.BatchCancelAppointment = channel.unary_unary(
                '/moego.service.appointment.v1.AppointmentService/BatchCancelAppointment',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.BatchCancelAppointmentRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.BatchCancelAppointmentResponse.FromString,
                _registered_method=True)
        self.CountAppointmentForPets = channel.unary_unary(
                '/moego.service.appointment.v1.AppointmentService/CountAppointmentForPets',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CountAppointmentForPetsRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CountAppointmentForPetsResponse.FromString,
                _registered_method=True)
        self.DeleteAppointments = channel.unary_unary(
                '/moego.service.appointment.v1.AppointmentService/DeleteAppointments',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.DeleteAppointmentsRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.DeleteAppointmentsResponse.FromString,
                _registered_method=True)
        self.RestoreAppointments = channel.unary_unary(
                '/moego.service.appointment.v1.AppointmentService/RestoreAppointments',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.RestoreAppointmentsRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.RestoreAppointmentsResponse.FromString,
                _registered_method=True)
        self.RescheduleBoardingAppointment = channel.unary_unary(
                '/moego.service.appointment.v1.AppointmentService/RescheduleBoardingAppointment',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.RescheduleBoardingAppointmentRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.RescheduleBoardingAppointmentResponse.FromString,
                _registered_method=True)
        self.SyncAppointmentToOrder = channel.unary_unary(
                '/moego.service.appointment.v1.AppointmentService/SyncAppointmentToOrder',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.SyncAppointmentToOrderRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.SyncAppointmentToOrderResponse.FromString,
                _registered_method=True)
        self.PreviewOrderDetail = channel.unary_unary(
                '/moego.service.appointment.v1.AppointmentService/PreviewOrderDetail',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.PreviewOrderDetailRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.PreviewOrderDetailResponse.FromString,
                _registered_method=True)
        self.PreviewOrderLineItems = channel.unary_unary(
                '/moego.service.appointment.v1.AppointmentService/PreviewOrderLineItems',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.PreviewOrderLineItemsRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.PreviewOrderLineItemsResponse.FromString,
                _registered_method=True)
        self.GetTimeOverlapAppointmentList = channel.unary_unary(
                '/moego.service.appointment.v1.AppointmentService/GetTimeOverlapAppointmentList',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.GetTimeOverlapAppointmentListRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.GetTimeOverlapAppointmentListResponse.FromString,
                _registered_method=True)
        self.PreviewEstimateOrder = channel.unary_unary(
                '/moego.service.appointment.v1.AppointmentService/PreviewEstimateOrder',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.PreviewEstimateOrderRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.PreviewEstimateOrderResponse.FromString,
                _registered_method=True)
        self.ListExtraInfo = channel.unary_unary(
                '/moego.service.appointment.v1.AppointmentService/ListExtraInfo',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.ListExtraInfoRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.ListExtraInfoResponse.FromString,
                _registered_method=True)


class AppointmentServiceServicer(object):
    """the appointment service
    """

    def CreateAppointment(self, request, context):
        """Create a appointment
        Applicable to quick add and advanced add appointment scenarios
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateAppointment(self, request, context):
        """Incremental update appointment
        Applicable to add/modify pet&service, switch on multi-pets start at the same time.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateAppointmentSelective(self, request, context):
        """增量更新 appointment
        NOTE：这个方法名应该叫做 UpdateAppointment，但是被占用了，
        而且 UpdateAppointment 的 request 使用了 api 层的定义，没法扩展，所以直接新开一个接口。
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetAppointment(self, request, context):
        """Get appointment
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetAppointmentList(self, request, context):
        """Get appointment list
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetCustomerLastAppointment(self, request, context):
        """get customer last appointment
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CalculateAppointmentInvoice(self, request, context):
        """Calculate appointment invoice
        Estimated total, including service, add-on, tax, discount etc.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetInProgressAppointment(self, request, context):
        """get in progress appointment
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateBlock(self, request, context):
        """Create block
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListAppointments(self, request, context):
        """List appointments
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListBlockTimes(self, request, context):
        """list block times
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateAppointmentForOnlineBooking(self, request, context):
        """Create a appointment for online booking
        Applicable to online booking scenarios for boarding, daycare & evaluation services
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def BatchQuickCheckIn(self, request, context):
        """batch quick check-in will create appointments for the selected pets and service, and set the appointment status to "checked_in"
        if the appointment already exists, it will update the appointment status to "checked_in"
        if multiple pets belong to the same customer, will be in the same appointment
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListAppointmentForPets(self, request, context):
        """list appointments for pet
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListAppointmentsForCustomers(self, request, context):
        """list appointments for customer
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CancelAppointment(self, request, context):
        """Cancel appointment
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def BatchBookAgainAppointment(self, request, context):
        """Batch book again appointment by staff and date
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def BatchCancelAppointment(self, request, context):
        """Batch cancel appointment by staff and date
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CountAppointmentForPets(self, request, context):
        """Count appointment for pets
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteAppointments(self, request, context):
        """Delete appointments
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RestoreAppointments(self, request, context):
        """RestoreAppointments 用于恢复之前被删除的 appointment 记录
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RescheduleBoardingAppointment(self, request, context):
        """Reschedule boarding appointment
        处理与 boarding 相关的附加数据，如 daycare、grooming、addon 等服务。
        如果传入的 end_date 早于当前 end_date，则删除 boarding 关联的所有 daycare、grooming、addon 数据。
        如果传入的 end_date 晚于当前 end_date，则在 boarding 上添加相应的新增 daycare、grooming、addon 数据。
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SyncAppointmentToOrder(self, request, context):
        """Sync appointment to order, temporary for data fix
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def PreviewOrderDetail(self, request, context):
        """Preview order detail by appointment id, temporary for data fix
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def PreviewOrderLineItems(self, request, context):
        """Preview order line items by appointment id, for invoice preview
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetTimeOverlapAppointmentList(self, request, context):
        """Get appointment time overlap list
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def PreviewEstimateOrder(self, request, context):
        """Preview estimated order for appointment
        estimated total = total service price + service charge(auto apply)
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListExtraInfo(self, request, context):
        """List extra info for appointment
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_AppointmentServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CreateAppointment': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateAppointment,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CreateAppointmentRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CreateAppointmentResponse.SerializeToString,
            ),
            'UpdateAppointment': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateAppointment,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.UpdateAppointmentRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.UpdateAppointmentResponse.SerializeToString,
            ),
            'UpdateAppointmentSelective': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateAppointmentSelective,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.UpdateAppointmentSelectiveRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.UpdateAppointmentSelectiveResponse.SerializeToString,
            ),
            'GetAppointment': grpc.unary_unary_rpc_method_handler(
                    servicer.GetAppointment,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.GetAppointmentRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.GetAppointmentResponse.SerializeToString,
            ),
            'GetAppointmentList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetAppointmentList,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.GetAppointmentListRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.GetAppointmentListResponse.SerializeToString,
            ),
            'GetCustomerLastAppointment': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCustomerLastAppointment,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.GetCustomerLastAppointmentRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.GetCustomerLastAppointmentResponse.SerializeToString,
            ),
            'CalculateAppointmentInvoice': grpc.unary_unary_rpc_method_handler(
                    servicer.CalculateAppointmentInvoice,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CalculateAppointmentInvoiceRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CalculateAppointmentInvoiceResponse.SerializeToString,
            ),
            'GetInProgressAppointment': grpc.unary_unary_rpc_method_handler(
                    servicer.GetInProgressAppointment,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.GetInProgressAppointmentRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.GetInProgressAppointmentResponse.SerializeToString,
            ),
            'CreateBlock': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateBlock,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CreateBlockRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CreateBlockResponse.SerializeToString,
            ),
            'ListAppointments': grpc.unary_unary_rpc_method_handler(
                    servicer.ListAppointments,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.ListAppointmentsRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.ListAppointmentsResponse.SerializeToString,
            ),
            'ListBlockTimes': grpc.unary_unary_rpc_method_handler(
                    servicer.ListBlockTimes,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.ListBlockTimesRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.ListBlockTimesResponse.SerializeToString,
            ),
            'CreateAppointmentForOnlineBooking': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateAppointmentForOnlineBooking,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CreateAppointmentForOnlineBookingRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CreateAppointmentForOnlineBookingResponse.SerializeToString,
            ),
            'BatchQuickCheckIn': grpc.unary_unary_rpc_method_handler(
                    servicer.BatchQuickCheckIn,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.BatchQuickCheckInRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.BatchQuickCheckInResponse.SerializeToString,
            ),
            'ListAppointmentForPets': grpc.unary_unary_rpc_method_handler(
                    servicer.ListAppointmentForPets,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.ListAppointmentForPetsRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.ListAppointmentForPetsResponse.SerializeToString,
            ),
            'ListAppointmentsForCustomers': grpc.unary_unary_rpc_method_handler(
                    servicer.ListAppointmentsForCustomers,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.ListAppointmentsForCustomersRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.ListAppointmentsForCustomersResponse.SerializeToString,
            ),
            'CancelAppointment': grpc.unary_unary_rpc_method_handler(
                    servicer.CancelAppointment,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CancelAppointmentRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CancelAppointmentResponse.SerializeToString,
            ),
            'BatchBookAgainAppointment': grpc.unary_unary_rpc_method_handler(
                    servicer.BatchBookAgainAppointment,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.BatchBookAgainAppointmentRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.BatchBookAgainAppointmentResponse.SerializeToString,
            ),
            'BatchCancelAppointment': grpc.unary_unary_rpc_method_handler(
                    servicer.BatchCancelAppointment,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.BatchCancelAppointmentRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.BatchCancelAppointmentResponse.SerializeToString,
            ),
            'CountAppointmentForPets': grpc.unary_unary_rpc_method_handler(
                    servicer.CountAppointmentForPets,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CountAppointmentForPetsRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CountAppointmentForPetsResponse.SerializeToString,
            ),
            'DeleteAppointments': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteAppointments,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.DeleteAppointmentsRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.DeleteAppointmentsResponse.SerializeToString,
            ),
            'RestoreAppointments': grpc.unary_unary_rpc_method_handler(
                    servicer.RestoreAppointments,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.RestoreAppointmentsRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.RestoreAppointmentsResponse.SerializeToString,
            ),
            'RescheduleBoardingAppointment': grpc.unary_unary_rpc_method_handler(
                    servicer.RescheduleBoardingAppointment,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.RescheduleBoardingAppointmentRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.RescheduleBoardingAppointmentResponse.SerializeToString,
            ),
            'SyncAppointmentToOrder': grpc.unary_unary_rpc_method_handler(
                    servicer.SyncAppointmentToOrder,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.SyncAppointmentToOrderRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.SyncAppointmentToOrderResponse.SerializeToString,
            ),
            'PreviewOrderDetail': grpc.unary_unary_rpc_method_handler(
                    servicer.PreviewOrderDetail,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.PreviewOrderDetailRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.PreviewOrderDetailResponse.SerializeToString,
            ),
            'PreviewOrderLineItems': grpc.unary_unary_rpc_method_handler(
                    servicer.PreviewOrderLineItems,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.PreviewOrderLineItemsRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.PreviewOrderLineItemsResponse.SerializeToString,
            ),
            'GetTimeOverlapAppointmentList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetTimeOverlapAppointmentList,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.GetTimeOverlapAppointmentListRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.GetTimeOverlapAppointmentListResponse.SerializeToString,
            ),
            'PreviewEstimateOrder': grpc.unary_unary_rpc_method_handler(
                    servicer.PreviewEstimateOrder,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.PreviewEstimateOrderRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.PreviewEstimateOrderResponse.SerializeToString,
            ),
            'ListExtraInfo': grpc.unary_unary_rpc_method_handler(
                    servicer.ListExtraInfo,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.ListExtraInfoRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.ListExtraInfoResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.service.appointment.v1.AppointmentService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.service.appointment.v1.AppointmentService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class AppointmentService(object):
    """the appointment service
    """

    @staticmethod
    def CreateAppointment(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.AppointmentService/CreateAppointment',
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CreateAppointmentRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CreateAppointmentResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateAppointment(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.AppointmentService/UpdateAppointment',
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.UpdateAppointmentRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.UpdateAppointmentResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateAppointmentSelective(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.AppointmentService/UpdateAppointmentSelective',
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.UpdateAppointmentSelectiveRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.UpdateAppointmentSelectiveResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetAppointment(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.AppointmentService/GetAppointment',
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.GetAppointmentRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.GetAppointmentResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetAppointmentList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.AppointmentService/GetAppointmentList',
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.GetAppointmentListRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.GetAppointmentListResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetCustomerLastAppointment(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.AppointmentService/GetCustomerLastAppointment',
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.GetCustomerLastAppointmentRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.GetCustomerLastAppointmentResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CalculateAppointmentInvoice(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.AppointmentService/CalculateAppointmentInvoice',
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CalculateAppointmentInvoiceRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CalculateAppointmentInvoiceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetInProgressAppointment(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.AppointmentService/GetInProgressAppointment',
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.GetInProgressAppointmentRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.GetInProgressAppointmentResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateBlock(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.AppointmentService/CreateBlock',
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CreateBlockRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CreateBlockResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListAppointments(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.AppointmentService/ListAppointments',
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.ListAppointmentsRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.ListAppointmentsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListBlockTimes(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.AppointmentService/ListBlockTimes',
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.ListBlockTimesRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.ListBlockTimesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateAppointmentForOnlineBooking(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.AppointmentService/CreateAppointmentForOnlineBooking',
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CreateAppointmentForOnlineBookingRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CreateAppointmentForOnlineBookingResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def BatchQuickCheckIn(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.AppointmentService/BatchQuickCheckIn',
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.BatchQuickCheckInRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.BatchQuickCheckInResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListAppointmentForPets(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.AppointmentService/ListAppointmentForPets',
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.ListAppointmentForPetsRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.ListAppointmentForPetsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListAppointmentsForCustomers(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.AppointmentService/ListAppointmentsForCustomers',
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.ListAppointmentsForCustomersRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.ListAppointmentsForCustomersResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CancelAppointment(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.AppointmentService/CancelAppointment',
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CancelAppointmentRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CancelAppointmentResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def BatchBookAgainAppointment(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.AppointmentService/BatchBookAgainAppointment',
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.BatchBookAgainAppointmentRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.BatchBookAgainAppointmentResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def BatchCancelAppointment(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.AppointmentService/BatchCancelAppointment',
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.BatchCancelAppointmentRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.BatchCancelAppointmentResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CountAppointmentForPets(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.AppointmentService/CountAppointmentForPets',
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CountAppointmentForPetsRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.CountAppointmentForPetsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteAppointments(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.AppointmentService/DeleteAppointments',
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.DeleteAppointmentsRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.DeleteAppointmentsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def RestoreAppointments(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.AppointmentService/RestoreAppointments',
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.RestoreAppointmentsRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.RestoreAppointmentsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def RescheduleBoardingAppointment(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.AppointmentService/RescheduleBoardingAppointment',
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.RescheduleBoardingAppointmentRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.RescheduleBoardingAppointmentResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SyncAppointmentToOrder(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.AppointmentService/SyncAppointmentToOrder',
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.SyncAppointmentToOrderRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.SyncAppointmentToOrderResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def PreviewOrderDetail(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.AppointmentService/PreviewOrderDetail',
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.PreviewOrderDetailRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.PreviewOrderDetailResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def PreviewOrderLineItems(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.AppointmentService/PreviewOrderLineItems',
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.PreviewOrderLineItemsRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.PreviewOrderLineItemsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetTimeOverlapAppointmentList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.AppointmentService/GetTimeOverlapAppointmentList',
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.GetTimeOverlapAppointmentListRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.GetTimeOverlapAppointmentListResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def PreviewEstimateOrder(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.AppointmentService/PreviewEstimateOrder',
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.PreviewEstimateOrderRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.PreviewEstimateOrderResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListExtraInfo(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.AppointmentService/ListExtraInfo',
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.ListExtraInfoRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_appointment__service__pb2.ListExtraInfoResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

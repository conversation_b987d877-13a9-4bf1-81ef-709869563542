from google.protobuf import struct_pb2 as _struct_pb2
from moego.utils.v2 import pagination_messages_pb2 as _pagination_messages_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class GetDataQueryInput(_message.Message):
    __slots__ = ("sql", "pagination")
    SQL_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    sql: str
    pagination: _pagination_messages_pb2.PaginationRequest
    def __init__(self, sql: _Optional[str] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ...) -> None: ...

class GetDataQueryOutput(_message.Message):
    __slots__ = ("result", "pagination")
    RESULT_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    result: _containers.RepeatedCompositeFieldContainer[_struct_pb2.Struct]
    pagination: _pagination_messages_pb2.PaginationResponse
    def __init__(self, result: _Optional[_Iterable[_Union[_struct_pb2.Struct, _Mapping]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ...) -> None: ...

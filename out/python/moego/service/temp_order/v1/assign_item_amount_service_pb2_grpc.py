# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.service.temp_order.v1 import assign_item_amount_service_pb2 as moego_dot_service_dot_temp__order_dot_v1_dot_assign__item__amount__service__pb2


class AssignItemAmountServiceStub(object):
    """Service for managing item amount assignments
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.AssignItemPaidAmount = channel.unary_unary(
                '/moego.service.temp_order.v1.AssignItemAmountService/AssignItemPaidAmount',
                request_serializer=moego_dot_service_dot_temp__order_dot_v1_dot_assign__item__amount__service__pb2.AssignItemPaidAmountRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_temp__order_dot_v1_dot_assign__item__amount__service__pb2.AssignItemPaidAmountResponse.FromString,
                _registered_method=True)
        self.GetAssignedItemPaidAmount = channel.unary_unary(
                '/moego.service.temp_order.v1.AssignItemAmountService/GetAssignedItemPaidAmount',
                request_serializer=moego_dot_service_dot_temp__order_dot_v1_dot_assign__item__amount__service__pb2.GetAssignedItemPaidAmountRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_temp__order_dot_v1_dot_assign__item__amount__service__pb2.GetAssignedItemPaidAmountResponse.FromString,
                _registered_method=True)


class AssignItemAmountServiceServicer(object):
    """Service for managing item amount assignments
    """

    def AssignItemPaidAmount(self, request, context):
        """分配已支付金额到指定项目 assigned item paid amount for order for whitelist(centralbark only)
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetAssignedItemPaidAmount(self, request, context):
        """get assigned item paid amount for order for whitelist(centralbark only)
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_AssignItemAmountServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'AssignItemPaidAmount': grpc.unary_unary_rpc_method_handler(
                    servicer.AssignItemPaidAmount,
                    request_deserializer=moego_dot_service_dot_temp__order_dot_v1_dot_assign__item__amount__service__pb2.AssignItemPaidAmountRequest.FromString,
                    response_serializer=moego_dot_service_dot_temp__order_dot_v1_dot_assign__item__amount__service__pb2.AssignItemPaidAmountResponse.SerializeToString,
            ),
            'GetAssignedItemPaidAmount': grpc.unary_unary_rpc_method_handler(
                    servicer.GetAssignedItemPaidAmount,
                    request_deserializer=moego_dot_service_dot_temp__order_dot_v1_dot_assign__item__amount__service__pb2.GetAssignedItemPaidAmountRequest.FromString,
                    response_serializer=moego_dot_service_dot_temp__order_dot_v1_dot_assign__item__amount__service__pb2.GetAssignedItemPaidAmountResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.service.temp_order.v1.AssignItemAmountService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.service.temp_order.v1.AssignItemAmountService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class AssignItemAmountService(object):
    """Service for managing item amount assignments
    """

    @staticmethod
    def AssignItemPaidAmount(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.temp_order.v1.AssignItemAmountService/AssignItemPaidAmount',
            moego_dot_service_dot_temp__order_dot_v1_dot_assign__item__amount__service__pb2.AssignItemPaidAmountRequest.SerializeToString,
            moego_dot_service_dot_temp__order_dot_v1_dot_assign__item__amount__service__pb2.AssignItemPaidAmountResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetAssignedItemPaidAmount(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.temp_order.v1.AssignItemAmountService/GetAssignedItemPaidAmount',
            moego_dot_service_dot_temp__order_dot_v1_dot_assign__item__amount__service__pb2.GetAssignedItemPaidAmountRequest.SerializeToString,
            moego_dot_service_dot_temp__order_dot_v1_dot_assign__item__amount__service__pb2.GetAssignedItemPaidAmountResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

from moego.models.open_platform.v1 import oauth_models_pb2 as _oauth_models_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class AuthenticateRequest(_message.Message):
    __slots__ = ("apikey", "oauth", "code", "session_info")
    APIKEY_FIELD_NUMBER: _ClassVar[int]
    OAUTH_FIELD_NUMBER: _ClassVar[int]
    CODE_FIELD_NUMBER: _ClassVar[int]
    SESSION_INFO_FIELD_NUMBER: _ClassVar[int]
    apikey: str
    oauth: OAuthCredential
    code: str
    session_info: SessionInfo
    def __init__(self, apikey: _Optional[str] = ..., oauth: _Optional[_Union[OAuthCredential, _Mapping]] = ..., code: _Optional[str] = ..., session_info: _Optional[_Union[SessionInfo, _Mapping]] = ...) -> None: ...

class SessionInfo(_message.Message):
    __slots__ = ("company_id", "business_id", "account_id")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    ACCOUNT_ID_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    business_id: int
    account_id: int
    def __init__(self, company_id: _Optional[int] = ..., business_id: _Optional[int] = ..., account_id: _Optional[int] = ...) -> None: ...

class OAuthCredential(_message.Message):
    __slots__ = ("client_id", "open_id", "access_token")
    CLIENT_ID_FIELD_NUMBER: _ClassVar[int]
    OPEN_ID_FIELD_NUMBER: _ClassVar[int]
    ACCESS_TOKEN_FIELD_NUMBER: _ClassVar[int]
    client_id: str
    open_id: str
    access_token: str
    def __init__(self, client_id: _Optional[str] = ..., open_id: _Optional[str] = ..., access_token: _Optional[str] = ...) -> None: ...

class AuthenticateResponse(_message.Message):
    __slots__ = ("user", "scopes", "restrictions")
    USER_FIELD_NUMBER: _ClassVar[int]
    SCOPES_FIELD_NUMBER: _ClassVar[int]
    RESTRICTIONS_FIELD_NUMBER: _ClassVar[int]
    user: _oauth_models_pb2.User
    scopes: _containers.RepeatedScalarFieldContainer[str]
    restrictions: _oauth_models_pb2.Restrictions
    def __init__(self, user: _Optional[_Union[_oauth_models_pb2.User, _Mapping]] = ..., scopes: _Optional[_Iterable[str]] = ..., restrictions: _Optional[_Union[_oauth_models_pb2.Restrictions, _Mapping]] = ...) -> None: ...

class CreateAPIKeyRequest(_message.Message):
    __slots__ = ("name", "restrictions", "enterprise_id")
    NAME_FIELD_NUMBER: _ClassVar[int]
    RESTRICTIONS_FIELD_NUMBER: _ClassVar[int]
    ENTERPRISE_ID_FIELD_NUMBER: _ClassVar[int]
    name: str
    restrictions: _oauth_models_pb2.Restrictions
    enterprise_id: int
    def __init__(self, name: _Optional[str] = ..., restrictions: _Optional[_Union[_oauth_models_pb2.Restrictions, _Mapping]] = ..., enterprise_id: _Optional[int] = ...) -> None: ...

class CreateAPIKeyResponse(_message.Message):
    __slots__ = ("api_key",)
    API_KEY_FIELD_NUMBER: _ClassVar[int]
    api_key: _oauth_models_pb2.APIKey
    def __init__(self, api_key: _Optional[_Union[_oauth_models_pb2.APIKey, _Mapping]] = ...) -> None: ...

class DeleteAPIKeyRequest(_message.Message):
    __slots__ = ("id",)
    ID_FIELD_NUMBER: _ClassVar[int]
    id: str
    def __init__(self, id: _Optional[str] = ...) -> None: ...

class DeleteAPIKeyResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from typing import ClassVar as _ClassVar

DESCRIPTOR: _descriptor.FileDescriptor

class SplitTipsMethod(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    SPLIT_TIPS_METHOD_UNSPECIFIED: _ClassVar[SplitTipsMethod]
    SPLIT_TIPS_METHOD_BY_SERVICE: _ClassVar[SplitTipsMethod]
    SPLIT_TIPS_METHOD_BY_EQUALLY: _ClassVar[SplitTipsMethod]
    SPLIT_TIPS_METHOD_CUSTOMIZED: _ClassVar[SplitTipsMethod]
    SPLIT_TIPS_METHOD_BY_PERCENTAGE: _ClassVar[SplitTipsMethod]
    SPLIT_TIPS_METHOD_BY_FIXED_AMOUNT: _ClassVar[SplitTipsMethod]

class CustomizedTipType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    CUSTOMIZED_TIP_TYPE_UNSPECIFIED: _ClassVar[CustomizedTipType]
    CUSTOMIZED_TIP_TYPE_AMOUNT: _ClassVar[CustomizedTipType]
    CUSTOMIZED_TIP_TYPE_PERCENTAGE: _ClassVar[CustomizedTipType]

class TipsSplitMode(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    TIPS_SPLIT_MODE_UNSPECIFIED: _ClassVar[TipsSplitMode]
    TIPS_SPLIT_MODE_APPT: _ClassVar[TipsSplitMode]
    TIPS_SPLIT_MODE_ORDER: _ClassVar[TipsSplitMode]
SPLIT_TIPS_METHOD_UNSPECIFIED: SplitTipsMethod
SPLIT_TIPS_METHOD_BY_SERVICE: SplitTipsMethod
SPLIT_TIPS_METHOD_BY_EQUALLY: SplitTipsMethod
SPLIT_TIPS_METHOD_CUSTOMIZED: SplitTipsMethod
SPLIT_TIPS_METHOD_BY_PERCENTAGE: SplitTipsMethod
SPLIT_TIPS_METHOD_BY_FIXED_AMOUNT: SplitTipsMethod
CUSTOMIZED_TIP_TYPE_UNSPECIFIED: CustomizedTipType
CUSTOMIZED_TIP_TYPE_AMOUNT: CustomizedTipType
CUSTOMIZED_TIP_TYPE_PERCENTAGE: CustomizedTipType
TIPS_SPLIT_MODE_UNSPECIFIED: TipsSplitMode
TIPS_SPLIT_MODE_APPT: TipsSplitMode
TIPS_SPLIT_MODE_ORDER: TipsSplitMode

from google.protobuf import timestamp_pb2 as _timestamp_pb2
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class MessageSendEvent(_message.Message):
    __slots__ = ("message_id", "customer_id", "phone_number", "staff_id", "text", "direction", "status", "time", "fail_reason")
    class Direction(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        DIRECTION_UNSPECIFIED: _ClassVar[MessageSendEvent.Direction]
        SEND: _ClassVar[MessageSendEvent.Direction]
        RECEIVE: _ClassVar[MessageSendEvent.Direction]
    DIRECTION_UNSPECIFIED: MessageSendEvent.Direction
    SEND: MessageSendEvent.Direction
    RECEIVE: MessageSendEvent.Direction
    class Status(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        STATUS_UNSPECIFIED: _ClassVar[MessageSendEvent.Status]
        SUCCESS: _ClassVar[MessageSendEvent.Status]
        FAIL: _ClassVar[MessageSendEvent.Status]
    STATUS_UNSPECIFIED: MessageSendEvent.Status
    SUCCESS: MessageSendEvent.Status
    FAIL: MessageSendEvent.Status
    MESSAGE_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    PHONE_NUMBER_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    TEXT_FIELD_NUMBER: _ClassVar[int]
    DIRECTION_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    TIME_FIELD_NUMBER: _ClassVar[int]
    FAIL_REASON_FIELD_NUMBER: _ClassVar[int]
    message_id: int
    customer_id: int
    phone_number: str
    staff_id: int
    text: str
    direction: MessageSendEvent.Direction
    status: MessageSendEvent.Status
    time: _timestamp_pb2.Timestamp
    fail_reason: str
    def __init__(self, message_id: _Optional[int] = ..., customer_id: _Optional[int] = ..., phone_number: _Optional[str] = ..., staff_id: _Optional[int] = ..., text: _Optional[str] = ..., direction: _Optional[_Union[MessageSendEvent.Direction, str]] = ..., status: _Optional[_Union[MessageSendEvent.Status, str]] = ..., time: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., fail_reason: _Optional[str] = ...) -> None: ...

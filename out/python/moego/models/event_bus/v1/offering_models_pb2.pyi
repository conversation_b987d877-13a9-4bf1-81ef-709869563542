from google.protobuf import duration_pb2 as _duration_pb2
from google.type import interval_pb2 as _interval_pb2
from moego.models.offering.v1 import group_class_models_pb2 as _group_class_models_pb2
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class GroupClassSessionEvent(_message.Message):
    __slots__ = ("session", "before_interval", "before_duration")
    SESSION_FIELD_NUMBER: _ClassVar[int]
    BEFORE_INTERVAL_FIELD_NUMBER: _ClassVar[int]
    BEFORE_DURATION_FIELD_NUMBER: _ClassVar[int]
    session: _group_class_models_pb2.GroupClassSession
    before_interval: _interval_pb2.Interval
    before_duration: _duration_pb2.Duration
    def __init__(self, session: _Optional[_Union[_group_class_models_pb2.GroupClassSession, _Mapping]] = ..., before_interval: _Optional[_Union[_interval_pb2.Interval, _Mapping]] = ..., before_duration: _Optional[_Union[datetime.timedelta, _duration_pb2.Duration, _Mapping]] = ...) -> None: ...

from moego.models.organization.v1 import camera_enums_pb2 as _camera_enums_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class IdogcamFilter(_message.Message):
    __slots__ = ("kennel_id", "erp_code")
    KENNEL_ID_FIELD_NUMBER: _ClassVar[int]
    ERP_CODE_FIELD_NUMBER: _ClassVar[int]
    kennel_id: str
    erp_code: str
    def __init__(self, kennel_id: _Optional[str] = ..., erp_code: _Optional[str] = ...) -> None: ...

class AbckamFilter(_message.Message):
    __slots__ = ("abckam_id",)
    ABCKAM_ID_FIELD_NUMBER: _ClassVar[int]
    abckam_id: str
    def __init__(self, abckam_id: _Optional[str] = ...) -> None: ...

class CameraFilter(_message.Message):
    __slots__ = ("is_active", "visibility_type")
    IS_ACTIVE_FIELD_NUMBER: _ClassVar[int]
    VISIBILITY_TYPE_FIELD_NUMBER: _ClassVar[int]
    is_active: bool
    visibility_type: _camera_enums_pb2.VisibilityType
    def __init__(self, is_active: bool = ..., visibility_type: _Optional[_Union[_camera_enums_pb2.VisibilityType, str]] = ...) -> None: ...

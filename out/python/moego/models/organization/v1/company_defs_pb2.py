# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/organization/v1/company_defs.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/organization/v1/company_defs.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.organization.v1 import company_enums_pb2 as moego_dot_models_dot_organization_dot_v1_dot_company__enums__pb2
from moego.models.organization.v1 import time_zone_pb2 as moego_dot_models_dot_organization_dot_v1_dot_time__zone__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n/moego/models/organization/v1/company_defs.proto\x12\x1cmoego.models.organization.v1\x1a\x30moego/models/organization/v1/company_enums.proto\x1a,moego/models/organization/v1/time_zone.proto\x1a\x17validate/validate.proto\"\xe5\x04\n!UpdateCompanyPreferenceSettingDef\x12-\n\rcurrency_code\x18\x01 \x01(\tB\x08\xfa\x42\x05r\x03\x98\x01\x03R\x0c\x63urrencyCode\x12\x30\n\x0f\x63urrency_symbol\x18\x02 \x01(\tB\x07\xfa\x42\x04r\x02\x10\x01R\x0e\x63urrencySymbol\x12R\n\x10\x64\x61te_format_type\x18\x03 \x01(\x0e\x32(.moego.models.organization.v1.DateFormatR\x0e\x64\x61teFormatType\x12R\n\x10time_format_type\x18\x04 \x01(\x0e\x32(.moego.models.organization.v1.TimeFormatR\x0etimeFormatType\x12W\n\x13unit_of_weight_type\x18\x05 \x01(\x0e\x32(.moego.models.organization.v1.WeightUnitR\x10unitOfWeightType\x12]\n\x15unit_of_distance_type\x18\x06 \x01(\x0e\x32*.moego.models.organization.v1.DistanceUnitR\x12unitOfDistanceType\x12:\n\x19notification_sound_enable\x18\x07 \x01(\x08R\x17notificationSoundEnable\x12\x43\n\ttime_zone\x18\x08 \x01(\x0b\x32&.moego.models.organization.v1.TimeZoneR\x08timeZone\"Q\n\x13\x43ompanyExtraInfoDef\x12:\n\x19unread_notification_count\x18\x01 \x01(\x05R\x17unreadNotificationCountB\x8a\x01\n$com.moego.idl.models.organization.v1P\x01Z`github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.organization.v1.company_defs_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n$com.moego.idl.models.organization.v1P\001Z`github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb'
  _globals['_UPDATECOMPANYPREFERENCESETTINGDEF'].fields_by_name['currency_code']._loaded_options = None
  _globals['_UPDATECOMPANYPREFERENCESETTINGDEF'].fields_by_name['currency_code']._serialized_options = b'\372B\005r\003\230\001\003'
  _globals['_UPDATECOMPANYPREFERENCESETTINGDEF'].fields_by_name['currency_symbol']._loaded_options = None
  _globals['_UPDATECOMPANYPREFERENCESETTINGDEF'].fields_by_name['currency_symbol']._serialized_options = b'\372B\004r\002\020\001'
  _globals['_UPDATECOMPANYPREFERENCESETTINGDEF']._serialized_start=203
  _globals['_UPDATECOMPANYPREFERENCESETTINGDEF']._serialized_end=816
  _globals['_COMPANYEXTRAINFODEF']._serialized_start=818
  _globals['_COMPANYEXTRAINFODEF']._serialized_end=899
# @@protoc_insertion_point(module_scope)

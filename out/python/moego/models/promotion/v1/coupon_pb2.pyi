from google.protobuf import timestamp_pb2 as _timestamp_pb2
from google.type import decimal_pb2 as _decimal_pb2
from google.type import interval_pb2 as _interval_pb2
from google.type import money_pb2 as _money_pb2
from moego.models.promotion.v1 import promotion_pb2 as _promotion_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class Coupon(_message.Message):
    __slots__ = ("id", "promotion_id", "source", "name", "description", "owner", "restrictions", "discount", "validity_period", "redemptions", "revision", "created_at", "updated_at")
    ID_FIELD_NUMBER: _ClassVar[int]
    PROMOTION_ID_FIELD_NUMBER: _ClassVar[int]
    SOURCE_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    OWNER_FIELD_NUMBER: _ClassVar[int]
    RESTRICTIONS_FIELD_NUMBER: _ClassVar[int]
    DISCOUNT_FIELD_NUMBER: _ClassVar[int]
    VALIDITY_PERIOD_FIELD_NUMBER: _ClassVar[int]
    REDEMPTIONS_FIELD_NUMBER: _ClassVar[int]
    REVISION_FIELD_NUMBER: _ClassVar[int]
    CREATED_AT_FIELD_NUMBER: _ClassVar[int]
    UPDATED_AT_FIELD_NUMBER: _ClassVar[int]
    id: int
    promotion_id: int
    source: _promotion_pb2.Source
    name: str
    description: str
    owner: User
    restrictions: _promotion_pb2.Restrictions
    discount: _promotion_pb2.Discount
    validity_period: _interval_pb2.Interval
    redemptions: _promotion_pb2.Redemptions
    revision: int
    created_at: _timestamp_pb2.Timestamp
    updated_at: _timestamp_pb2.Timestamp
    def __init__(self, id: _Optional[int] = ..., promotion_id: _Optional[int] = ..., source: _Optional[_Union[_promotion_pb2.Source, _Mapping]] = ..., name: _Optional[str] = ..., description: _Optional[str] = ..., owner: _Optional[_Union[User, _Mapping]] = ..., restrictions: _Optional[_Union[_promotion_pb2.Restrictions, _Mapping]] = ..., discount: _Optional[_Union[_promotion_pb2.Discount, _Mapping]] = ..., validity_period: _Optional[_Union[_interval_pb2.Interval, _Mapping]] = ..., redemptions: _Optional[_Union[_promotion_pb2.Redemptions, _Mapping]] = ..., revision: _Optional[int] = ..., created_at: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., updated_at: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ...) -> None: ...

class CouponUsage(_message.Message):
    __slots__ = ("coupon", "targets")
    COUPON_FIELD_NUMBER: _ClassVar[int]
    TARGETS_FIELD_NUMBER: _ClassVar[int]
    coupon: Coupon
    targets: _containers.RepeatedCompositeFieldContainer[_promotion_pb2.CouponApplicationTarget]
    def __init__(self, coupon: _Optional[_Union[Coupon, _Mapping]] = ..., targets: _Optional[_Iterable[_Union[_promotion_pb2.CouponApplicationTarget, _Mapping]]] = ...) -> None: ...

class User(_message.Message):
    __slots__ = ("id", "type")
    class Type(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        TYPE_UNSPECIFIED: _ClassVar[User.Type]
        MOEGO: _ClassVar[User.Type]
        ACCOUNT: _ClassVar[User.Type]
        ENTERPRISE: _ClassVar[User.Type]
        COMPANY: _ClassVar[User.Type]
        BUSINESS: _ClassVar[User.Type]
        CUSTOMER: _ClassVar[User.Type]
        STAFF: _ClassVar[User.Type]
    TYPE_UNSPECIFIED: User.Type
    MOEGO: User.Type
    ACCOUNT: User.Type
    ENTERPRISE: User.Type
    COMPANY: User.Type
    BUSINESS: User.Type
    CUSTOMER: User.Type
    STAFF: User.Type
    ID_FIELD_NUMBER: _ClassVar[int]
    TYPE_FIELD_NUMBER: _ClassVar[int]
    id: int
    type: User.Type
    def __init__(self, id: _Optional[int] = ..., type: _Optional[_Union[User.Type, str]] = ...) -> None: ...

class CouponRevision(_message.Message):
    __slots__ = ("id", "type", "coupon_id", "order_id", "order_line_item_id", "user", "operator", "targets", "redeemed_times", "cost", "created_at")
    class Type(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        TYPE_UNSPECIFIED: _ClassVar[CouponRevision.Type]
        REDEEM: _ClassVar[CouponRevision.Type]
        REFUND: _ClassVar[CouponRevision.Type]
    TYPE_UNSPECIFIED: CouponRevision.Type
    REDEEM: CouponRevision.Type
    REFUND: CouponRevision.Type
    ID_FIELD_NUMBER: _ClassVar[int]
    TYPE_FIELD_NUMBER: _ClassVar[int]
    COUPON_ID_FIELD_NUMBER: _ClassVar[int]
    ORDER_ID_FIELD_NUMBER: _ClassVar[int]
    ORDER_LINE_ITEM_ID_FIELD_NUMBER: _ClassVar[int]
    USER_FIELD_NUMBER: _ClassVar[int]
    OPERATOR_FIELD_NUMBER: _ClassVar[int]
    TARGETS_FIELD_NUMBER: _ClassVar[int]
    REDEEMED_TIMES_FIELD_NUMBER: _ClassVar[int]
    COST_FIELD_NUMBER: _ClassVar[int]
    CREATED_AT_FIELD_NUMBER: _ClassVar[int]
    id: int
    type: CouponRevision.Type
    coupon_id: int
    order_id: int
    order_line_item_id: int
    user: User
    operator: User
    targets: _promotion_pb2.Targets
    redeemed_times: int
    cost: _money_pb2.Money
    created_at: _timestamp_pb2.Timestamp
    def __init__(self, id: _Optional[int] = ..., type: _Optional[_Union[CouponRevision.Type, str]] = ..., coupon_id: _Optional[int] = ..., order_id: _Optional[int] = ..., order_line_item_id: _Optional[int] = ..., user: _Optional[_Union[User, _Mapping]] = ..., operator: _Optional[_Union[User, _Mapping]] = ..., targets: _Optional[_Union[_promotion_pb2.Targets, _Mapping]] = ..., redeemed_times: _Optional[int] = ..., cost: _Optional[_Union[_money_pb2.Money, _Mapping]] = ..., created_at: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ...) -> None: ...

class TargetDeduction(_message.Message):
    __slots__ = ("target_type", "target_id", "original_amount", "final_amount", "coupon_deductions", "order_item_id")
    TARGET_TYPE_FIELD_NUMBER: _ClassVar[int]
    TARGET_ID_FIELD_NUMBER: _ClassVar[int]
    ORIGINAL_AMOUNT_FIELD_NUMBER: _ClassVar[int]
    FINAL_AMOUNT_FIELD_NUMBER: _ClassVar[int]
    COUPON_DEDUCTIONS_FIELD_NUMBER: _ClassVar[int]
    ORDER_ITEM_ID_FIELD_NUMBER: _ClassVar[int]
    target_type: _promotion_pb2.TargetType
    target_id: int
    original_amount: _money_pb2.Money
    final_amount: _money_pb2.Money
    coupon_deductions: _containers.RepeatedCompositeFieldContainer[CouponDeduction]
    order_item_id: int
    def __init__(self, target_type: _Optional[_Union[_promotion_pb2.TargetType, str]] = ..., target_id: _Optional[int] = ..., original_amount: _Optional[_Union[_money_pb2.Money, _Mapping]] = ..., final_amount: _Optional[_Union[_money_pb2.Money, _Mapping]] = ..., coupon_deductions: _Optional[_Iterable[_Union[CouponDeduction, _Mapping]]] = ..., order_item_id: _Optional[int] = ...) -> None: ...

class CouponDeduction(_message.Message):
    __slots__ = ("coupon", "fixed_amount", "percentage", "quantity")
    COUPON_FIELD_NUMBER: _ClassVar[int]
    FIXED_AMOUNT_FIELD_NUMBER: _ClassVar[int]
    PERCENTAGE_FIELD_NUMBER: _ClassVar[int]
    QUANTITY_FIELD_NUMBER: _ClassVar[int]
    coupon: Coupon
    fixed_amount: _money_pb2.Money
    percentage: _decimal_pb2.Decimal
    quantity: int
    def __init__(self, coupon: _Optional[_Union[Coupon, _Mapping]] = ..., fixed_amount: _Optional[_Union[_money_pb2.Money, _Mapping]] = ..., percentage: _Optional[_Union[_decimal_pb2.Decimal, _Mapping]] = ..., quantity: _Optional[int] = ...) -> None: ...

class CouponRedeem(_message.Message):
    __slots__ = ("coupon_sources", "targets")
    class RedeemTarget(_message.Message):
        __slots__ = ("target_type", "target_id", "order_item_id", "idempotence_key", "amount", "target_sales")
        TARGET_TYPE_FIELD_NUMBER: _ClassVar[int]
        TARGET_ID_FIELD_NUMBER: _ClassVar[int]
        ORDER_ITEM_ID_FIELD_NUMBER: _ClassVar[int]
        IDEMPOTENCE_KEY_FIELD_NUMBER: _ClassVar[int]
        AMOUNT_FIELD_NUMBER: _ClassVar[int]
        TARGET_SALES_FIELD_NUMBER: _ClassVar[int]
        target_type: _promotion_pb2.TargetType
        target_id: int
        order_item_id: int
        idempotence_key: str
        amount: int
        target_sales: _money_pb2.Money
        def __init__(self, target_type: _Optional[_Union[_promotion_pb2.TargetType, str]] = ..., target_id: _Optional[int] = ..., order_item_id: _Optional[int] = ..., idempotence_key: _Optional[str] = ..., amount: _Optional[int] = ..., target_sales: _Optional[_Union[_money_pb2.Money, _Mapping]] = ...) -> None: ...
    COUPON_SOURCES_FIELD_NUMBER: _ClassVar[int]
    TARGETS_FIELD_NUMBER: _ClassVar[int]
    coupon_sources: _promotion_pb2.Source
    targets: _containers.RepeatedCompositeFieldContainer[CouponRedeem.RedeemTarget]
    def __init__(self, coupon_sources: _Optional[_Union[_promotion_pb2.Source, _Mapping]] = ..., targets: _Optional[_Iterable[_Union[CouponRedeem.RedeemTarget, _Mapping]]] = ...) -> None: ...

class CouponSearchCondition(_message.Message):
    __slots__ = ("customer_id", "name_keyword", "source_types", "targets")
    class SourceType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        SOURCE_TYPE_UNSPECIFIED: _ClassVar[CouponSearchCondition.SourceType]
        DISCOUNT: _ClassVar[CouponSearchCondition.SourceType]
        PACKAGE: _ClassVar[CouponSearchCondition.SourceType]
        MEMBERSHIP: _ClassVar[CouponSearchCondition.SourceType]
    SOURCE_TYPE_UNSPECIFIED: CouponSearchCondition.SourceType
    DISCOUNT: CouponSearchCondition.SourceType
    PACKAGE: CouponSearchCondition.SourceType
    MEMBERSHIP: CouponSearchCondition.SourceType
    class Target(_message.Message):
        __slots__ = ("target_type", "target_id")
        TARGET_TYPE_FIELD_NUMBER: _ClassVar[int]
        TARGET_ID_FIELD_NUMBER: _ClassVar[int]
        target_type: _promotion_pb2.TargetType
        target_id: int
        def __init__(self, target_type: _Optional[_Union[_promotion_pb2.TargetType, str]] = ..., target_id: _Optional[int] = ...) -> None: ...
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    NAME_KEYWORD_FIELD_NUMBER: _ClassVar[int]
    SOURCE_TYPES_FIELD_NUMBER: _ClassVar[int]
    TARGETS_FIELD_NUMBER: _ClassVar[int]
    customer_id: int
    name_keyword: str
    source_types: _containers.RepeatedScalarFieldContainer[CouponSearchCondition.SourceType]
    targets: _containers.RepeatedCompositeFieldContainer[CouponSearchCondition.Target]
    def __init__(self, customer_id: _Optional[int] = ..., name_keyword: _Optional[str] = ..., source_types: _Optional[_Iterable[_Union[CouponSearchCondition.SourceType, str]]] = ..., targets: _Optional[_Iterable[_Union[CouponSearchCondition.Target, _Mapping]]] = ...) -> None: ...

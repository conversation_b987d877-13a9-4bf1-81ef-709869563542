# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/enterprise/v1/tenant_group_models.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/enterprise/v1/tenant_group_models.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n4moego/models/enterprise/v1/tenant_group_models.proto\x12\x1amoego.models.enterprise.v1\"\xca\x01\n\x10TenantGroupModel\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n\x04name\x18\x02 \x01(\tR\x04name\x12K\n\x06status\x18\x03 \x01(\x0e\x32\x33.moego.models.enterprise.v1.TenantGroupModel.StatusR\x06status\"E\n\x06Status\x12#\n\x1fTENANT_GROUP_STATUS_UNSPECIFIED\x10\x00\x12\n\n\x06NORMAL\x10\x01\x12\n\n\x06\x44\x45LETE\x10\x02\x42\x84\x01\n\"com.moego.idl.models.enterprise.v1P\x01Z\\github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1;enterprisepbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.enterprise.v1.tenant_group_models_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\"com.moego.idl.models.enterprise.v1P\001Z\\github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1;enterprisepb'
  _globals['_TENANTGROUPMODEL']._serialized_start=85
  _globals['_TENANTGROUPMODEL']._serialized_end=287
  _globals['_TENANTGROUPMODEL_STATUS']._serialized_start=218
  _globals['_TENANTGROUPMODEL_STATUS']._serialized_end=287
# @@protoc_insertion_point(module_scope)

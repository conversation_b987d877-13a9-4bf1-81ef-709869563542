from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from typing import ClassVar as _ClassVar

DESCRIPTOR: _descriptor.FileDescriptor

class TransactionHistoryStatus(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    TRANSACTION_HISTORY_UNSPECIFIED: _ClassVar[TransactionHistoryStatus]
    CREATED: _ClassVar[TransactionHistoryStatus]
    PROCESSING: _ClassVar[TransactionHistoryStatus]
    PAID: _ClassVar[TransactionHistoryStatus]
    COMPLETED: _ClassVar[TransactionHistoryStatus]
    FAILED: _ClassVar[TransactionHistoryStatus]
TRANSACTION_HISTORY_UNSPECIFIED: TransactionHistoryStatus
CREATED: TransactionHistoryStatus
PROCESSING: TransactionHistoryStatus
PAID: TransactionHistoryStatus
COMPLETED: TransactionHistoryStatus
FAILED: TransactionHistoryStatus

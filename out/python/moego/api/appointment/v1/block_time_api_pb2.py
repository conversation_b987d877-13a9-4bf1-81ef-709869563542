# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/api/appointment/v1/block_time_api.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/api/appointment/v1/block_time_api.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.appointment.v1 import appointment_defs_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_appointment__defs__pb2
from moego.models.appointment.v1 import appointment_note_defs_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_appointment__note__defs__pb2
from moego.models.appointment.v1 import pet_detail_defs_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_pet__detail__defs__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n-moego/api/appointment/v1/block_time_api.proto\x12\x18moego.api.appointment.v1\x1a\x32moego/models/appointment/v1/appointment_defs.proto\x1a\x37moego/models/appointment/v1/appointment_note_defs.proto\x1a\x31moego/models/appointment/v1/pet_detail_defs.proto\x1a\x17validate/validate.proto\"\xbd\x02\n\x15\x43reateBlockTimeParams\x12m\n\x11\x62lock_appointment\x18\x01 \x01(\x0b\x32\x36.moego.models.appointment.v1.BlockAppointmentCreateDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x10\x62lockAppointment\x12R\n\nblock_time\x18\x02 \x01(\x0b\x32).moego.models.appointment.v1.BlockTimeDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\tblockTime\x12X\n\x04note\x18\x03 \x01(\x0b\x32\x35.moego.models.appointment.v1.AppointmentNoteCreateDefB\x08\xfa\x42\x05\x8a\x01\x02\x08\x01H\x00R\x04note\x88\x01\x01\x42\x07\n\x05_note\">\n\x15\x43reateBlockTimeResult\x12%\n\x0e\x61ppointment_id\x18\x01 \x01(\x03R\rappointmentId\"\xbd\x02\n\x15UpdateBlockTimeParams\x12m\n\x11\x62lock_appointment\x18\x01 \x01(\x0b\x32\x36.moego.models.appointment.v1.BlockAppointmentUpdateDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x10\x62lockAppointment\x12R\n\nblock_time\x18\x02 \x01(\x0b\x32).moego.models.appointment.v1.BlockTimeDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\tblockTime\x12X\n\x04note\x18\x03 \x01(\x0b\x32\x35.moego.models.appointment.v1.AppointmentNoteUpdateDefB\x08\xfa\x42\x05\x8a\x01\x02\x08\x01H\x00R\x04note\x88\x01\x01\x42\x07\n\x05_note\"\x17\n\x15UpdateBlockTimeResult2\xfc\x01\n\x10\x42lockTimeService\x12s\n\x0f\x43reateBlockTime\x12/.moego.api.appointment.v1.CreateBlockTimeParams\x1a/.moego.api.appointment.v1.CreateBlockTimeResult\x12s\n\x0fUpdateBlockTime\x12/.moego.api.appointment.v1.UpdateBlockTimeParams\x1a/.moego.api.appointment.v1.UpdateBlockTimeResultB\x84\x01\n com.moego.idl.api.appointment.v1P\x01Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1;appointmentapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.api.appointment.v1.block_time_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n com.moego.idl.api.appointment.v1P\001Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1;appointmentapipb'
  _globals['_CREATEBLOCKTIMEPARAMS'].fields_by_name['block_appointment']._loaded_options = None
  _globals['_CREATEBLOCKTIMEPARAMS'].fields_by_name['block_appointment']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_CREATEBLOCKTIMEPARAMS'].fields_by_name['block_time']._loaded_options = None
  _globals['_CREATEBLOCKTIMEPARAMS'].fields_by_name['block_time']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_CREATEBLOCKTIMEPARAMS'].fields_by_name['note']._loaded_options = None
  _globals['_CREATEBLOCKTIMEPARAMS'].fields_by_name['note']._serialized_options = b'\372B\005\212\001\002\010\001'
  _globals['_UPDATEBLOCKTIMEPARAMS'].fields_by_name['block_appointment']._loaded_options = None
  _globals['_UPDATEBLOCKTIMEPARAMS'].fields_by_name['block_appointment']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_UPDATEBLOCKTIMEPARAMS'].fields_by_name['block_time']._loaded_options = None
  _globals['_UPDATEBLOCKTIMEPARAMS'].fields_by_name['block_time']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_UPDATEBLOCKTIMEPARAMS'].fields_by_name['note']._loaded_options = None
  _globals['_UPDATEBLOCKTIMEPARAMS'].fields_by_name['note']._serialized_options = b'\372B\005\212\001\002\010\001'
  _globals['_CREATEBLOCKTIMEPARAMS']._serialized_start=261
  _globals['_CREATEBLOCKTIMEPARAMS']._serialized_end=578
  _globals['_CREATEBLOCKTIMERESULT']._serialized_start=580
  _globals['_CREATEBLOCKTIMERESULT']._serialized_end=642
  _globals['_UPDATEBLOCKTIMEPARAMS']._serialized_start=645
  _globals['_UPDATEBLOCKTIMEPARAMS']._serialized_end=962
  _globals['_UPDATEBLOCKTIMERESULT']._serialized_start=964
  _globals['_UPDATEBLOCKTIMERESULT']._serialized_end=987
  _globals['_BLOCKTIMESERVICE']._serialized_start=990
  _globals['_BLOCKTIMESERVICE']._serialized_end=1242
# @@protoc_insertion_point(module_scope)

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/api/online_booking/v1/booking_request_api.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/api/online_booking/v1/booking_request_api.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.type import date_pb2 as google_dot_type_dot_date__pb2
from moego.api.appointment.v1 import appointment_view_pb2 as moego_dot_api_dot_appointment_dot_v1_dot_appointment__view__pb2
from moego.models.appointment.v1 import appointment_pet_medication_schedule_defs_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_appointment__pet__medication__schedule__defs__pb2
from moego.models.appointment.v1 import pet_detail_enums_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_pet__detail__enums__pb2
from moego.models.customer.v1 import customer_pet_enums_pb2 as moego_dot_models_dot_customer_dot_v1_dot_customer__pet__enums__pb2
from moego.models.membership.v1 import membership_models_pb2 as moego_dot_models_dot_membership_dot_v1_dot_membership__models__pb2
from moego.models.membership.v1 import subscription_models_pb2 as moego_dot_models_dot_membership_dot_v1_dot_subscription__models__pb2
from moego.models.offering.v1 import evaluation_models_pb2 as moego_dot_models_dot_offering_dot_v1_dot_evaluation__models__pb2
from moego.models.offering.v1 import group_class_models_pb2 as moego_dot_models_dot_offering_dot_v1_dot_group__class__models__pb2
from moego.models.offering.v1 import service_enum_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__enum__pb2
from moego.models.offering.v1 import service_models_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__models__pb2
from moego.models.online_booking.v1 import boarding_add_on_detail_models_pb2 as moego_dot_models_dot_online__booking_dot_v1_dot_boarding__add__on__detail__models__pb2
from moego.models.online_booking.v1 import boarding_service_detail_models_pb2 as moego_dot_models_dot_online__booking_dot_v1_dot_boarding__service__detail__models__pb2
from moego.models.online_booking.v1 import booking_request_enums_pb2 as moego_dot_models_dot_online__booking_dot_v1_dot_booking__request__enums__pb2
from moego.models.online_booking.v1 import booking_request_models_pb2 as moego_dot_models_dot_online__booking_dot_v1_dot_booking__request__models__pb2
from moego.models.online_booking.v1 import daycare_add_on_detail_models_pb2 as moego_dot_models_dot_online__booking_dot_v1_dot_daycare__add__on__detail__models__pb2
from moego.models.online_booking.v1 import daycare_service_detail_models_pb2 as moego_dot_models_dot_online__booking_dot_v1_dot_daycare__service__detail__models__pb2
from moego.models.online_booking.v1 import evaluation_test_detail_models_pb2 as moego_dot_models_dot_online__booking_dot_v1_dot_evaluation__test__detail__models__pb2
from moego.models.online_booking.v1 import feeding_models_pb2 as moego_dot_models_dot_online__booking_dot_v1_dot_feeding__models__pb2
from moego.models.online_booking.v1 import grooming_add_on_detail_models_pb2 as moego_dot_models_dot_online__booking_dot_v1_dot_grooming__add__on__detail__models__pb2
from moego.models.online_booking.v1 import grooming_service_detail_models_pb2 as moego_dot_models_dot_online__booking_dot_v1_dot_grooming__service__detail__models__pb2
from moego.models.online_booking.v1 import medication_models_pb2 as moego_dot_models_dot_online__booking_dot_v1_dot_medication__models__pb2
from moego.service.online_booking.v1 import booking_request_service_pb2 as moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2
from moego.utils.v2 import condition_messages_pb2 as moego_dot_utils_dot_v2_dot_condition__messages__pb2
from moego.utils.v2 import pagination_messages_pb2 as moego_dot_utils_dot_v2_dot_pagination__messages__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n5moego/api/online_booking/v1/booking_request_api.proto\x12\x1bmoego.api.online_booking.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x16google/type/date.proto\x1a/moego/api/appointment/v1/appointment_view.proto\x1aJmoego/models/appointment/v1/appointment_pet_medication_schedule_defs.proto\x1a\x32moego/models/appointment/v1/pet_detail_enums.proto\x1a\x31moego/models/customer/v1/customer_pet_enums.proto\x1a\x32moego/models/membership/v1/membership_models.proto\x1a\x34moego/models/membership/v1/subscription_models.proto\x1a\x30moego/models/offering/v1/evaluation_models.proto\x1a\x31moego/models/offering/v1/group_class_models.proto\x1a+moego/models/offering/v1/service_enum.proto\x1a-moego/models/offering/v1/service_models.proto\x1a\x42moego/models/online_booking/v1/boarding_add_on_detail_models.proto\x1a\x43moego/models/online_booking/v1/boarding_service_detail_models.proto\x1a:moego/models/online_booking/v1/booking_request_enums.proto\x1a;moego/models/online_booking/v1/booking_request_models.proto\x1a\x41moego/models/online_booking/v1/daycare_add_on_detail_models.proto\x1a\x42moego/models/online_booking/v1/daycare_service_detail_models.proto\x1a\x42moego/models/online_booking/v1/evaluation_test_detail_models.proto\x1a\x33moego/models/online_booking/v1/feeding_models.proto\x1a\x42moego/models/online_booking/v1/grooming_add_on_detail_models.proto\x1a\x43moego/models/online_booking/v1/grooming_service_detail_models.proto\x1a\x36moego/models/online_booking/v1/medication_models.proto\x1a=moego/service/online_booking/v1/booking_request_service.proto\x1a\'moego/utils/v2/condition_messages.proto\x1a(moego/utils/v2/pagination_messages.proto\x1a\x17validate/validate.proto\"\xff\x01\n\x1cGetBookingRequestListRequest\x12(\n\x0b\x62usiness_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12K\n\npagination\x18\x02 \x01(\x0b\x32!.moego.utils.v2.PaginationRequestB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\npagination\x12&\n\x07keyword\x18\x03 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x32H\x00R\x07keyword\x88\x01\x01\x12\x34\n\torder_bys\x18\x04 \x03(\x0b\x32\x17.moego.utils.v2.OrderByR\x08orderBysB\n\n\x08_keyword\"\xf4\t\n\x0e\x43ustomerDetail\x12\x1d\n\nfirst_name\x18\x01 \x01(\tR\tfirstName\x12\x1b\n\tlast_name\x18\x02 \x01(\tR\x08lastName\x12!\n\x0cphone_number\x18\x03 \x01(\tR\x0bphoneNumber\x12\x1f\n\x0b\x62usiness_id\x18\x04 \x01(\x03R\nbusinessId\x12\x1f\n\x0b\x63ustomer_id\x18\x05 \x01(\x03R\ncustomerId\x12\x14\n\x05\x65mail\x18\x06 \x01(\tR\x05\x65mail\x12\x1f\n\x0b\x61vatar_path\x18\x07 \x01(\tR\navatarPath\x12!\n\x0c\x63lient_color\x18\x08 \x01(\tR\x0b\x63lientColor\x12,\n\x12referral_source_id\x18\t \x01(\x05R\x10referralSourceId\x12\x30\n\x14preferred_groomer_id\x18\n \x01(\x05R\x12preferredGroomerId\x12\x38\n\x18preferred_frequency_type\x18\x0b \x01(\x05R\x16preferredFrequencyType\x12\x36\n\x17preferred_frequency_day\x18\x0c \x01(\x05R\x15preferredFrequencyDay\x12#\n\rpreferred_day\x18\r \x03(\x05R\x0cpreferredDay\x12%\n\x0epreferred_time\x18\x0e \x03(\x05R\rpreferredTime\x12&\n\x0fis_new_customer\x18\x0f \x01(\x08R\risNewCustomer\x12X\n\x0fprimary_address\x18\x10 \x01(\x0b\x32*.moego.api.online_booking.v1.AddressDetailH\x00R\x0eprimaryAddress\x88\x01\x01\x12K\n\x0bnew_address\x18\x11 \x03(\x0b\x32*.moego.api.online_booking.v1.AddressDetailR\nnewAddress\x12\\\n\x10question_answers\x18\x12 \x03(\x0b\x32\x31.moego.api.online_booking.v1.QuestionAnswerDetailR\x0fquestionAnswers\x12+\n\x0flast_alert_note\x18\x13 \x01(\tH\x01R\rlastAlertNote\x88\x01\x01\x12-\n\x13out_of_service_area\x18\x14 \x01(\x08R\x10outOfServiceArea\x12:\n\x1ahas_pet_parent_app_account\x18\x15 \x01(\x08R\x16hasPetParentAppAccount\x12i\n\x11\x65mergency_contact\x18\x16 \x01(\x0b\x32<.moego.api.online_booking.v1.CustomerDetail.EmergencyContactR\x10\x65mergencyContact\x1aq\n\x10\x45mergencyContact\x12\x1d\n\nfirst_name\x18\x01 \x01(\tR\tfirstName\x12\x1b\n\tlast_name\x18\x02 \x01(\tR\x08lastName\x12!\n\x0cphone_number\x18\x03 \x01(\tR\x0bphoneNumberB\x12\n\x10_primary_addressB\x12\n\x10_last_alert_note\"\xcb\x01\n\x1dGetBookingRequestListResponse\x12\x66\n\x15\x62ooking_request_items\x18\x01 \x03(\x0b\x32\x32.moego.api.online_booking.v1.GetBookingRequestItemR\x13\x62ookingRequestItems\x12\x42\n\npagination\x18\x02 \x01(\x0b\x32\".moego.utils.v2.PaginationResponseR\npagination\"3\n\x18GetBookingRequestRequest\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\"\x84\x07\n\x15GetBookingRequestItem\x12Z\n\x0f\x62ooking_request\x18\x01 \x01(\x0b\x32\x31.moego.api.online_booking.v1.BookingRequestDetailR\x0e\x62ookingRequest\x12S\n\x0fservice_details\x18\x02 \x03(\x0b\x32*.moego.api.online_booking.v1.ServiceDetailR\x0eserviceDetails\x12T\n\x0f\x63ustomer_detail\x18\x03 \x01(\x0b\x32+.moego.api.online_booking.v1.CustomerDetailR\x0e\x63ustomerDetail\x12\x44\n\x03pay\x18\x04 \x01(\x0b\x32\x32.moego.api.online_booking.v1.PayBookingRequestViewR\x03pay\x12,\n\x12has_request_update\x18\x05 \x01(\x08R\x10hasRequestUpdate\x12U\n\x0e\x61ssign_require\x18\x06 \x01(\x0b\x32*.moego.api.online_booking.v1.AssignRequireB\x02\x18\x01R\rassignRequire\x12v\n\x18membership_subscriptions\x18\x07 \x01(\x0b\x32;.moego.models.membership.v1.MembershipSubscriptionListModelR\x17membershipSubscriptions\x12Z\n\x11\x63ustomer_packages\x18\x08 \x03(\x0b\x32-.moego.api.appointment.v1.CustomerPackageViewR\x10\x63ustomerPackages\x12]\n\x12incomplete_details\x18\t \x01(\x0b\x32..moego.api.online_booking.v1.IncompleteDetailsR\x11incompleteDetails\x12\x66\n\x13related_memberships\x18\n \x03(\x0b\x32\x35.moego.models.membership.v1.MembershipModelPublicViewR\x12relatedMemberships\"\xed\x05\n\rServiceDetail\x12\x45\n\npet_detail\x18\x01 \x01(\x0b\x32&.moego.api.online_booking.v1.PetDetailR\tpetDetail\x12N\n\x08services\x18\x02 \x03(\x0b\x32\x32.moego.api.online_booking.v1.ServiceDetail.ServiceR\x08services\x1a\xc4\x04\n\x07Service\x12J\n\x08grooming\x18\x01 \x01(\x0b\x32,.moego.api.online_booking.v1.GroomingServiceH\x00R\x08grooming\x12J\n\x08\x62oarding\x18\x02 \x01(\x0b\x32,.moego.api.online_booking.v1.BoardingServiceH\x00R\x08\x62oarding\x12G\n\x07\x64\x61ycare\x18\x03 \x01(\x0b\x32+.moego.api.online_booking.v1.DaycareServiceH\x00R\x07\x64\x61ycare\x12P\n\nevaluation\x18\x04 \x01(\x0b\x32..moego.api.online_booking.v1.EvaluationServiceH\x00R\nevaluation\x12Q\n\x0b\x64og_walking\x18\x06 \x01(\x0b\x32..moego.api.online_booking.v1.DogWalkingServiceH\x00R\ndogWalking\x12Q\n\x0bgroup_class\x18\x07 \x01(\x0b\x32..moego.api.online_booking.v1.GroupClassServiceH\x00R\ngroupClass\x12U\n\x11service_item_type\x18\x05 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x0fserviceItemTypeB\t\n\x07service\"\xe2\x07\n\x14\x42ookingRequestDetail\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n\x0b\x62usiness_id\x18\x02 \x01(\x03R\nbusinessId\x12\x1f\n\x0b\x63ustomer_id\x18\x03 \x01(\x03R\ncustomerId\x12%\n\x0e\x61ppointment_id\x18\x04 \x01(\x03R\rappointmentId\x12\x1d\n\nstart_date\x18\x05 \x01(\tR\tstartDate\x12\x1d\n\nstart_time\x18\x06 \x01(\x05R\tstartTime\x12\x19\n\x08\x65nd_date\x18\x07 \x01(\tR\x07\x65ndDate\x12\x19\n\x08\x65nd_time\x18\x08 \x01(\x05R\x07\x65ndTime\x12L\n\x06status\x18\t \x01(\x0e\x32\x34.moego.models.online_booking.v1.BookingRequestStatusR\x06status\x12\x1d\n\nis_prepaid\x18\n \x01(\x08R\tisPrepaid\x12\'\n\x0f\x61\x64\x64itional_note\x18\x0b \x01(\tR\x0e\x61\x64\x64itionalNote\x12\x65\n\x0fsource_platform\x18\x0c \x01(\x0e\x32<.moego.models.online_booking.v1.BookingRequestSourcePlatformR\x0esourcePlatform\x12\x30\n\x14service_type_include\x18\r \x01(\x05R\x12serviceTypeInclude\x12\x39\n\ncreated_at\x18\x14 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\tcreatedAt\x12\"\n\rno_start_time\x18\x15 \x01(\x08R\x0bnoStartTime\x12W\n\x12service_item_types\x18\x16 \x03(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x10serviceItemTypes\x12\"\n\nstaff_name\x18\x17 \x01(\tH\x00R\tstaffName\x88\x01\x01\x12\x1e\n\x08staff_id\x18\x18 \x01(\x03H\x01R\x07staffId\x88\x01\x01\x12%\n\x0especific_dates\x18\x19 \x03(\tR\rspecificDates\x12R\n\x06source\x18\x1a \x01(\x0e\x32:.moego.models.online_booking.v1.BookingRequestModel.SourceR\x06source\x12\x1b\n\tsource_id\x18\x1b \x01(\x03R\x08sourceIdB\r\n\x0b_staff_nameB\x0b\n\t_staff_id\"\xde\x05\n\x19GetBookingRequestResponse\x12Z\n\x0f\x62ooking_request\x18\x01 \x01(\x0b\x32\x31.moego.api.online_booking.v1.BookingRequestDetailR\x0e\x62ookingRequest\x12S\n\x0fservice_details\x18\x02 \x03(\x0b\x32*.moego.api.online_booking.v1.ServiceDetailR\x0eserviceDetails\x12T\n\x0f\x63ustomer_detail\x18\x03 \x01(\x0b\x32+.moego.api.online_booking.v1.CustomerDetailR\x0e\x63ustomerDetail\x12\x44\n\x03pay\x18\x04 \x01(\x0b\x32\x32.moego.api.online_booking.v1.PayBookingRequestViewR\x03pay\x12,\n\x12has_request_update\x18\x05 \x01(\x08R\x10hasRequestUpdate\x12\x44\n\x07\x61\x64\x64ress\x18\x06 \x01(\x0b\x32*.moego.api.online_booking.v1.AddressDetailR\x07\x61\x64\x64ress\x12U\n\x0e\x61ssign_require\x18\x07 \x01(\x0b\x32*.moego.api.online_booking.v1.AssignRequireB\x02\x18\x01R\rassignRequire\x12J\n\x05order\x18\x08 \x01(\x0b\x32\x34.moego.api.online_booking.v1.OrderBookingRequestViewR\x05order\x12]\n\x12incomplete_details\x18\t \x01(\x0b\x32..moego.api.online_booking.v1.IncompleteDetailsR\x11incompleteDetails\"\x96\x02\n\x0fGroomingService\x12L\n\x07service\x18\x01 \x01(\x0b\x32\x32.moego.api.online_booking.v1.GroomingServiceDetailR\x07service\x12H\n\x06\x61\x64\x64ons\x18\x02 \x03(\x0b\x32\x30.moego.api.online_booking.v1.GroomingAddOnDetailR\x06\x61\x64\x64ons\x12[\n\x0b\x61uto_assign\x18\x06 \x01(\x0b\x32\x35.moego.api.online_booking.v1.GroomingAutoAssignDetailH\x00R\nautoAssign\x88\x01\x01\x42\x0e\n\x0c_auto_assign\"\xe8\x04\n\x15GroomingServiceDetail\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12,\n\x12\x62ooking_request_id\x18\x02 \x01(\x03R\x10\x62ookingRequestId\x12\x15\n\x06pet_id\x18\x03 \x01(\x03R\x05petId\x12\x19\n\x08staff_id\x18\x04 \x01(\x03R\x07staffId\x12\x1d\n\nservice_id\x18\x05 \x01(\x03R\tserviceId\x12!\n\x0cservice_time\x18\x06 \x01(\x05R\x0bserviceTime\x12#\n\rservice_price\x18\x07 \x01(\x01R\x0cservicePrice\x12\x1d\n\nstart_date\x18\n \x01(\tR\tstartDate\x12\x1d\n\nstart_time\x18\x0b \x01(\x05R\tstartTime\x12\x19\n\x08\x65nd_date\x18\x0c \x01(\tR\x07\x65ndDate\x12\x19\n\x08\x65nd_time\x18\r \x01(\x05R\x07\x65ndTime\x12!\n\x0cservice_name\x18\x10 \x01(\tR\x0bserviceName\x12\x1d\n\nstaff_name\x18\x11 \x01(\tR\tstaffName\x12]\n\x13price_override_type\x18\x12 \x01(\x0e\x32-.moego.models.offering.v1.ServiceOverrideTypeR\x11priceOverrideType\x12\x63\n\x16\x64uration_override_type\x18\x13 \x01(\x0e\x32-.moego.models.offering.v1.ServiceOverrideTypeR\x14\x64urationOverrideType\"\xcb\x03\n\x13GroomingAddOnDetail\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12,\n\x12\x62ooking_request_id\x18\x02 \x01(\x03R\x10\x62ookingRequestId\x12*\n\x11service_detail_id\x18\x03 \x01(\x03R\x0fserviceDetailId\x12\x15\n\x06pet_id\x18\x04 \x01(\x03R\x05petId\x12\x19\n\x08staff_id\x18\x05 \x01(\x03R\x07staffId\x12\x1a\n\tadd_on_id\x18\x06 \x01(\x03R\x07\x61\x64\x64OnId\x12!\n\x0cservice_time\x18\x07 \x01(\x05R\x0bserviceTime\x12#\n\rservice_price\x18\x08 \x01(\x01R\x0cservicePrice\x12\x1d\n\nstart_date\x18\t \x01(\tR\tstartDate\x12\x1d\n\nstart_time\x18\n \x01(\x05R\tstartTime\x12\x19\n\x08\x65nd_date\x18\x0b \x01(\tR\x07\x65ndDate\x12\x19\n\x08\x65nd_time\x18\x0c \x01(\x05R\x07\x65ndTime\x12!\n\x0cservice_name\x18\x10 \x01(\tR\x0bserviceName\x12\x1d\n\nstaff_name\x18\x11 \x01(\tR\tstaffName\"\xd0\x04\n\x0f\x42oardingService\x12L\n\x07service\x18\x01 \x01(\x0b\x32\x32.moego.api.online_booking.v1.BoardingServiceDetailR\x07service\x12H\n\x06\x61\x64\x64ons\x18\x02 \x03(\x0b\x32\x30.moego.api.online_booking.v1.BoardingAddOnDetailR\x06\x61\x64\x64ons\x12H\n\x07\x66\x65\x65\x64ing\x18\x03 \x01(\x0b\x32*.moego.api.online_booking.v1.FeedingDetailB\x02\x18\x01R\x07\x66\x65\x65\x64ing\x12Q\n\nmedication\x18\x04 \x01(\x0b\x32-.moego.api.online_booking.v1.MedicationDetailB\x02\x18\x01R\nmedication\x12_\n\x0b\x61uto_assign\x18\x06 \x01(\x0b\x32\x35.moego.api.online_booking.v1.BoardingAutoAssignDetailB\x02\x18\x01H\x00R\nautoAssign\x88\x01\x01\x12\x46\n\x08\x66\x65\x65\x64ings\x18\x07 \x03(\x0b\x32*.moego.api.online_booking.v1.FeedingDetailR\x08\x66\x65\x65\x64ings\x12O\n\x0bmedications\x18\x08 \x03(\x0b\x32-.moego.api.online_booking.v1.MedicationDetailR\x0bmedicationsB\x0e\n\x0c_auto_assign\"\xd3\x04\n\x15\x42oardingServiceDetail\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12,\n\x12\x62ooking_request_id\x18\x02 \x01(\x03R\x10\x62ookingRequestId\x12\x15\n\x06pet_id\x18\x03 \x01(\x03R\x05petId\x12!\n\nlodging_id\x18\x04 \x01(\x03\x42\x02\x18\x01R\tlodgingId\x12\x1d\n\nservice_id\x18\x05 \x01(\x03R\tserviceId\x12%\n\x0especific_dates\x18\x06 \x03(\tR\rspecificDates\x12#\n\rservice_price\x18\x07 \x01(\x01R\x0cservicePrice\x12\x15\n\x06tax_id\x18\x08 \x01(\x03R\x05taxId\x12\x1d\n\nstart_date\x18\t \x01(\tR\tstartDate\x12\x1d\n\nstart_time\x18\n \x01(\x05R\tstartTime\x12\x19\n\x08\x65nd_date\x18\x0b \x01(\tR\x07\x65ndDate\x12\x19\n\x08\x65nd_time\x18\x0c \x01(\x05R\x07\x65ndTime\x12!\n\x0cservice_name\x18\x10 \x01(\tR\x0bserviceName\x12.\n\x11lodging_unit_name\x18\x11 \x01(\tB\x02\x18\x01R\x0flodgingUnitName\x12.\n\x11lodging_type_name\x18\x12 \x01(\tB\x02\x18\x01R\x0flodgingTypeName\x12I\n\nprice_unit\x18\x13 \x01(\x0e\x32*.moego.models.offering.v1.ServicePriceUnitR\tpriceUnit\"\xee\x04\n\x13\x42oardingAddOnDetail\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12,\n\x12\x62ooking_request_id\x18\x02 \x01(\x03R\x10\x62ookingRequestId\x12*\n\x11service_detail_id\x18\x03 \x01(\x03R\x0fserviceDetailId\x12\x15\n\x06pet_id\x18\x04 \x01(\x03R\x05petId\x12\x1a\n\tadd_on_id\x18\x05 \x01(\x03R\x07\x61\x64\x64OnId\x12%\n\x0especific_dates\x18\x06 \x03(\tR\rspecificDates\x12#\n\x0bis_everyday\x18\x07 \x01(\x08\x42\x02\x18\x01R\nisEveryday\x12#\n\rservice_price\x18\x08 \x01(\x01R\x0cservicePrice\x12\x15\n\x06tax_id\x18\t \x01(\x03R\x05taxId\x12\x1a\n\x08\x64uration\x18\n \x01(\x05R\x08\x64uration\x12!\n\x0cservice_name\x18\x10 \x01(\tR\x0bserviceName\x12(\n\x10quantity_per_day\x18\x11 \x01(\x05R\x0equantityPerDay\x12\x36\n\x17require_dedicated_staff\x18\x12 \x01(\x08R\x15requireDedicatedStaff\x12P\n\tdate_type\x18\r \x01(\x0e\x32..moego.models.appointment.v1.PetDetailDateTypeH\x00R\x08\x64\x61teType\x88\x01\x01\x12\"\n\nstart_date\x18\x0e \x01(\tH\x01R\tstartDate\x88\x01\x01\x42\x0c\n\n_date_typeB\r\n\x0b_start_date\"\xa2\x03\n\rFeedingDetail\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12,\n\x12\x62ooking_request_id\x18\x02 \x01(\x03R\x10\x62ookingRequestId\x12*\n\x11service_detail_id\x18\x03 \x01(\x03R\x0fserviceDetailId\x12P\n\x04time\x18\x05 \x03(\x0b\x32<.moego.models.online_booking.v1.FeedingModel.FeedingScheduleR\x04time\x12\x1a\n\x06\x61mount\x18\x06 \x01(\x01\x42\x02\x18\x01R\x06\x61mount\x12\x12\n\x04unit\x18\x07 \x01(\tR\x04unit\x12\x1b\n\tfood_type\x18\x08 \x03(\tR\x08\x66oodType\x12\x1f\n\x0b\x66ood_source\x18\t \x01(\tR\nfoodSource\x12 \n\x0binstruction\x18\n \x01(\tR\x0binstruction\x12\x12\n\x04note\x18\x0b \x01(\tR\x04note\x12\"\n\namount_str\x18\x0c \x01(\tH\x00R\tamountStr\x88\x01\x01\x42\r\n\x0b_amount_str\"\x84\x04\n\x10MedicationDetail\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12,\n\x12\x62ooking_request_id\x18\x02 \x01(\x03R\x10\x62ookingRequestId\x12*\n\x11service_detail_id\x18\x03 \x01(\x03R\x0fserviceDetailId\x12V\n\x04time\x18\x05 \x03(\x0b\x32\x42.moego.models.online_booking.v1.MedicationModel.MedicationScheduleR\x04time\x12\x1a\n\x06\x61mount\x18\x06 \x01(\x01\x42\x02\x18\x01R\x06\x61mount\x12\x12\n\x04unit\x18\x07 \x01(\tR\x04unit\x12\'\n\x0fmedication_name\x18\x08 \x01(\tR\x0emedicationName\x12\x14\n\x05notes\x18\t \x01(\tR\x05notes\x12\"\n\namount_str\x18\x0c \x01(\tH\x00R\tamountStr\x88\x01\x01\x12z\n\rselected_date\x18\r \x01(\x0b\x32P.moego.models.appointment.v1.AppointmentPetMedicationScheduleDef.SelectedDateDefH\x01R\x0cselectedDate\x88\x01\x01\x42\r\n\x0b_amount_strB\x10\n\x0e_selected_date\"\xdc\x03\n\x0e\x44\x61ycareService\x12K\n\x07service\x18\x01 \x01(\x0b\x32\x31.moego.api.online_booking.v1.DaycareServiceDetailR\x07service\x12G\n\x06\x61\x64\x64ons\x18\x02 \x03(\x0b\x32/.moego.api.online_booking.v1.DaycareAddOnDetailR\x06\x61\x64\x64ons\x12H\n\x07\x66\x65\x65\x64ing\x18\x03 \x01(\x0b\x32*.moego.api.online_booking.v1.FeedingDetailB\x02\x18\x01R\x07\x66\x65\x65\x64ing\x12Q\n\nmedication\x18\x04 \x01(\x0b\x32-.moego.api.online_booking.v1.MedicationDetailB\x02\x18\x01R\nmedication\x12\x46\n\x08\x66\x65\x65\x64ings\x18\x05 \x03(\x0b\x32*.moego.api.online_booking.v1.FeedingDetailR\x08\x66\x65\x65\x64ings\x12O\n\x0bmedications\x18\x06 \x03(\x0b\x32-.moego.api.online_booking.v1.MedicationDetailR\x0bmedications\"\xa7\x03\n\x14\x44\x61ycareServiceDetail\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12,\n\x12\x62ooking_request_id\x18\x02 \x01(\x03R\x10\x62ookingRequestId\x12\x15\n\x06pet_id\x18\x03 \x01(\x03R\x05petId\x12\x1d\n\nservice_id\x18\x04 \x01(\x03R\tserviceId\x12%\n\x0especific_dates\x18\x05 \x03(\tR\rspecificDates\x12#\n\rservice_price\x18\x06 \x01(\x01R\x0cservicePrice\x12\x15\n\x06tax_id\x18\x07 \x01(\x03R\x05taxId\x12!\n\x0cmax_duration\x18\x08 \x01(\x05R\x0bmaxDuration\x12\x1d\n\nstart_date\x18\t \x01(\tR\tstartDate\x12\x1d\n\nstart_time\x18\n \x01(\x05R\tstartTime\x12\x19\n\x08\x65nd_date\x18\x0b \x01(\tR\x07\x65ndDate\x12\x19\n\x08\x65nd_time\x18\x0c \x01(\x05R\x07\x65ndTime\x12!\n\x0cservice_name\x18\x10 \x01(\tR\x0bserviceName\"\xd6\x03\n\x12\x44\x61ycareAddOnDetail\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12,\n\x12\x62ooking_request_id\x18\x02 \x01(\x03R\x10\x62ookingRequestId\x12*\n\x11service_detail_id\x18\x03 \x01(\x03R\x0fserviceDetailId\x12\x15\n\x06pet_id\x18\x04 \x01(\x03R\x05petId\x12\x1a\n\tadd_on_id\x18\x05 \x01(\x03R\x07\x61\x64\x64OnId\x12%\n\x0especific_dates\x18\x06 \x03(\tR\rspecificDates\x12\x1f\n\x0bis_everyday\x18\x07 \x01(\x08R\nisEveryday\x12#\n\rservice_price\x18\x08 \x01(\x01R\x0cservicePrice\x12\x15\n\x06tax_id\x18\t \x01(\x03R\x05taxId\x12\x1a\n\x08\x64uration\x18\n \x01(\x05R\x08\x64uration\x12!\n\x0cservice_name\x18\x10 \x01(\tR\x0bserviceName\x12(\n\x10quantity_per_day\x18\x11 \x01(\x05R\x0equantityPerDay\x12\x36\n\x17require_dedicated_staff\x18\x12 \x01(\x08R\x15requireDedicatedStaff\"`\n\x11\x45valuationService\x12K\n\x07service\x18\x01 \x01(\x0b\x32\x31.moego.api.online_booking.v1.EvaluationTestDetailR\x07service\"\xb4\x03\n\x14\x45valuationTestDetail\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12,\n\x12\x62ooking_request_id\x18\x02 \x01(\x03R\x10\x62ookingRequestId\x12\x15\n\x06pet_id\x18\x03 \x01(\x03R\x05petId\x12#\n\revaluation_id\x18\x04 \x01(\x03R\x0c\x65valuationId\x12#\n\rservice_price\x18\x05 \x01(\x01R\x0cservicePrice\x12\x1a\n\x08\x64uration\x18\x06 \x01(\x05R\x08\x64uration\x12\x1d\n\nstart_date\x18\x07 \x01(\tR\tstartDate\x12\x1d\n\nstart_time\x18\x08 \x01(\x05R\tstartTime\x12\x19\n\x08\x65nd_date\x18\t \x01(\tR\x07\x65ndDate\x12\x19\n\x08\x65nd_time\x18\n \x01(\x05R\x07\x65ndTime\x12%\n\x0cservice_name\x18\x10 \x01(\tB\x02\x18\x01R\x0bserviceName\x12\'\n\x0f\x65valuation_name\x18\x11 \x01(\tR\x0e\x65valuationName\x12\x1d\n\nservice_id\x18\x12 \x01(\x03R\tserviceId\"c\n\x11\x44ogWalkingService\x12N\n\x07service\x18\x01 \x01(\x0b\x32\x34.moego.api.online_booking.v1.DogWalkingServiceDetailR\x07service\"\xea\x04\n\x17\x44ogWalkingServiceDetail\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12,\n\x12\x62ooking_request_id\x18\x02 \x01(\x03R\x10\x62ookingRequestId\x12\x15\n\x06pet_id\x18\x03 \x01(\x03R\x05petId\x12\x19\n\x08staff_id\x18\x04 \x01(\x03R\x07staffId\x12\x1d\n\nservice_id\x18\x05 \x01(\x03R\tserviceId\x12!\n\x0cservice_time\x18\x06 \x01(\x05R\x0bserviceTime\x12#\n\rservice_price\x18\x07 \x01(\x01R\x0cservicePrice\x12\x1d\n\nstart_date\x18\n \x01(\tR\tstartDate\x12\x1d\n\nstart_time\x18\x0b \x01(\x05R\tstartTime\x12\x19\n\x08\x65nd_date\x18\x0c \x01(\tR\x07\x65ndDate\x12\x19\n\x08\x65nd_time\x18\r \x01(\x05R\x07\x65ndTime\x12!\n\x0cservice_name\x18\x10 \x01(\tR\x0bserviceName\x12\x1d\n\nstaff_name\x18\x11 \x01(\tR\tstaffName\x12]\n\x13price_override_type\x18\x12 \x01(\x0e\x32-.moego.models.offering.v1.ServiceOverrideTypeR\x11priceOverrideType\x12\x63\n\x16\x64uration_override_type\x18\x13 \x01(\x0e\x32-.moego.models.offering.v1.ServiceOverrideTypeR\x14\x64urationOverrideType\"c\n\x11GroupClassService\x12N\n\x07service\x18\x01 \x01(\x0b\x32\x34.moego.api.online_booking.v1.GroupClassServiceDetailR\x07service\"\xca\x04\n\x17GroupClassServiceDetail\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12,\n\x12\x62ooking_request_id\x18\x02 \x01(\x03R\x10\x62ookingRequestId\x12\x15\n\x06pet_id\x18\x03 \x01(\x03R\x05petId\x12*\n\x11\x63lass_instance_id\x18\x04 \x01(\x03R\x0f\x63lassInstanceId\x12\x19\n\x08staff_id\x18\x05 \x01(\x03R\x07staffId\x12\x1d\n\nservice_id\x18\x06 \x01(\x03R\tserviceId\x12%\n\x0especific_dates\x18\x07 \x03(\tR\rspecificDates\x12\x1d\n\nstart_time\x18\x08 \x01(\x05R\tstartTime\x12\x19\n\x08\x65nd_time\x18\t \x01(\x05R\x07\x65ndTime\x12\x30\n\x14\x64uration_per_session\x18\n \x01(\x05R\x12\x64urationPerSession\x12!\n\x0cservice_name\x18\x0b \x01(\tR\x0bserviceName\x12\x1d\n\nstaff_name\x18\x0c \x01(\tR\tstaffName\x12#\n\rinstance_name\x18\r \x01(\tR\x0cinstanceName\x12!\n\x0cnum_sessions\x18\x0e \x01(\x05R\x0bnumSessions\x12W\n\noccurrence\x18\x0f \x01(\x0b\x32\x37.moego.models.offering.v1.GroupClassInstance.OccurrenceR\noccurrence\"\xfb\x08\n\tPetDetail\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x19\n\x08pet_name\x18\x04 \x01(\tR\x07petName\x12\x1f\n\x0b\x61vatar_path\x18\x05 \x01(\tR\navatarPath\x12<\n\x08pet_type\x18\x06 \x01(\x0e\x32!.moego.models.customer.v1.PetTypeR\x07petType\x12\x14\n\x05\x62reed\x18\x07 \x01(\tR\x05\x62reed\x12\x1f\n\x0b\x62reed_mixed\x18\n \x01(\x08R\nbreedMixed\x12;\n\x06gender\x18\x0b \x01(\x0e\x32#.moego.models.customer.v1.PetGenderR\x06gender\x12\x16\n\x06weight\x18\x08 \x01(\tR\x06weight\x12\x1b\n\tcoat_type\x18\t \x01(\tR\x08\x63oatType\x12\x14\n\x05\x66ixed\x18\x0c \x01(\tR\x05\x66ixed\x12\x1a\n\x08\x62\x65havior\x18\r \x01(\tR\x08\x62\x65havior\x12\x32\n\x08\x62irthday\x18\x0f \x01(\x0b\x32\x11.google.type.DateH\x00R\x08\x62irthday\x88\x01\x01\x12\x1f\n\x0bpassed_away\x18\x10 \x01(\x08R\npassedAway\x12\x18\n\x07\x64\x65leted\x18\x11 \x01(\x08R\x07\x64\x65leted\x12/\n\x13\x65xpiry_notification\x18\x14 \x01(\x08R\x12\x65xpiryNotification\x12\x19\n\x08vet_name\x18\x1e \x01(\tR\x07vetName\x12(\n\x10vet_phone_number\x18\x1f \x01(\tR\x0evetPhoneNumber\x12\x1f\n\x0bvet_address\x18  \x01(\tR\nvetAddress\x12\x34\n\x16\x65mergency_contact_name\x18! \x01(\tR\x14\x65mergencyContactName\x12\x43\n\x1e\x65mergency_contact_phone_number\x18\" \x01(\tR\x1b\x65mergencyContactPhoneNumber\x12#\n\rhealth_issues\x18# \x01(\tR\x0chealthIssues\x12W\n\x11\x65valuation_status\x18$ \x01(\x0e\x32*.moego.models.customer.v1.EvaluationStatusR\x10\x65valuationStatus\x12J\n\tpet_codes\x18% \x03(\x0b\x32-.moego.api.online_booking.v1.PetCodeCompositeR\x08petCodes\x12\\\n\x10question_answers\x18& \x03(\x0b\x32\x31.moego.api.online_booking.v1.QuestionAnswerDetailR\x0fquestionAnswers\x12S\n\x0cpet_vaccines\x18\' \x03(\x0b\x32\x30.moego.api.online_booking.v1.PetVaccineCompositeR\x0bpetVaccinesB\x0b\n\t_birthday\"\xff\x01\n\x13PetVaccineComposite\x12,\n\x12vaccine_binding_id\x18\x01 \x01(\x03R\x10vaccineBindingId\x12\x1d\n\nvaccine_id\x18\x03 \x01(\x03R\tvaccineId\x12?\n\x0f\x65xpiration_date\x18\x04 \x01(\x0b\x32\x11.google.type.DateH\x00R\x0e\x65xpirationDate\x88\x01\x01\x12#\n\rdocument_urls\x18\x05 \x03(\tR\x0c\x64ocumentUrls\x12!\n\x0cvaccine_name\x18\x06 \x01(\tR\x0bvaccineNameB\x12\n\x10_expiration_date\"\xac\x01\n\x10PetCodeComposite\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\"\n\x0c\x61\x62\x62reviation\x18\x02 \x01(\tR\x0c\x61\x62\x62reviation\x12 \n\x0b\x64\x65scription\x18\x04 \x01(\tR\x0b\x64\x65scription\x12\x14\n\x05\x63olor\x18\x05 \x01(\tR\x05\x63olor\x12\x12\n\x04sort\x18\x06 \x01(\x05R\x04sort\x12\x18\n\x07\x64\x65leted\x18\x07 \x01(\x08R\x07\x64\x65leted\"\xfe\x02\n\x15PayBookingRequestView\x12$\n\x0bpaid_amount\x18\x01 \x01(\x01H\x00R\npaidAmount\x88\x01\x01\x12)\n\x0epre_pay_amount\x18\x02 \x01(\x01H\x01R\x0cprePayAmount\x88\x01\x01\x12(\n\rrefund_amount\x18\x03 \x01(\x01H\x02R\x0crefundAmount\x88\x01\x01\x12)\n\x0epre_pay_status\x18\x04 \x01(\x05H\x03R\x0cprePayStatus\x88\x01\x01\x12%\n\x0cpre_pay_rate\x18\x05 \x01(\x01H\x04R\nprePayRate\x88\x01\x01\x12+\n\x0fpre_auth_enable\x18\x06 \x01(\x08H\x05R\rpreAuthEnable\x88\x01\x01\x42\x0e\n\x0c_paid_amountB\x11\n\x0f_pre_pay_amountB\x10\n\x0e_refund_amountB\x11\n\x0f_pre_pay_statusB\x0f\n\r_pre_pay_rateB\x12\n\x10_pre_auth_enable\"\x89\x03\n\rAddressDetail\x12\x1d\n\naddress_id\x18\x01 \x01(\x05R\taddressId\x12\x1a\n\x08\x61\x64\x64ress1\x18\x02 \x01(\tR\x08\x61\x64\x64ress1\x12\x1a\n\x08\x61\x64\x64ress2\x18\x03 \x01(\tR\x08\x61\x64\x64ress2\x12\x12\n\x04\x63ity\x18\x04 \x01(\tR\x04\x63ity\x12\x14\n\x05state\x18\x05 \x01(\tR\x05state\x12\x18\n\x07zipcode\x18\x06 \x01(\tR\x07zipcode\x12\x18\n\x07\x63ountry\x18\x07 \x01(\tR\x07\x63ountry\x12\x10\n\x03lat\x18\x08 \x01(\tR\x03lat\x12\x10\n\x03lng\x18\t \x01(\tR\x03lng\x12\x1d\n\nis_primary\x18\n \x01(\x08R\tisPrimary\x12@\n\x1ais_profile_request_address\x18\x0b \x01(\x08H\x00R\x17isProfileRequestAddress\x88\x01\x01\x12\x1f\n\x0b\x63ustomer_id\x18\x0c \x01(\x03R\ncustomerIdB\x1d\n\x1b_is_profile_request_address\"\\\n\x14QuestionAnswerDetail\x12\x10\n\x03key\x18\x01 \x01(\tR\x03key\x12\x1a\n\x08question\x18\x02 \x01(\tR\x08question\x12\x16\n\x06\x61nswer\x18\x03 \x01(\tR\x06\x61nswer\"\x97\x01\n\x18GroomingAutoAssignDetail\x12\x0e\n\x02id\x18\x01 \x01(\x05R\x02id\x12%\n\x0e\x61ppointment_id\x18\x02 \x01(\x05R\rappointmentId\x12\x19\n\x08staff_id\x18\x03 \x01(\x05R\x07staffId\x12)\n\x10\x61ppointment_time\x18\x04 \x01(\x05R\x0f\x61ppointmentTime\"\xb4\x01\n\x18\x42oardingAutoAssignDetail\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12,\n\x12\x62ooking_request_id\x18\x02 \x01(\x03R\x10\x62ookingRequestId\x12;\n\x1a\x62oarding_service_detail_id\x18\x03 \x01(\x03R\x17\x62oardingServiceDetailId\x12\x1d\n\nlodging_id\x18\x04 \x01(\x03R\tlodgingId\"\xd8\x01\n\rAssignRequire\x12\x64\n\x14room_assign_requires\x18\x06 \x03(\x0b\x32..moego.api.online_booking.v1.RoomAssignRequireB\x02\x18\x01R\x12roomAssignRequires\x12\x61\n\x14staff_assign_require\x18\x07 \x03(\x0b\x32/.moego.api.online_booking.v1.StaffAssignRequireR\x12staffAssignRequire\"\x89\x12\n\x11IncompleteDetails\x12k\n\x11grooming_services\x18\x01 \x03(\x0b\x32>.moego.api.online_booking.v1.IncompleteDetails.GroomingServiceR\x10groomingServices\x12k\n\x11\x62oarding_services\x18\x02 \x03(\x0b\x32>.moego.api.online_booking.v1.IncompleteDetails.BoardingServiceR\x10\x62oardingServices\x12h\n\x10\x64\x61ycare_services\x18\x03 \x03(\x0b\x32=.moego.api.online_booking.v1.IncompleteDetails.DaycareServiceR\x0f\x64\x61ycareServices\x12q\n\x13\x65valuation_services\x18\x04 \x03(\x0b\<EMAIL>.online_booking.v1.IncompleteDetails.EvaluationServiceR\x12\x65valuationServices\x12\x65\n\x0fgrooming_addons\x18\x05 \x03(\x0b\x32<.moego.api.online_booking.v1.IncompleteDetails.GroomingAddonR\x0egroomingAddons\x12\x65\n\x0f\x62oarding_addons\x18\x06 \x03(\x0b\x32<.moego.api.online_booking.v1.IncompleteDetails.BoardingAddonR\x0e\x62oardingAddons\x12\x62\n\x0e\x64\x61ycare_addons\x18\x07 \x03(\x0b\x32;.moego.api.online_booking.v1.IncompleteDetails.DaycareAddonR\rdaycareAddons\x1a\xba\x01\n\x0fGroomingService\x12\x61\n\x0eservice_detail\x18\x01 \x01(\x0b\x32:.moego.models.online_booking.v1.GroomingServiceDetailModelR\rserviceDetail\x12\x44\n\x07service\x18\x02 \x01(\x0b\x32*.moego.models.offering.v1.ServiceBriefViewR\x07service\x1a\xb4\x02\n\x0f\x42oardingService\x12\x61\n\x0eservice_detail\x18\x01 \x01(\x0b\x32:.moego.models.online_booking.v1.BoardingServiceDetailModelR\rserviceDetail\x12\x44\n\x07service\x18\x02 \x01(\x0b\x32*.moego.models.offering.v1.ServiceBriefViewR\x07service\x12\x61\n\x12missing_evaluation\x18\x03 \x01(\x0b\x32-.moego.models.offering.v1.EvaluationBriefViewH\x00R\x11missingEvaluation\x88\x01\x01\x42\x15\n\x13_missing_evaluation\x1a\xb2\x02\n\x0e\x44\x61ycareService\x12`\n\x0eservice_detail\x18\x01 \x01(\x0b\x32\x39.moego.models.online_booking.v1.DaycareServiceDetailModelR\rserviceDetail\x12\x44\n\x07service\x18\x02 \x01(\x0b\x32*.moego.models.offering.v1.ServiceBriefViewR\x07service\x12\x61\n\x12missing_evaluation\x18\x03 \x01(\x0b\x32-.moego.models.offering.v1.EvaluationBriefViewH\x00R\x11missingEvaluation\x88\x01\x01\x42\x15\n\x13_missing_evaluation\x1a\xc4\x01\n\x11\x45valuationService\x12`\n\x0eservice_detail\x18\x01 \x01(\x0b\x32\x39.moego.models.online_booking.v1.EvaluationTestDetailModelR\rserviceDetail\x12M\n\nevaluation\x18\x02 \x01(\x0b\x32-.moego.models.offering.v1.EvaluationBriefViewR\nevaluation\x1a\xb2\x01\n\rGroomingAddon\x12[\n\x0c\x61\x64\x64on_detail\x18\x01 \x01(\x0b\x32\x38.moego.models.online_booking.v1.GroomingAddOnDetailModelR\x0b\x61\x64\x64onDetail\x12\x44\n\x07service\x18\x02 \x01(\x0b\x32*.moego.models.offering.v1.ServiceBriefViewR\x07service\x1a\xb2\x01\n\rBoardingAddon\x12[\n\x0c\x61\x64\x64on_detail\x18\x01 \x01(\x0b\x32\x38.moego.models.online_booking.v1.BoardingAddOnDetailModelR\x0b\x61\x64\x64onDetail\x12\x44\n\x07service\x18\x02 \x01(\x0b\x32*.moego.models.offering.v1.ServiceBriefViewR\x07service\x1a\xb0\x01\n\x0c\x44\x61ycareAddon\x12Z\n\x0c\x61\x64\x64on_detail\x18\x01 \x01(\x0b\x32\x37.moego.models.online_booking.v1.DaycareAddOnDetailModelR\x0b\x61\x64\x64onDetail\x12\x44\n\x07service\x18\x02 \x01(\x0b\x32*.moego.models.offering.v1.ServiceBriefViewR\x07service\"\xa2\x01\n\x11RoomAssignRequire\x12\x15\n\x06pet_id\x18\x01 \x01(\x03R\x05petId\x12\x19\n\x08pet_name\x18\x02 \x01(\tR\x07petName\x12\x1d\n\nservice_id\x18\x03 \x01(\x03R\tserviceId\x12\x1d\n\nstart_date\x18\x04 \x01(\tR\tstartDate\x12\x19\n\x08\x65nd_date\x18\x05 \x01(\tR\x07\x65ndDate:\x02\x18\x01\"\xcb\x01\n\x12StaffAssignRequire\x12\x15\n\x06pet_id\x18\x01 \x01(\x03R\x05petId\x12\x19\n\x08pet_name\x18\x02 \x01(\tR\x07petName\x12\x1d\n\nservice_id\x18\x03 \x01(\x03R\tserviceId\x12\x1a\n\x08\x64uration\x18\x06 \x01(\x05R\x08\x64uration\x12!\n\x0cservice_name\x18\x07 \x01(\tR\x0bserviceName\x12%\n\x0especific_dates\x18\x08 \x03(\tR\rspecificDates\"G\n\x17OrderBookingRequestView\x12,\n\x12\x64iscount_code_name\x18\x01 \x01(\tR\x10\x64iscountCodeName\"\x8b\x03\n\x1b\x41\x63\x63\x65ptBookingRequestRequest\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12Q\n\x0fpet_to_lodgings\x18\x02 \x03(\x0b\x32).moego.api.online_booking.v1.PetToLodgingR\rpetToLodgings\x12K\n\rpet_to_staffs\x18\x03 \x03(\x0b\x32\'.moego.api.online_booking.v1.PetToStaffR\x0bpetToStaffs\x12Q\n\x0fpet_to_services\x18\x04 \x03(\x0b\x32).moego.api.online_booking.v1.PetToServiceR\rpetToServices\x12`\n\x18\x65valuation_pet_to_staffs\x18\x05 \x03(\x0b\x32\'.moego.api.online_booking.v1.PetToStaffR\x15\x65valuationPetToStaffs\"_\n\x0cPetToLodging\x12\x1e\n\x06pet_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x05petId\x12/\n\x0flodging_unit_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\rlodgingUnitId\"\xa3\x01\n\nPetToStaff\x12\x1e\n\x06pet_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x05petId\x12&\n\nservice_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tserviceId\x12\"\n\x08staff_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x07staffId\x12)\n\nstart_time\x18\x04 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x10\xa0\x0b(\x00R\tstartTime\"\xfc\x01\n\x0cPetToService\x12\x1e\n\x06pet_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x05petId\x12I\n\x1a\x66rom_evaluation_service_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\x17\x66romEvaluationServiceId\x88\x01\x01\x12\x45\n\x18to_evaluation_service_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x01R\x15toEvaluationServiceId\x88\x01\x01\x42\x1d\n\x1b_from_evaluation_service_idB\x1b\n\x19_to_evaluation_service_id\"J\n\x11\x41utoAssignRequest\x12\x35\n\x12\x62ooking_request_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x10\x62ookingRequestId\"\xfb\x07\n\x12\x41utoAssignResponse\x12w\n\x18\x62oarding_assign_requires\x18\x01 \x03(\x0b\x32=.moego.api.online_booking.v1.AutoAssignResponse.AssignRequireR\x16\x62oardingAssignRequires\x12Q\n\x0fpet_to_lodgings\x18\x02 \x03(\x0b\x32).moego.api.online_booking.v1.PetToLodgingR\rpetToLodgings\x12{\n\x1a\x65valuation_assign_requires\x18\x03 \x03(\x0b\x32=.moego.api.online_booking.v1.AutoAssignResponse.AssignRequireR\x18\x65valuationAssignRequires\x12`\n\x18\x65valuation_pet_to_staffs\x18\x04 \x03(\x0b\x32\'.moego.api.online_booking.v1.PetToStaffR\x15\x65valuationPetToStaffs\x12Y\n\x08lodgings\x18\x0b \x03(\x0b\x32=.moego.api.online_booking.v1.AutoAssignResponse.LodgingDetailR\x08lodgings\x12S\n\x06staffs\x18\x0c \x03(\x0b\x32;.moego.api.online_booking.v1.AutoAssignResponse.StaffDetailR\x06staffs\x1a\xa5\x01\n\rAssignRequire\x12\x15\n\x06pet_id\x18\x01 \x01(\x03R\x05petId\x12\x1d\n\nservice_id\x18\x02 \x01(\x03R\tserviceId\x12\"\n\nstart_date\x18\x03 \x01(\tH\x00R\tstartDate\x88\x01\x01\x12\x1e\n\x08\x65nd_date\x18\x04 \x01(\tH\x01R\x07\x65ndDate\x88\x01\x01\x42\r\n\x0b_start_dateB\x0b\n\t_end_date\x1a\x86\x01\n\rLodgingDetail\x12\x1d\n\nlodging_id\x18\x01 \x01(\x03R\tlodgingId\x12*\n\x11lodging_unit_name\x18\x02 \x01(\tR\x0flodgingUnitName\x12*\n\x11lodging_type_name\x18\x03 \x01(\tR\x0flodgingTypeName\x1aY\n\x0bStaffDetail\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1d\n\nfirst_name\x18\x02 \x01(\tR\tfirstName\x12\x1b\n\tlast_name\x18\x03 \x01(\tR\x08lastName\"]\n\x1c\x41\x63\x63\x65ptBookingRequestResponse\x12\x16\n\x06result\x18\x01 \x01(\x08R\x06result\x12%\n\x0e\x61ppointment_id\x18\x02 \x01(\x03R\rappointmentId\"7\n\x1c\x44\x65\x63lineBookingRequestRequest\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\"^\n\x1d\x44\x65\x63lineBookingRequestResponse\x12\x16\n\x06result\x18\x01 \x01(\x08R\x06result\x12%\n\x0e\x61ppointment_id\x18\x02 \x01(\x03R\rappointmentId\"\xa9\x08\n\x1d\x41\x63\x63\x65ptBookingRequestV2Request\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12{\n\x11grooming_services\x18\x02 \x03(\x0b\x32N.moego.service.online_booking.v1.AcceptBookingRequestV2Request.GroomingServiceR\x10groomingServices\x12{\n\x11\x62oarding_services\x18\x03 \x03(\x0b\x32N.moego.service.online_booking.v1.AcceptBookingRequestV2Request.BoardingServiceR\x10\x62oardingServices\x12x\n\x10\x64\x61ycare_services\x18\x04 \x03(\x0b\x32M.moego.service.online_booking.v1.AcceptBookingRequestV2Request.DaycareServiceR\x0f\x64\x61ycareServices\x12\x81\x01\n\x13\x65valuation_services\x18\x05 \x03(\x0b\x32P.moego.service.online_booking.v1.AcceptBookingRequestV2Request.EvaluationServiceR\x12\x65valuationServices\x12u\n\x0fgrooming_addons\x18\x0f \x03(\x0b\x32L.moego.service.online_booking.v1.AcceptBookingRequestV2Request.GroomingAddonR\x0egroomingAddons\x12u\n\x0f\x62oarding_addons\x18\x10 \x03(\x0b\x32L.moego.service.online_booking.v1.AcceptBookingRequestV2Request.BoardingAddonR\x0e\x62oardingAddons\x12r\n\x0e\x64\x61ycare_addons\x18\x11 \x03(\x0b\x32K.moego.service.online_booking.v1.AcceptBookingRequestV2Request.DaycareAddonR\rdaycareAddons\x12\x94\x01\n\x1a\x63reate_evaluation_requests\x18\x12 \x03(\x0b\x32V.moego.service.online_booking.v1.AcceptBookingRequestV2Request.CreateEvaluationRequestR\x18\x63reateEvaluationRequests\"_\n\x1e\x41\x63\x63\x65ptBookingRequestV2Response\x12\x16\n\x06result\x18\x01 \x01(\x08R\x06result\x12%\n\x0e\x61ppointment_id\x18\x02 \x01(\x03R\rappointmentId\"L\n\x13\x41utoAssignV2Request\x12\x35\n\x12\x62ooking_request_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x10\x62ookingRequestId\"\x80\x02\n\x14\x41utoAssignV2Response\x12p\n\x11\x62oarding_services\x18\x01 \x03(\x0b\x32\x43.moego.service.online_booking.v1.AutoAssignResponse.BoardingServiceR\x10\x62oardingServices\x12v\n\x13\x65valuation_services\x18\x02 \x03(\x0b\x32\x45.moego.service.online_booking.v1.AutoAssignResponse.EvaluationServiceR\x12\x65valuationServices\"\x85\x01\n\"MoveBookingRequestToWaitlistParams\x12(\n\x0b\x62usiness_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12\x35\n\x12\x62ooking_request_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x10\x62ookingRequestId\"$\n\"MoveBookingRequestToWaitlistResult2\xf1\x08\n\x15\x42ookingRequestService\x12\x8e\x01\n\x15GetBookingRequestList\x12\x39.moego.api.online_booking.v1.GetBookingRequestListRequest\x1a:.moego.api.online_booking.v1.GetBookingRequestListResponse\x12\x82\x01\n\x11GetBookingRequest\x12\x35.moego.api.online_booking.v1.GetBookingRequestRequest\x1a\x36.moego.api.online_booking.v1.GetBookingRequestResponse\x12r\n\nAutoAssign\x12..moego.api.online_booking.v1.AutoAssignRequest\x1a/.moego.api.online_booking.v1.AutoAssignResponse\"\x03\x88\x02\x01\x12s\n\x0c\x41utoAssignV2\x12\x30.moego.api.online_booking.v1.AutoAssignV2Request\x1a\x31.moego.api.online_booking.v1.AutoAssignV2Response\x12\x90\x01\n\x14\x41\x63\x63\x65ptBookingRequest\x12\x38.moego.api.online_booking.v1.AcceptBookingRequestRequest\x1a\x39.moego.api.online_booking.v1.AcceptBookingRequestResponse\"\x03\x88\x02\x01\x12\x91\x01\n\x16\x41\x63\x63\x65ptBookingRequestV2\x12:.moego.api.online_booking.v1.AcceptBookingRequestV2Request\x1a;.moego.api.online_booking.v1.AcceptBookingRequestV2Response\x12\x8e\x01\n\x15\x44\x65\x63lineBookingRequest\x12\x39.moego.api.online_booking.v1.DeclineBookingRequestRequest\x1a:.moego.api.online_booking.v1.DeclineBookingRequestResponse\x12\xa0\x01\n\x1cMoveBookingRequestToWaitlist\x12?.moego.api.online_booking.v1.MoveBookingRequestToWaitlistParams\x1a?.moego.api.online_booking.v1.MoveBookingRequestToWaitlistResultB\x8c\x01\n#com.moego.idl.api.online_booking.v1P\x01Zcgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/online_booking/v1;onlinebookingapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.api.online_booking.v1.booking_request_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n#com.moego.idl.api.online_booking.v1P\001Zcgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/online_booking/v1;onlinebookingapipb'
  _globals['_GETBOOKINGREQUESTLISTREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_GETBOOKINGREQUESTLISTREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETBOOKINGREQUESTLISTREQUEST'].fields_by_name['pagination']._loaded_options = None
  _globals['_GETBOOKINGREQUESTLISTREQUEST'].fields_by_name['pagination']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_GETBOOKINGREQUESTLISTREQUEST'].fields_by_name['keyword']._loaded_options = None
  _globals['_GETBOOKINGREQUESTLISTREQUEST'].fields_by_name['keyword']._serialized_options = b'\372B\004r\002\0302'
  _globals['_GETBOOKINGREQUESTREQUEST'].fields_by_name['id']._loaded_options = None
  _globals['_GETBOOKINGREQUESTREQUEST'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETBOOKINGREQUESTITEM'].fields_by_name['assign_require']._loaded_options = None
  _globals['_GETBOOKINGREQUESTITEM'].fields_by_name['assign_require']._serialized_options = b'\030\001'
  _globals['_GETBOOKINGREQUESTRESPONSE'].fields_by_name['assign_require']._loaded_options = None
  _globals['_GETBOOKINGREQUESTRESPONSE'].fields_by_name['assign_require']._serialized_options = b'\030\001'
  _globals['_BOARDINGSERVICE'].fields_by_name['feeding']._loaded_options = None
  _globals['_BOARDINGSERVICE'].fields_by_name['feeding']._serialized_options = b'\030\001'
  _globals['_BOARDINGSERVICE'].fields_by_name['medication']._loaded_options = None
  _globals['_BOARDINGSERVICE'].fields_by_name['medication']._serialized_options = b'\030\001'
  _globals['_BOARDINGSERVICE'].fields_by_name['auto_assign']._loaded_options = None
  _globals['_BOARDINGSERVICE'].fields_by_name['auto_assign']._serialized_options = b'\030\001'
  _globals['_BOARDINGSERVICEDETAIL'].fields_by_name['lodging_id']._loaded_options = None
  _globals['_BOARDINGSERVICEDETAIL'].fields_by_name['lodging_id']._serialized_options = b'\030\001'
  _globals['_BOARDINGSERVICEDETAIL'].fields_by_name['lodging_unit_name']._loaded_options = None
  _globals['_BOARDINGSERVICEDETAIL'].fields_by_name['lodging_unit_name']._serialized_options = b'\030\001'
  _globals['_BOARDINGSERVICEDETAIL'].fields_by_name['lodging_type_name']._loaded_options = None
  _globals['_BOARDINGSERVICEDETAIL'].fields_by_name['lodging_type_name']._serialized_options = b'\030\001'
  _globals['_BOARDINGADDONDETAIL'].fields_by_name['is_everyday']._loaded_options = None
  _globals['_BOARDINGADDONDETAIL'].fields_by_name['is_everyday']._serialized_options = b'\030\001'
  _globals['_FEEDINGDETAIL'].fields_by_name['amount']._loaded_options = None
  _globals['_FEEDINGDETAIL'].fields_by_name['amount']._serialized_options = b'\030\001'
  _globals['_MEDICATIONDETAIL'].fields_by_name['amount']._loaded_options = None
  _globals['_MEDICATIONDETAIL'].fields_by_name['amount']._serialized_options = b'\030\001'
  _globals['_DAYCARESERVICE'].fields_by_name['feeding']._loaded_options = None
  _globals['_DAYCARESERVICE'].fields_by_name['feeding']._serialized_options = b'\030\001'
  _globals['_DAYCARESERVICE'].fields_by_name['medication']._loaded_options = None
  _globals['_DAYCARESERVICE'].fields_by_name['medication']._serialized_options = b'\030\001'
  _globals['_EVALUATIONTESTDETAIL'].fields_by_name['service_name']._loaded_options = None
  _globals['_EVALUATIONTESTDETAIL'].fields_by_name['service_name']._serialized_options = b'\030\001'
  _globals['_ASSIGNREQUIRE'].fields_by_name['room_assign_requires']._loaded_options = None
  _globals['_ASSIGNREQUIRE'].fields_by_name['room_assign_requires']._serialized_options = b'\030\001'
  _globals['_ROOMASSIGNREQUIRE']._loaded_options = None
  _globals['_ROOMASSIGNREQUIRE']._serialized_options = b'\030\001'
  _globals['_ACCEPTBOOKINGREQUESTREQUEST'].fields_by_name['id']._loaded_options = None
  _globals['_ACCEPTBOOKINGREQUESTREQUEST'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_PETTOLODGING'].fields_by_name['pet_id']._loaded_options = None
  _globals['_PETTOLODGING'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_PETTOLODGING'].fields_by_name['lodging_unit_id']._loaded_options = None
  _globals['_PETTOLODGING'].fields_by_name['lodging_unit_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_PETTOSTAFF'].fields_by_name['pet_id']._loaded_options = None
  _globals['_PETTOSTAFF'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_PETTOSTAFF'].fields_by_name['service_id']._loaded_options = None
  _globals['_PETTOSTAFF'].fields_by_name['service_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_PETTOSTAFF'].fields_by_name['staff_id']._loaded_options = None
  _globals['_PETTOSTAFF'].fields_by_name['staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_PETTOSTAFF'].fields_by_name['start_time']._loaded_options = None
  _globals['_PETTOSTAFF'].fields_by_name['start_time']._serialized_options = b'\372B\007\032\005\020\240\013(\000'
  _globals['_PETTOSERVICE'].fields_by_name['pet_id']._loaded_options = None
  _globals['_PETTOSERVICE'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_PETTOSERVICE'].fields_by_name['from_evaluation_service_id']._loaded_options = None
  _globals['_PETTOSERVICE'].fields_by_name['from_evaluation_service_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_PETTOSERVICE'].fields_by_name['to_evaluation_service_id']._loaded_options = None
  _globals['_PETTOSERVICE'].fields_by_name['to_evaluation_service_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_AUTOASSIGNREQUEST'].fields_by_name['booking_request_id']._loaded_options = None
  _globals['_AUTOASSIGNREQUEST'].fields_by_name['booking_request_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_DECLINEBOOKINGREQUESTREQUEST'].fields_by_name['id']._loaded_options = None
  _globals['_DECLINEBOOKINGREQUESTREQUEST'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST'].fields_by_name['id']._loaded_options = None
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_AUTOASSIGNV2REQUEST'].fields_by_name['booking_request_id']._loaded_options = None
  _globals['_AUTOASSIGNV2REQUEST'].fields_by_name['booking_request_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_MOVEBOOKINGREQUESTTOWAITLISTPARAMS'].fields_by_name['business_id']._loaded_options = None
  _globals['_MOVEBOOKINGREQUESTTOWAITLISTPARAMS'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_MOVEBOOKINGREQUESTTOWAITLISTPARAMS'].fields_by_name['booking_request_id']._loaded_options = None
  _globals['_MOVEBOOKINGREQUESTTOWAITLISTPARAMS'].fields_by_name['booking_request_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_BOOKINGREQUESTSERVICE'].methods_by_name['AutoAssign']._loaded_options = None
  _globals['_BOOKINGREQUESTSERVICE'].methods_by_name['AutoAssign']._serialized_options = b'\210\002\001'
  _globals['_BOOKINGREQUESTSERVICE'].methods_by_name['AcceptBookingRequest']._loaded_options = None
  _globals['_BOOKINGREQUESTSERVICE'].methods_by_name['AcceptBookingRequest']._serialized_options = b'\210\002\001'
  _globals['_GETBOOKINGREQUESTLISTREQUEST']._serialized_start=1549
  _globals['_GETBOOKINGREQUESTLISTREQUEST']._serialized_end=1804
  _globals['_CUSTOMERDETAIL']._serialized_start=1807
  _globals['_CUSTOMERDETAIL']._serialized_end=3075
  _globals['_CUSTOMERDETAIL_EMERGENCYCONTACT']._serialized_start=2922
  _globals['_CUSTOMERDETAIL_EMERGENCYCONTACT']._serialized_end=3035
  _globals['_GETBOOKINGREQUESTLISTRESPONSE']._serialized_start=3078
  _globals['_GETBOOKINGREQUESTLISTRESPONSE']._serialized_end=3281
  _globals['_GETBOOKINGREQUESTREQUEST']._serialized_start=3283
  _globals['_GETBOOKINGREQUESTREQUEST']._serialized_end=3334
  _globals['_GETBOOKINGREQUESTITEM']._serialized_start=3337
  _globals['_GETBOOKINGREQUESTITEM']._serialized_end=4237
  _globals['_SERVICEDETAIL']._serialized_start=4240
  _globals['_SERVICEDETAIL']._serialized_end=4989
  _globals['_SERVICEDETAIL_SERVICE']._serialized_start=4409
  _globals['_SERVICEDETAIL_SERVICE']._serialized_end=4989
  _globals['_BOOKINGREQUESTDETAIL']._serialized_start=4992
  _globals['_BOOKINGREQUESTDETAIL']._serialized_end=5986
  _globals['_GETBOOKINGREQUESTRESPONSE']._serialized_start=5989
  _globals['_GETBOOKINGREQUESTRESPONSE']._serialized_end=6723
  _globals['_GROOMINGSERVICE']._serialized_start=6726
  _globals['_GROOMINGSERVICE']._serialized_end=7004
  _globals['_GROOMINGSERVICEDETAIL']._serialized_start=7007
  _globals['_GROOMINGSERVICEDETAIL']._serialized_end=7623
  _globals['_GROOMINGADDONDETAIL']._serialized_start=7626
  _globals['_GROOMINGADDONDETAIL']._serialized_end=8085
  _globals['_BOARDINGSERVICE']._serialized_start=8088
  _globals['_BOARDINGSERVICE']._serialized_end=8680
  _globals['_BOARDINGSERVICEDETAIL']._serialized_start=8683
  _globals['_BOARDINGSERVICEDETAIL']._serialized_end=9278
  _globals['_BOARDINGADDONDETAIL']._serialized_start=9281
  _globals['_BOARDINGADDONDETAIL']._serialized_end=9903
  _globals['_FEEDINGDETAIL']._serialized_start=9906
  _globals['_FEEDINGDETAIL']._serialized_end=10324
  _globals['_MEDICATIONDETAIL']._serialized_start=10327
  _globals['_MEDICATIONDETAIL']._serialized_end=10843
  _globals['_DAYCARESERVICE']._serialized_start=10846
  _globals['_DAYCARESERVICE']._serialized_end=11322
  _globals['_DAYCARESERVICEDETAIL']._serialized_start=11325
  _globals['_DAYCARESERVICEDETAIL']._serialized_end=11748
  _globals['_DAYCAREADDONDETAIL']._serialized_start=11751
  _globals['_DAYCAREADDONDETAIL']._serialized_end=12221
  _globals['_EVALUATIONSERVICE']._serialized_start=12223
  _globals['_EVALUATIONSERVICE']._serialized_end=12319
  _globals['_EVALUATIONTESTDETAIL']._serialized_start=12322
  _globals['_EVALUATIONTESTDETAIL']._serialized_end=12758
  _globals['_DOGWALKINGSERVICE']._serialized_start=12760
  _globals['_DOGWALKINGSERVICE']._serialized_end=12859
  _globals['_DOGWALKINGSERVICEDETAIL']._serialized_start=12862
  _globals['_DOGWALKINGSERVICEDETAIL']._serialized_end=13480
  _globals['_GROUPCLASSSERVICE']._serialized_start=13482
  _globals['_GROUPCLASSSERVICE']._serialized_end=13581
  _globals['_GROUPCLASSSERVICEDETAIL']._serialized_start=13584
  _globals['_GROUPCLASSSERVICEDETAIL']._serialized_end=14170
  _globals['_PETDETAIL']._serialized_start=14173
  _globals['_PETDETAIL']._serialized_end=15320
  _globals['_PETVACCINECOMPOSITE']._serialized_start=15323
  _globals['_PETVACCINECOMPOSITE']._serialized_end=15578
  _globals['_PETCODECOMPOSITE']._serialized_start=15581
  _globals['_PETCODECOMPOSITE']._serialized_end=15753
  _globals['_PAYBOOKINGREQUESTVIEW']._serialized_start=15756
  _globals['_PAYBOOKINGREQUESTVIEW']._serialized_end=16138
  _globals['_ADDRESSDETAIL']._serialized_start=16141
  _globals['_ADDRESSDETAIL']._serialized_end=16534
  _globals['_QUESTIONANSWERDETAIL']._serialized_start=16536
  _globals['_QUESTIONANSWERDETAIL']._serialized_end=16628
  _globals['_GROOMINGAUTOASSIGNDETAIL']._serialized_start=16631
  _globals['_GROOMINGAUTOASSIGNDETAIL']._serialized_end=16782
  _globals['_BOARDINGAUTOASSIGNDETAIL']._serialized_start=16785
  _globals['_BOARDINGAUTOASSIGNDETAIL']._serialized_end=16965
  _globals['_ASSIGNREQUIRE']._serialized_start=16968
  _globals['_ASSIGNREQUIRE']._serialized_end=17184
  _globals['_INCOMPLETEDETAILS']._serialized_start=17187
  _globals['_INCOMPLETEDETAILS']._serialized_end=19500
  _globals['_INCOMPLETEDETAILS_GROOMINGSERVICE']._serialized_start=17954
  _globals['_INCOMPLETEDETAILS_GROOMINGSERVICE']._serialized_end=18140
  _globals['_INCOMPLETEDETAILS_BOARDINGSERVICE']._serialized_start=18143
  _globals['_INCOMPLETEDETAILS_BOARDINGSERVICE']._serialized_end=18451
  _globals['_INCOMPLETEDETAILS_DAYCARESERVICE']._serialized_start=18454
  _globals['_INCOMPLETEDETAILS_DAYCARESERVICE']._serialized_end=18760
  _globals['_INCOMPLETEDETAILS_EVALUATIONSERVICE']._serialized_start=18763
  _globals['_INCOMPLETEDETAILS_EVALUATIONSERVICE']._serialized_end=18959
  _globals['_INCOMPLETEDETAILS_GROOMINGADDON']._serialized_start=18962
  _globals['_INCOMPLETEDETAILS_GROOMINGADDON']._serialized_end=19140
  _globals['_INCOMPLETEDETAILS_BOARDINGADDON']._serialized_start=19143
  _globals['_INCOMPLETEDETAILS_BOARDINGADDON']._serialized_end=19321
  _globals['_INCOMPLETEDETAILS_DAYCAREADDON']._serialized_start=19324
  _globals['_INCOMPLETEDETAILS_DAYCAREADDON']._serialized_end=19500
  _globals['_ROOMASSIGNREQUIRE']._serialized_start=19503
  _globals['_ROOMASSIGNREQUIRE']._serialized_end=19665
  _globals['_STAFFASSIGNREQUIRE']._serialized_start=19668
  _globals['_STAFFASSIGNREQUIRE']._serialized_end=19871
  _globals['_ORDERBOOKINGREQUESTVIEW']._serialized_start=19873
  _globals['_ORDERBOOKINGREQUESTVIEW']._serialized_end=19944
  _globals['_ACCEPTBOOKINGREQUESTREQUEST']._serialized_start=19947
  _globals['_ACCEPTBOOKINGREQUESTREQUEST']._serialized_end=20342
  _globals['_PETTOLODGING']._serialized_start=20344
  _globals['_PETTOLODGING']._serialized_end=20439
  _globals['_PETTOSTAFF']._serialized_start=20442
  _globals['_PETTOSTAFF']._serialized_end=20605
  _globals['_PETTOSERVICE']._serialized_start=20608
  _globals['_PETTOSERVICE']._serialized_end=20860
  _globals['_AUTOASSIGNREQUEST']._serialized_start=20862
  _globals['_AUTOASSIGNREQUEST']._serialized_end=20936
  _globals['_AUTOASSIGNRESPONSE']._serialized_start=20939
  _globals['_AUTOASSIGNRESPONSE']._serialized_end=21958
  _globals['_AUTOASSIGNRESPONSE_ASSIGNREQUIRE']._serialized_start=21565
  _globals['_AUTOASSIGNRESPONSE_ASSIGNREQUIRE']._serialized_end=21730
  _globals['_AUTOASSIGNRESPONSE_LODGINGDETAIL']._serialized_start=21733
  _globals['_AUTOASSIGNRESPONSE_LODGINGDETAIL']._serialized_end=21867
  _globals['_AUTOASSIGNRESPONSE_STAFFDETAIL']._serialized_start=21869
  _globals['_AUTOASSIGNRESPONSE_STAFFDETAIL']._serialized_end=21958
  _globals['_ACCEPTBOOKINGREQUESTRESPONSE']._serialized_start=21960
  _globals['_ACCEPTBOOKINGREQUESTRESPONSE']._serialized_end=22053
  _globals['_DECLINEBOOKINGREQUESTREQUEST']._serialized_start=22055
  _globals['_DECLINEBOOKINGREQUESTREQUEST']._serialized_end=22110
  _globals['_DECLINEBOOKINGREQUESTRESPONSE']._serialized_start=22112
  _globals['_DECLINEBOOKINGREQUESTRESPONSE']._serialized_end=22206
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST']._serialized_start=22209
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST']._serialized_end=23274
  _globals['_ACCEPTBOOKINGREQUESTV2RESPONSE']._serialized_start=23276
  _globals['_ACCEPTBOOKINGREQUESTV2RESPONSE']._serialized_end=23371
  _globals['_AUTOASSIGNV2REQUEST']._serialized_start=23373
  _globals['_AUTOASSIGNV2REQUEST']._serialized_end=23449
  _globals['_AUTOASSIGNV2RESPONSE']._serialized_start=23452
  _globals['_AUTOASSIGNV2RESPONSE']._serialized_end=23708
  _globals['_MOVEBOOKINGREQUESTTOWAITLISTPARAMS']._serialized_start=23711
  _globals['_MOVEBOOKINGREQUESTTOWAITLISTPARAMS']._serialized_end=23844
  _globals['_MOVEBOOKINGREQUESTTOWAITLISTRESULT']._serialized_start=23846
  _globals['_MOVEBOOKINGREQUESTTOWAITLISTRESULT']._serialized_end=23882
  _globals['_BOOKINGREQUESTSERVICE']._serialized_start=23885
  _globals['_BOOKINGREQUESTSERVICE']._serialized_end=25022
# @@protoc_insertion_point(module_scope)

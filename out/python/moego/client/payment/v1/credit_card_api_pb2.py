# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/client/payment/v1/credit_card_api.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/client/payment/v1/credit_card_api.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.payment.v1 import credit_card_defs_pb2 as moego_dot_models_dot_payment_dot_v1_dot_credit__card__defs__pb2
from moego.models.payment.v1 import credit_card_models_pb2 as moego_dot_models_dot_payment_dot_v1_dot_credit__card__models__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n-moego/client/payment/v1/credit_card_api.proto\x12\x17moego.client.payment.v1\x1a.moego/models/payment/v1/credit_card_defs.proto\x1a\x30moego/models/payment/v1/credit_card_models.proto\x1a\x17validate/validate.proto\"D\n\x18GetCreditCardListRequest\x12(\n\x0b\x62usiness_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\"[\n\x19GetCreditCardListResponse\x12>\n\x05\x63\x61rds\x18\x01 \x03(\x0b\x32(.moego.models.payment.v1.CreditCardModelR\x05\x63\x61rds\"S\n\x15ListCreditCardsParams\x12+\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\tcompanyId\x88\x01\x01\x42\r\n\x0b_company_id\"W\n\x15ListCreditCardsResult\x12>\n\x05\x63\x61rds\x18\x01 \x03(\x0b\x32(.moego.models.payment.v1.CreditCardModelR\x05\x63\x61rds\"\xa0\x01\n\x16\x43reateCreditCardParams\x12+\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\tcompanyId\x88\x01\x01\x12J\n\x04\x63\x61rd\x18\x02 \x01(\x0b\x32,.moego.models.payment.v1.CreditCardCreateDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x04\x63\x61rdB\r\n\x0b_company_id\"(\n\x16\x43reateCreditCardResult\x12\x0e\n\x02id\x18\x01 \x01(\tR\x02id2\xf8\x02\n\x11\x43reditCardService\x12z\n\x11GetCreditCardList\x12\x31.moego.client.payment.v1.GetCreditCardListRequest\x1a\x32.moego.client.payment.v1.GetCreditCardListResponse\x12q\n\x0fListCreditCards\x12..moego.client.payment.v1.ListCreditCardsParams\x1a..moego.client.payment.v1.ListCreditCardsResult\x12t\n\x10\x43reateCreditCard\x12/.moego.client.payment.v1.CreateCreditCardParams\x1a/.moego.client.payment.v1.CreateCreditCardResultB~\n\x1f\x63om.moego.idl.client.payment.v1P\x01ZYgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/payment/v1;paymentapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.client.payment.v1.credit_card_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\037com.moego.idl.client.payment.v1P\001ZYgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/payment/v1;paymentapipb'
  _globals['_GETCREDITCARDLISTREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_GETCREDITCARDLISTREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTCREDITCARDSPARAMS'].fields_by_name['company_id']._loaded_options = None
  _globals['_LISTCREDITCARDSPARAMS'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATECREDITCARDPARAMS'].fields_by_name['company_id']._loaded_options = None
  _globals['_CREATECREDITCARDPARAMS'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATECREDITCARDPARAMS'].fields_by_name['card']._loaded_options = None
  _globals['_CREATECREDITCARDPARAMS'].fields_by_name['card']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_GETCREDITCARDLISTREQUEST']._serialized_start=197
  _globals['_GETCREDITCARDLISTREQUEST']._serialized_end=265
  _globals['_GETCREDITCARDLISTRESPONSE']._serialized_start=267
  _globals['_GETCREDITCARDLISTRESPONSE']._serialized_end=358
  _globals['_LISTCREDITCARDSPARAMS']._serialized_start=360
  _globals['_LISTCREDITCARDSPARAMS']._serialized_end=443
  _globals['_LISTCREDITCARDSRESULT']._serialized_start=445
  _globals['_LISTCREDITCARDSRESULT']._serialized_end=532
  _globals['_CREATECREDITCARDPARAMS']._serialized_start=535
  _globals['_CREATECREDITCARDPARAMS']._serialized_end=695
  _globals['_CREATECREDITCARDRESULT']._serialized_start=697
  _globals['_CREATECREDITCARDRESULT']._serialized_end=737
  _globals['_CREDITCARDSERVICE']._serialized_start=740
  _globals['_CREDITCARDSERVICE']._serialized_end=1116
# @@protoc_insertion_point(module_scope)

// @since 2024-01-15 15:02:36
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.appointment.v1;

import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/interval.proto";
import "google/type/money.proto";
import "moego/models/appointment/v1/appointment_defs.proto";
import "moego/models/appointment/v1/appointment_enums.proto";
import "moego/models/appointment/v1/appointment_models.proto";
import "moego/models/appointment/v1/appointment_note_defs.proto";
import "moego/models/appointment/v1/appointment_extra_info_models.proto";
import "moego/models/appointment/v1/block_time_models.proto";
import "moego/models/appointment/v1/pet_detail_defs.proto";
import "moego/models/appointment/v1/pet_detail_enums.proto";
import "moego/models/appointment/v1/wait_list_enums.proto";
import "moego/models/fulfillment/v1/fulfillment_defs.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/models/order/v1/order_detail_defs.proto";
import "moego/models/order/v1/order_detail_models.proto";
import "moego/models/payment/v1/pre_auth_defs.proto";
import "moego/utils/v1/struct.proto";
import "moego/utils/v2/condition_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1;appointmentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.appointment.v1";

// Create a appointment request
message CreateAppointmentRequest {
  // Appointment request
  models.appointment.v1.AppointmentCreateDef appointment = 1 [(validate.rules).message = {required: true}];

  // Selected pet and services
  repeated models.appointment.v1.PetDetailDef pet_details = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    items: {
      message: {required: true}
    }
  }];

  // Pre-auth
  // Simply request a card on file, and we'll pre-authorize the ticket amount 24 hours before the appointment.
  // Final payment will be automatically charged at the end of the service day.
  optional models.payment.v1.PreAuthEnableDef pre_auth = 3 [(validate.rules).message = {skip: true}];

  // Appointment notes, contains ticket comments and alert notes
  // Ticket comments: For this appointment. Private to business only
  // Alert notes: It will be shown as a red exclamation mark on the appointment card. Private for business.
  repeated models.appointment.v1.AppointmentNoteCreateDef notes = 4 [(validate.rules).repeated = {
    min_items: 0
    max_items: 100
    items: {
      message: {required: true}
    }
  }];

  // company id
  int64 company_id = 5 [(validate.rules).int64 = {gt: 0}];

  // business id
  int64 business_id = 6 [(validate.rules).int64 = {gt: 0}];

  // staff id
  int64 staff_id = 7 [(validate.rules).int64 = {gt: 0}];

  // created at, if not set, use current time, used for data migration from other systems
  optional google.protobuf.Timestamp created_at = 8;
}

// Create a appointment response
message CreateAppointmentResponse {
  // Appointment id
  int64 appointment_id = 1;
}

// Update a appointment request, contains appointment date and pet details
message UpdateAppointmentRequest {
  // Appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];

  // appointment
  models.appointment.v1.AppointmentUpdateDef appointment = 2 [(validate.rules).message = {required: true}];

  // company id
  int64 company_id = 5 [(validate.rules).int64 = {gt: 0}];

  // business id
  int64 business_id = 6 [(validate.rules).int64 = {gt: 0}];

  // staff id
  int64 staff_id = 7 [(validate.rules).int64 = {gt: 0}];
}

// Update a appointment response
message UpdateAppointmentResponse {}

// Get appointment request
message GetAppointmentRequest {
  // Appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // company id
  int64 company_id = 2 [(validate.rules).int64 = {
    gt: 0
    ignore_empty: true
  }];
  // business id
  int64 business_id = 3 [(validate.rules).int64 = {
    gt: 0
    ignore_empty: true
  }];
}

// Get appointment response
message GetAppointmentResponse {
  // Appointment detail
  models.appointment.v1.AppointmentModel appointment = 1;
}

// Get appointment list request
message GetAppointmentListRequest {
  // Appointment id
  repeated int64 appointment_id = 1 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
  }];
  // company id
  int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 3 [
    (validate.rules).int64 = {gte: 0},
    deprecated = true
  ];
}

// Get appointment list response
message GetAppointmentListResponse {
  // Appointment detail
  repeated models.appointment.v1.AppointmentModel appointments = 1;
}

// get customer last appointment request
message GetCustomerLastAppointmentRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // customer id
  repeated int64 customer_id = 2 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
  }];
  // appointment status, if is empty then return not canceled appointment
  optional moego.models.appointment.v1.AppointmentStatus status = 3;

  // filter
  optional Filter filter = 4;

  // filter
  message Filter {
    // if no start time or end time, default is before current time
    // start time range
    optional google.type.Interval start_time_range = 1;
    // end_time_range
    optional google.type.Interval end_time_range = 2;
    // service item type
    repeated models.offering.v1.ServiceItemType service_item_types = 3;
    // filter no start time, default include
    optional bool filter_no_start_time = 8;
    // filter booking request, default include
    optional bool filter_booking_request = 9;
  }
}

// get customer last appointment response
message GetCustomerLastAppointmentResponse {
  // customer last appointment
  map<int64, models.appointment.v1.AppointmentModel> customer_last_appointment = 1;
}

// calculate appointment invoice request
message CalculateAppointmentInvoiceRequest {
  // Selected pet and services
  repeated models.appointment.v1.PetDetailDef pet_details = 3 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    items: {
      message: {required: true}
    }
  }];

  // company id
  int64 company_id = 5 [(validate.rules).int64 = {gt: 0}];

  // business id
  int64 business_id = 6 [(validate.rules).int64 = {gt: 0}];

  // staff id
  int64 staff_id = 7 [(validate.rules).int64 = {gt: 0}];
}

// calculate appointment invoice response
message CalculateAppointmentInvoiceResponse {
  // order detail
  models.order.v1.OrderDef order = 1;
}

// get in progress appointment request
message GetInProgressAppointmentRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // service item type
  models.offering.v1.ServiceItemType service_item_type = 2;
  // business id
  optional int64 business_id = 3 [(validate.rules).int64 = {gt: 0}];
  // customer id
  optional int64 customer_id = 4 [(validate.rules).int64 = {gt: 0}];
  // pet id
  optional int64 pet_id = 5 [(validate.rules).int64 = {gt: 0}];
}

// get in progress appointment response
message GetInProgressAppointmentResponse {
  // appointment id
  optional int64 appointment_id = 1;
}

// Create block request
message CreateBlockRequest {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // staff id
  int64 staff_id = 2 [(validate.rules).int64 = {gt: 0}];
  // company id
  int64 company_id = 3 [(validate.rules).int64 = {gt: 0}];
  // created by
  int64 created_by = 4 [(validate.rules).int64 = {gt: 0}];
  // start time
  google.protobuf.Timestamp start_time = 5 [(validate.rules).timestamp = {required: true}];
  // end time
  google.protobuf.Timestamp end_time = 6 [(validate.rules).timestamp = {required: true}];
  // color code
  string color_code = 7 [(validate.rules).string = {max_len: 16}];
  // description
  string description = 8;
  // Source of the appointment
  models.appointment.v1.AppointmentSource source = 9 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// Create block response
message CreateBlockResponse {
  // id of the block
  int64 id = 1;
}

// List appointments request
message ListAppointmentsRequest {
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 1;
  // company id
  int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];
  // business id
  repeated int64 business_ids = 3 [(validate.rules).repeated = {
    min_items: 0
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // filter
  Filter filter = 4;
  // order by (support multi fields), optional
  repeated moego.utils.v2.OrderBy order_bys = 5;
  // priority order type, optional
  optional PriorityOrderType priority_order_type = 6 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // filter
  message Filter {
    // start time range
    google.type.Interval start_time_range = 1;
    // end_time_range
    google.type.Interval end_time_range = 2;
    // last_updated_time_range
    google.type.Interval last_updated_time_range = 3;
    // status
    repeated models.appointment.v1.AppointmentStatus status = 4;
    // filter wait list status
    repeated models.appointment.v1.WaitListStatus wait_list_statuses = 6 [(validate.rules).repeated = {
      unique: true
      items: {
        enum: {defined_only: true}
      }
    }];
    // filter no start time, default include
    optional bool filter_no_start_time = 8;
    // filter booking request, default include
    optional bool filter_booking_request = 9;
    // service type include, If empty, it means all service item types
    // 查询等于这个类型的预约,only bitmap value
    // 如果，要查包含某一个类型，需要通过 ServiceItemEnum.getBitValueListByServiceItem 转化
    repeated int32 service_type_includes = 10 [(validate.rules).repeated = {
      min_items: 0
      unique: true
    }];
    // customer id
    repeated int64 customer_ids = 5 [(validate.rules).repeated = {
      max_items: 100
      unique: true
      items: {
        int64: {gt: 0}
      }
    }];
    // appointment date type
    optional AppointmentDateType date_type = 11 [(validate.rules).enum = {
      defined_only: true
      not_in: [0]
    }];
    // staff ids
    repeated int64 staff_ids = 12 [(validate.rules).repeated = {
      unique: true
      items: {
        int64: {gt: 0}
      }
      max_items: 500
    }];
    // appointment date, in yyyy-MM-dd format
    optional google.type.Date appointment_date = 13;
    // get staff appt by operation detail(multi-staff)
    optional bool include_service_operation = 14;
    // pet ids
    repeated int64 pet_ids = 15 [(validate.rules).repeated = {
      max_items: 100
      unique: true
      items: {
        int64: {gt: 0}
      }
    }];
    // service ids
    repeated int64 service_ids = 16 [(validate.rules).repeated = {
      max_items: 100
      unique: true
      items: {
        int64: {gt: 0}
      }
    }];
    // include block
    optional bool include_block = 17;
    // is_waiting_list
    optional bool is_waiting_list = 18;
    // check in time range
    google.type.Interval check_in_time_range = 19;
    // check out time range
    google.type.Interval check_out_time_range = 20;
    // payment status
    repeated moego.models.appointment.v1.AppointmentPaymentStatus payment_statuses = 21 [(validate.rules).repeated = {unique: true}];
  }
  // PriorityOrderType
  enum PriorityOrderType {
    // buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
    // unspecified
    UNSPECIFIED = 0;
    // Unexpired and unconfirmed appointments.
    // Must filter by one and only one customer_id.
    UNEXPIRED_UNCONFIRMED = 1;
  }
}

// List appointments response
message ListAppointmentsResponse {
  // pagination
  optional moego.utils.v2.PaginationResponse pagination = 1;
  // appointment detail
  repeated models.appointment.v1.AppointmentModel appointments = 2;
}

// Create a appointment request
message CreateAppointmentForOnlineBookingRequest {
  // Appointment request
  models.appointment.v1.AppointmentCreateForOnlineBookingDef appointment = 1 [(validate.rules).message = {required: true}];

  // Selected pet and services
  repeated models.appointment.v1.PetDetailDef pet_details = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    items: {
      message: {required: true}
    }
  }];

  // Appointment notes, contains ticket comments and alert notes
  // Ticket comments: For this appointment. Private to business only
  // Alert notes: It will be shown as a red exclamation mark on the appointment card. Private for business.
  // Cancel reason: For this appointment. Private to business only
  repeated models.appointment.v1.AppointmentNoteCreateDef notes = 4 [(validate.rules).repeated = {
    min_items: 0
    max_items: 100
    items: {
      message: {required: true}
    }
  }];

  // company id
  int64 company_id = 5 [(validate.rules).int64 = {gt: 0}];

  // business id
  int64 business_id = 6 [(validate.rules).int64 = {gt: 0}];

  // staff id, 0 means operator is system
  int64 staff_id = 7 [(validate.rules).int64 = {gte: 0}];

  // booking request id
  int64 booking_request_id = 8 [(validate.rules).int64 = {gt: 0}];

  // booking request identifier
  string booking_request_identifier = 9 [(validate.rules).string = {max_len: 255}];
}

// Create a appointment response
message CreateAppointmentForOnlineBookingResponse {
  // Appointment id
  int64 appointment_id = 1;
}

// List block times request
message ListBlockTimesRequest {
  // company id
  int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];
  // business id
  repeated int64 business_ids = 3 [(validate.rules).repeated = {
    min_items: 1
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // filter
  message Filter {
    // start time range
    google.type.Interval start_time_range = 1;
    // end_time_range
    google.type.Interval end_time_range = 2;
  }
  // filter
  Filter filter = 4;
}

// List block times response
message ListBlockTimesResponse {
  // block times
  repeated models.appointment.v1.BlockTimeModel block_times = 1;
}

// batch quick check-in request
message BatchQuickCheckInRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // selected service id
  int64 service_id = 3 [(validate.rules).int64 = {gt: 0}];
  // selected pet ids, if multi pets belong to the same customer, will be in the same appointment
  repeated int64 pet_ids = 4 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // selected date
  google.type.Date date = 5 [(validate.rules).timestamp = {required: true}];
  // staff id
  int64 staff_id = 6 [(validate.rules).int64 = {gt: 0}];
  // source
  models.appointment.v1.AppointmentSource source = 7 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// quick check-in response
message BatchQuickCheckInResponse {
  // created appointment ids
  repeated int64 created_appointment_ids = 1;
  // updated appointment ids
  repeated int64 updated_appointment_ids = 2;
}

// UpdateAppointmentSelective request
message UpdateAppointmentSelectiveRequest {
  // appointment id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // appointment date, in yyyy-MM-dd format
  optional string appointment_date = 2 [(validate.rules).string = {pattern: "\\d{4}-\\d{2}-\\d{2}"}];
  // appointment start time, the number of minutes of the day
  optional int32 appointment_start_time = 3 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
  // appointment end time, the number of minutes of the day
  optional int32 appointment_end_time = 4 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
  // confirmed time in seconds
  // 会有修改为 0 的场景，所以用 int64 而不是 Timestamp
  optional int64 confirmed_time = 5 [(validate.rules).int64 = {gte: 0}];
  // check in time in seconds
  // 会有修改为 0 的场景，所以用 int64 而不是 Timestamp
  optional int64 check_in_time = 6 [(validate.rules).int64 = {gte: 0}];
  // check out time in seconds
  // 会有修改为 0 的场景，所以用 int64 而不是 Timestamp
  optional int64 check_out_time = 7 [(validate.rules).int64 = {gte: 0}];
  // canceled time in seconds
  // 会有修改为 0 的场景，所以用 int64 而不是 Timestamp
  optional int64 canceled_time = 8 [(validate.rules).int64 = {gte: 0}];
  // color code
  optional string color_code = 9 [(validate.rules).string = {max_len: 20}];
  // cancel by type
  optional int32 cancel_by_type = 10 [(validate.rules).int32 = {gte: 0}];
  // cancel by
  optional int64 cancel_by = 11 [(validate.rules).int64 = {gte: 0}];
  // confirm by type
  optional int32 confirm_by_type = 12 [(validate.rules).int32 = {gte: 0}];
  // confirm by
  optional int64 confirm_by = 13;
  // status before checkin
  optional moego.models.appointment.v1.AppointmentStatus status_before_checkin = 14 [(validate.rules).enum = {
    defined_only: true
    //     not_in: [0] // 会有修改为 0 的场景
  }];
  // status before ready
  optional moego.models.appointment.v1.AppointmentStatus status_before_ready = 15 [(validate.rules).enum = {
    defined_only: true
    //     not_in: [0] // 会有修改为 0 的场景
  }];
  // status before finish
  optional moego.models.appointment.v1.AppointmentStatus status_before_finish = 16 [(validate.rules).enum = {
    defined_only: true
    //     not_in: [0] // 会有修改为 0 的场景
  }];
  // update time
  optional google.protobuf.Timestamp update_time = 17;
  // updated by
  optional int64 updated_by_id = 18;
  // appointment status
  optional moego.models.appointment.v1.AppointmentStatus status = 19 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // ready time in seconds
  // 会有修改为 0 的场景，所以用 int64 而不是 Timestamp
  optional int64 ready_time = 20 [(validate.rules).int64 = {gte: 0}];
  // is paid
  optional moego.models.appointment.v1.AppointmentPaymentStatus payment_status = 21 [(validate.rules).enum = {defined_only: true}];
  // Indicates if the appointment was booked online
  optional moego.models.appointment.v1.AppointmentBookOnlineStatus book_online_status = 22 [(validate.rules).enum = {defined_only: true}];
  // If this parameter is not passed, it will not be updated
  repeated models.appointment.v1.UpdatePetDetailDef pet_details = 23 [(validate.rules).repeated = {min_items: 0}];
  // updated by type. This field is not used for appointment data updates
  optional models.appointment.v1.AppointmentUpdatedBy update_by_type = 24 [(validate.rules).enum = {defined_only: true}];
  // is_deprecate
  // 支持删除 appointment
  // deprecated by Freeman since 2025/3/17, 这个接口的实现被修改的很奇怪，不要通过设置 is_deprecate 来删除 appointment，use DeleteAppointments instead
  optional bool is_deprecate = 25 [deprecated = true];
}

// UpdateAppointmentSelective response
message UpdateAppointmentSelectiveResponse {
  // affected rows count
  int32 affected_rows = 1;
}

// list appointments for pet
// Considering the query performance, it is necessary to specify the service and date
message ListAppointmentForPetsRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet id
  repeated int64 pet_ids = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // filter
  message Filter {
    // service id
    int64 service_id = 1 [(validate.rules).int64 = {
      gt: 0
      ignore_empty: true
    }];
    // date
    google.type.Date date = 2 [(validate.rules).message.required = true];
    // business id
    optional int64 business_id = 3 [(validate.rules).int64 = {gt: 0}];
    // appointment status
    repeated moego.models.appointment.v1.AppointmentStatus statuses = 4;
  }
  // filter
  Filter filter = 3 [(validate.rules).message.required = true];
}

// list appointments for pet response
message ListAppointmentForPetsResponse {
  // appointment detail
  repeated models.appointment.v1.AppointmentModel appointments = 1;
  // pet id to appointment ids
  map<int64, utils.v1.Int64ListValue> pet_id_to_appointment_id_list = 2;
}

// list appointments for customer
message ListAppointmentsForCustomersRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // customer id
  repeated int64 customer_ids = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // filter
  Filter filter = 3 [(validate.rules).message.required = true];

  // filter
  message Filter {
    // appointment date type
    AppointmentDateType date_type = 1 [(validate.rules).enum = {
      defined_only: true
      not_in: [0]
    }];
    // appointment status
    repeated moego.models.appointment.v1.AppointmentStatus statuses = 2 [(validate.rules).repeated = {
      max_items: 10
      unique: true
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];
    // service type include, If empty, it means all service item types
    repeated int32 service_type_includes = 10 [(validate.rules).repeated = {
      min_items: 0
      unique: true
    }];
  }
}

// Appointment date type, determined by appointment date field
enum AppointmentDateType {
  // unspecified
  APPOINTMENT_DATE_TYPE_UNSPECIFIED = 0;
  // last: appointment_date < currentDate or (appointment_date = currentDate and appointment_end_time < currentMinute)
  LAST = 1;
  // next: appointment_date > currentDate or (appointment_date = currentDate and appointment_start_time > currentMinute)
  NEXT = 2;
  // today: appointment_date = currentDate
  TODAY = 3;
  // NEXT_NOT_END: appointment_end_date > currentDate or (appointment_end_date = currentDate and appointment_end_time >= currentMinute)
  NEXT_NOT_END = 4;
  // NEXT_AFTER_TODAY: appointment_date > currentDate
  NEXT_AFTER_TODAY = 5;
}

// list appointments for customer response
message ListAppointmentsForCustomersResponse {
  // appointment detail
  repeated models.appointment.v1.AppointmentModel appointments = 1;
}

// Cancel appointment request
message CancelAppointmentRequest {
  // Appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // cancel by type
  models.appointment.v1.AppointmentUpdatedBy cancel_by_type = 2 [(validate.rules).enum = {defined_only: true}];
  // cancel by, client account id or business staff id
  int64 cancel_by = 3 [(validate.rules).int64 = {gt: 0}];
  // cancel reason, optional
  optional string cancel_reason = 4 [(validate.rules).string = {max_len: 1048576}];
  // no-show status
  models.appointment.v1.AppointmentNoShowStatus no_show = 5 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // Repeat appointment modify scope
  optional models.appointment.v1.RepeatAppointmentModifyScope repeat_appointment_modify_scope = 6 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// Cancel appointment response
message CancelAppointmentResponse {}

// Batch book again appointment
message BatchBookAgainAppointmentRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];

  // appointment ids
  repeated int64 appointment_ids = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
    items: {
      int64: {gt: 0}
    }
  }];

  // Target date
  google.type.Date target_date = 3;

  // operate staff id
  int64 rebook_by = 4 [(validate.rules).int64 = {gt: 0}];
}

// Batch book again appointment
message BatchBookAgainAppointmentResponse {
  // book again appointment detail
  repeated models.appointment.v1.AppointmentModel appointments = 1;
}

// Batch cancel appointment
message BatchCancelAppointmentRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];

  // appointment ids
  repeated int64 appointment_ids = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
    items: {
      int64: {gt: 0}
    }
  }];

  // operate staff id
  int64 cancel_by = 3 [(validate.rules).int64 = {gt: 0}];

  // cancel reason, optional
  optional string cancel_reason = 4 [(validate.rules).string = {max_len: 1024}];
}

// Batch cancel appointment by staff and date result
message BatchCancelAppointmentResponse {
  // canceled appointments detail
  repeated models.appointment.v1.AppointmentModel appointments = 1;
}

// CountAppointmentForPets request
message CountAppointmentForPetsRequest {
  // pet id
  repeated int64 pet_ids = 2 [(validate.rules).repeated = {
    max_items: 10000
    items: {
      int64: {gt: 0}
    }
  }];
}

// CountAppointmentForPets response
message CountAppointmentForPetsResponse {
  // pet id -> appointment count
  // 不会出现 request 中的 pet id 不存在于 response 中的情况
  map<int64, int32> pet_id_to_appointment_count = 1;
}

// DeleteAppointments request
message DeleteAppointmentsRequest {
  // company id, optional
  optional int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id, optional
  optional int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // appointment ids
  repeated int64 ids = 3 [(validate.rules).repeated = {
    max_items: 10000
    items: {
      int64: {gt: 0}
    }
  }];
}

// DeleteAppointments response
message DeleteAppointmentsResponse {
  // affected rows count
  int32 affected_rows = 1;
}

// RestoreAppointments request
message RestoreAppointmentsRequest {
  // company id, optional
  optional int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id, optional
  optional int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // appointment ids
  repeated int64 ids = 3 [(validate.rules).repeated = {
    max_items: 10000
    items: {
      int64: {gt: 0}
    }
  }];
}

// RestoreAppointments response
message RestoreAppointmentsResponse {
  // affected rows count
  int32 affected_rows = 1;
}

// RescheduleBoardingAppointment request
message RescheduleBoardingAppointmentRequest {
  // company id, optional
  optional int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // appointment id
  int64 appointment_id = 2 [(validate.rules).int64 = {gt: 0}];

  // start date, optional
  optional string start_date = 3 [(validate.rules).string = {pattern: "\\d{4}-\\d{2}-\\d{2}"}];
  // end date, optional
  optional string end_date = 4 [(validate.rules).string = {pattern: "\\d{4}-\\d{2}-\\d{2}"}];
}

// sync appointment to order, temporary for data fix
message SyncAppointmentToOrderRequest {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// sync appointment to order response
message SyncAppointmentToOrderResponse {}

// RescheduleBoardingAppointment response
message RescheduleBoardingAppointmentResponse {}

// Preview order detail by appointment id, temporary for data fix
message PreviewOrderDetailRequest {
  // appointment id
  repeated int64 appointment_ids = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
    items: {
      int64: {gt: 0}
    }
  }];
}

// Preview order detail by appointment id, temporary for data fix
message PreviewOrderDetailResponse {
  // order detail
  repeated models.order.v1.OrderDetailModel order_details = 1;
}

// The request message for list order line items
message PreviewOrderLineItemsRequest {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// The response message for list order line items
message PreviewOrderLineItemsResponse {
  // pet services
  repeated models.fulfillment.v1.PetService pet_services = 1;
  // surcharges
  repeated models.fulfillment.v1.SurchargeItem surcharges = 2;
}

// Get time overlap appointment list
message GetTimeOverlapAppointmentListRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];

  // customer id
  repeated int64 customer_id = 2 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
  }];

  // pet ids
  repeated int64 pet_ids = 3 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
  }];

  // date range filter
  Filter filter = 4;

  // Date range filter
  message Filter {
    // date range
    google.type.Interval date_range = 1;
  }
}

// Get time overlap appointment list response
message GetTimeOverlapAppointmentListResponse {
  // appointment list map, key is pet id
  map<int64, AppointmentList> appointment_list = 1;
}

// Appointment list
message AppointmentList {
  // appointment list
  repeated models.appointment.v1.AppointmentModel appointments = 1;
}

// The request message for the PreviewEstimateOrder method.
message PreviewEstimateOrderRequest {
  // The company ID
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];

  // The business ID
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];

  // The appointment ID
  repeated int64 appointment_ids = 3 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
    items: {
      int64: {gt: 0}
    }
  }];
}

// The response message for the PreviewEstimateOrder method.
message PreviewEstimateOrderResponse {
  // Estimated order
  repeated EstimatedOrder estimated_orders = 1;

  // The estimated order
  message EstimatedOrder {
    // The appointment id
    int64 appointment_id = 1;
    // The service subtotal
    google.type.Money services_subtotal = 2;
    // The service charges total
    google.type.Money services_charges_total = 3;
    // The estimated total
    google.type.Money estimated_total = 4;
  }
}

// The request message for the ListExtraInfo method.
message ListExtraInfoRequest {
  // The appointment ID
  repeated int64 appointment_ids = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
    items: {
      int64: {gt: 0}
    }
  }];
}

// The response message for the ListExtraInfo method.
message ListExtraInfoResponse {
  // The extra info
  repeated models.appointment.v1.AppointmentExtraInfoModel extra_info = 1;
}

// the appointment service
service AppointmentService {
  // Create a appointment
  // Applicable to quick add and advanced add appointment scenarios
  rpc CreateAppointment(CreateAppointmentRequest) returns (CreateAppointmentResponse);

  // Incremental update appointment
  // Applicable to add/modify pet&service, switch on multi-pets start at the same time.
  rpc UpdateAppointment(UpdateAppointmentRequest) returns (UpdateAppointmentResponse);

  // 增量更新 appointment
  // NOTE：这个方法名应该叫做 UpdateAppointment，但是被占用了，
  // 而且 UpdateAppointment 的 request 使用了 api 层的定义，没法扩展，所以直接新开一个接口。
  rpc UpdateAppointmentSelective(UpdateAppointmentSelectiveRequest) returns (UpdateAppointmentSelectiveResponse);

  // Get appointment
  rpc GetAppointment(GetAppointmentRequest) returns (GetAppointmentResponse);

  // Get appointment list
  rpc GetAppointmentList(GetAppointmentListRequest) returns (GetAppointmentListResponse);

  // get customer last appointment
  rpc GetCustomerLastAppointment(GetCustomerLastAppointmentRequest) returns (GetCustomerLastAppointmentResponse);

  // Calculate appointment invoice
  // Estimated total, including service, add-on, tax, discount etc.
  rpc CalculateAppointmentInvoice(CalculateAppointmentInvoiceRequest) returns (CalculateAppointmentInvoiceResponse);

  // get in progress appointment
  rpc GetInProgressAppointment(GetInProgressAppointmentRequest) returns (GetInProgressAppointmentResponse);
  // Create block
  rpc CreateBlock(CreateBlockRequest) returns (CreateBlockResponse);
  // List appointments
  rpc ListAppointments(ListAppointmentsRequest) returns (ListAppointmentsResponse);
  // list block times
  rpc ListBlockTimes(ListBlockTimesRequest) returns (ListBlockTimesResponse);

  // Create a appointment for online booking
  // Applicable to online booking scenarios for boarding, daycare & evaluation services
  rpc CreateAppointmentForOnlineBooking(CreateAppointmentForOnlineBookingRequest) returns (CreateAppointmentForOnlineBookingResponse);

  // batch quick check-in will create appointments for the selected pets and service, and set the appointment status to "checked_in"
  // if the appointment already exists, it will update the appointment status to "checked_in"
  // if multiple pets belong to the same customer, will be in the same appointment
  rpc BatchQuickCheckIn(BatchQuickCheckInRequest) returns (BatchQuickCheckInResponse);

  // list appointments for pet
  rpc ListAppointmentForPets(ListAppointmentForPetsRequest) returns (ListAppointmentForPetsResponse);

  // list appointments for customer
  rpc ListAppointmentsForCustomers(ListAppointmentsForCustomersRequest) returns (ListAppointmentsForCustomersResponse);

  // Cancel appointment
  rpc CancelAppointment(CancelAppointmentRequest) returns (CancelAppointmentResponse);

  // Batch book again appointment by staff and date
  rpc BatchBookAgainAppointment(BatchBookAgainAppointmentRequest) returns (BatchBookAgainAppointmentResponse);

  // Batch cancel appointment by staff and date
  rpc BatchCancelAppointment(BatchCancelAppointmentRequest) returns (BatchCancelAppointmentResponse);

  // Count appointment for pets
  rpc CountAppointmentForPets(CountAppointmentForPetsRequest) returns (CountAppointmentForPetsResponse);

  // Delete appointments
  rpc DeleteAppointments(DeleteAppointmentsRequest) returns (DeleteAppointmentsResponse);

  // RestoreAppointments 用于恢复之前被删除的 appointment 记录
  rpc RestoreAppointments(RestoreAppointmentsRequest) returns (RestoreAppointmentsResponse);

  // Reschedule boarding appointment
  // 处理与 boarding 相关的附加数据，如 daycare、grooming、addon 等服务。
  // 如果传入的 end_date 早于当前 end_date，则删除 boarding 关联的所有 daycare、grooming、addon 数据。
  // 如果传入的 end_date 晚于当前 end_date，则在 boarding 上添加相应的新增 daycare、grooming、addon 数据。
  rpc RescheduleBoardingAppointment(RescheduleBoardingAppointmentRequest) returns (RescheduleBoardingAppointmentResponse);

  // Sync appointment to order, temporary for data fix
  rpc SyncAppointmentToOrder(SyncAppointmentToOrderRequest) returns (SyncAppointmentToOrderResponse);

  // Preview order detail by appointment id, temporary for data fix
  rpc PreviewOrderDetail(PreviewOrderDetailRequest) returns (PreviewOrderDetailResponse);

  // Preview order line items by appointment id, for invoice preview
  rpc PreviewOrderLineItems(PreviewOrderLineItemsRequest) returns (PreviewOrderLineItemsResponse);

  // Get appointment time overlap list
  rpc GetTimeOverlapAppointmentList(GetTimeOverlapAppointmentListRequest) returns (GetTimeOverlapAppointmentListResponse);

  // Preview estimated order for appointment
  // estimated total = total service price + service charge(auto apply)
  rpc PreviewEstimateOrder(PreviewEstimateOrderRequest) returns (PreviewEstimateOrderResponse);

  // List extra info for appointment
  rpc ListExtraInfo(ListExtraInfoRequest) returns (ListExtraInfoResponse);
}

package com.moego.server.retail.web;

import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.response.ResponseResult;
import com.moego.common.utils.PermissionUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.GetCompanyIdRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.business.api.IBusinessStaffService;
import com.moego.server.retail.dto.InvoiceDto;
import com.moego.server.retail.mapperbean.Cart;
import com.moego.server.retail.param.InvoiceIdParams;
import com.moego.server.retail.service.InvoiceService;
import com.moego.server.retail.service.dto.RetailInvoiceListDto;
import com.moego.server.retail.service.params.CartIdParams;
import com.moego.server.retail.service.params.GetListQueryParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(path = "/retail/invoice")
public class InvoiceController {

    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private IBusinessStaffService iBusinessStaffService;

    @Autowired
    private BusinessServiceGrpc.BusinessServiceBlockingStub businessServiceBlockingStub;

    @PostMapping("")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<InvoiceDto> create(AuthContext context, @RequestBody CartIdParams cartIdParams) {
        return ResponseResult.success(
                invoiceService.create(context.companyId(), context.getBusinessId(), cartIdParams.getCartId()));
    }

    @PostMapping("/createFromCart")
    @Auth(value = AuthType.BUSINESS)
    public InvoiceDto createFromCart(AuthContext context, @RequestBody Cart cart) {
        var staffPermissions = iBusinessStaffService.getBusinessRoleByStaffId(context.getStaffId());
        if (!PermissionUtil.isOwner(staffPermissions)) {
            throw new CommonException(ResponseCodeEnum.PERMISSION_NOT_ENOUGH);
        }
        cart.setBusinessId(context.getBusinessId());
        cart.setCompanyId(context.companyId());
        return invoiceService.createFromCart(context.companyId(), cart);
    }

    @PostMapping("/confirm-payment")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<InvoiceDto> confirmPayment(AuthContext context, @RequestBody InvoiceIdParams params) {
        return ResponseResult.success(invoiceService.confirmPayment(
                context.companyId(), context.getBusinessId(), context.getStaffId(), params.getInvoiceId()));
    }

    @GetMapping("/list")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<RetailInvoiceListDto> selectList(
            AuthContext context,
            @RequestParam(defaultValue = "") String saleType,
            @RequestParam(defaultValue = "") String keyword,
            @RequestParam(defaultValue = "") Integer customerId,
            @RequestParam(defaultValue = "false") Boolean withPayment,
            GetListQueryParams params) {
        return ResponseResult.success(invoiceService.selectPageList(
                context.getBusinessId(), saleType, keyword, customerId, withPayment, params));
    }

    @GetMapping("/detail")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<InvoiceDto> selectById(AuthContext context, @RequestParam Integer invoiceId) {
        InvoiceDto invoiceDto = invoiceService.selectById(invoiceId);
        if (businessServiceBlockingStub
                        .getCompanyId(GetCompanyIdRequest.newBuilder()
                                .setBusinessId(invoiceDto.getBusinessId().longValue())
                                .build())
                        .getCompanyId()
                != context.companyId()) {
            throw ExceptionUtil.bizException(Code.CODE_INVOICE_NOT_FOUND, "invoice not found");
        }

        return ResponseResult.success(invoiceDto);
    }
}

alter table moe_grooming.moe_grooming_package
    add status TINYINT default 1 not null comment '状态：1-正常，2-删除';

#package history 表增加 status 字段，用于失效记录
alter table moe_grooming.moe_grooming_package_history
  add status TINYINT default 1 not null comment '状态：1-正常，2-删除';

# moe_grooming_invoice_apply_package 表增加 status 字段，用于失效记录
alter table moe_grooming.moe_grooming_invoice_apply_package
  add status TINYINT default 1 not null comment '状态：1-正常，2-失效';
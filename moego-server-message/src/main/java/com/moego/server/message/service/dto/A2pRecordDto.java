package com.moego.server.message.service.dto;

import java.util.List;
import lombok.Data;

@Data
public class A2pRecordDto {

    private Integer companyId;
    private Boolean isPaid;

    private String a2pFeeStatus;

    private Boolean withEin;
    private String businessName;
    private String businessType;
    private String address1;
    private String address2;
    private String city;
    private String region;
    private String zipcode;
    private String lat;
    private String lng;
    private String iosCountry;
    private String website;
    private String ein;
    private List<String> einFiles;
    private String firstName;
    private String lastName;
    private String phoneNumber;
    private String email;
}

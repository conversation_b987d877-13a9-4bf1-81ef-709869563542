## moego-lib-messaging

提供 MQ 的抽象，目前提供了 Pulsar 实现。

### Core Concepts

-   Topic

    topic 是一个主题或主题类别，用于识别和分类消息。

    当发布者发布一条消息时，它会将消息发布到一个特定的 topic 中，这个 topic 可以被多个订阅者订阅，这些订阅者会收到与该
    topic 相关的所有消息。

    通常，topic 是通过使用一种特殊的字符串格式来表示的。

    约定：**topic 格式为 `{namespace}:{event}`，其中 namespace 为服务名，event 为事件名。**
    例如：`grooming:AddAppointmentEvent`

    > 如果使用的实现是 Pulsar，创建新的 namespace 需要找 Frank 预先创建好。

-   Producer

    Producer 是指生产者，也称为发布者。

    当 producer 发布一条消息时，它会将消息发送到一个特定的主题（topic）中，MQ 会将该消息保存在消息队列中，以便消费者可以消费它。

    生产者通常不知道谁订阅了它们发布的消息，也不关心消息是否被消费着接收或消费。发布者只需将消息发布到指定的主题中，并信任
    MQ 会确保消息能够到达消费着。

    在 Messaging 模块，producer 通过 `MessagingOperations` 发送消息，支持同步/异步发送，延迟消息。

-   Consumer

    Consumer 是指消费者，它是一种接收消息的客户端，用于订阅一个或多个主题（Topic），以接收 Producer
    发送的消息。当消息被发布到一个特定的主题时，消费者会收到该消息并进行处理。

    在 Messaging 模块，consumer 通过实现 `EventListener`
    接口消费消息，支持多种订阅类型，多线程消费，详情查看 `@Consumer` 注解。

    当多个 consumer 订阅同一个 topic 时，我们需要控制其消费行为，因此 Messaging 模块抽象了三种订阅类型：

    -   `ONCE`: 只会一个实例消费一次
    -   `ONCE_EACH_SERVICE`: 会被每个服务一个实例消费一次
    -   `BROADCAST`: 会被每个服务的每个实例消费一次

### Usage

添加依赖

<details>
    <summary>deps.json</summary>

```json
{
    "deps": {
        "moego-java-lib": {
            "com.moego.lib:moego-lib-messaging": ""
        }
    }
}
```

</details>

配置连接信息

<details>
    <summary>application.yaml</summary>

```yaml
moego:
    messaging:
        pulsar:
            service-url: pulsar.t2.moego.pet:40650
            authentication: <Please Ask Frank>
            tenant: test2
```

</details>

模拟一个使用场景，在 grooming 服务新增一个 appointment 时，需要在 message 服务中向对应 customer 发送消息。

首先定义 Event:

```java
@Data
public class AddAppointmentEvent {

    // 可以将 event 对应的 topic 作为常量暴露出来，方便 producer 和 consumer 使用
    public static final String TOPIC = "grooming:AddAppointmentEvent";

    private Integer customerId;
    private String content;
}

```

> Event 就是一个 POJO，支持但**不要使用字面值**（int,String,boolean...），Event 会序列化成 JSON 进行传输，
> 在反序列化为 Java Bean 时，基本上所有的 JSON library 都会使用默认的构造函数来创建对象，
> 所以 **必须保证 Event 有无参构造函数。**

对于发送方 grooming（publisher），通过 `MessagingOperations` 发送消息：

```java
@Service
public class AppointmentService {

    @Autowired
    private MessagingOperations messagingOperations;

    public void addAppointment(Appointment appointment) {
        // do your logic ...

        // publish an event
        AddAppointmentEvent event = new AddAppointmentEvent();
        event.setCustomerId(customerId);
        event.setContent("Your appointment has been added.");
        messagingOperations.send(AddAppointmentEvent.TOPIC, event);
    }
}

```

也可以指直接使用 `Messages` 工具类直接发送消息，不用 inject：

```java
Messages.send("topic",event);
```

对于消费方 message（consumer），通过实现 `EventListener` 消费消息。提供两种使用方式：

1. 编码

    ```java
    @Consumer(
        topics = AddAppointmentEvent.TOPIC, // 支持通配符 '*'，例如 grooming.appointment.*，会消费 grooming.appointment 下的所有事件
        subscribeType = SubscribeType.ONCE_EACH_SERVICE, // 消费类型，once 表示只会一个实例消费一次，once_each_service 表示会被每个服务一个实例消费一次，broadcast 表示会被每个服务的每个实例消费一次
        initialPosition = InitialPosition.LATEST, // 消费初始位置，latest 表示从最新的消息开始消费，earliest 表示从最旧的消息开始消费
        threadCount = 3 // 消费线程数, 默认为 1
    )
    public class AddAppointmentEventListener implements EventListener<AddAppointmentEvent> {

        @Autowired
        private MessageService messageService;

        @Override
        public void onEvent(Msg<AddAppointmentEvent> msg) {
            AddAppointmentEvent event = msg.getBody();

            // do your logic ...
            messageService.sendMessageToCustomer(event.getCustomerId(), event.getContent());

            // Ack the message !
            msg.ack();
        }
    }

    ```

    > 可以通过配置 `moego.messaging.auto-ack=true` 开启自动 ack。

2. 配置

    ```java
    @Consumer(name = "add_appt_event_listener")
    public class AddAppointmentEventListener implements EventListener<AddAppointmentEvent> {

        @Autowired
        private MessageService messageService;

        @Override
        public void onEvent(Msg<AddAppointmentEvent> msg) {
            AddAppointmentEvent event = msg.getBody();

            // do your logic ...
            messageService.sendMessageToCustomer(event.getCustomerId(), event.getContent());

            // Ack the message !
            msg.ack();
        }
    }

    ```

    ```yaml
    moego:
        messaging:
            consumers:
                - name: add_appt_event_listener
                  topics: [grooming.appointment.AddAppointmentEvent]
                  subscribeType: once_each_service
                  initialPosition: latest
                  threadCount: 3
    ```

NOTE：可以但**不要混合使用**，**配置方式优先级会更高。**

### 事务消息

> 这里事务指的是关系型数据库的事务

事务消息是为了解决事务未提交但是消息已经发送的问题，使消息发送和事务提交具有原子性，事务消息的实现原理是在事务提交后再发送消息，如果事务回滚则不发送消息。

可以直接使用 `Messages` 工具类发送事务消息：

```java

Messages.sendTx("topic",event);
```

### Graceful Shutdown

理想情况下如果服务是处于正常关闭状态（Ctrl+C/kill），那么程序应该会等待正在处理的 Event
处理完成后再关闭，并在此时不再接收和处理新的
Event，这个过程称为 graceful shutdown。

通常 graceful shutdown 会有一个超时时间，如果超过了超时时间，那么就会强制关闭服务。
**如果在超时事件内，该 Event 没有被消费完，那么这个 Event 不会 Ack，从而导致这个 Event 会被重复消费。**
但是也不能无限等待，因为进程往往会被强制关闭（比如在 k8s 环境下超过了
k8s 的 graceful shutdown 时间），因此需要给程序一个合理的超时时间。

可以通过配置 `moego.messaging.graceful-shutdown-timeout` 来设置 graceful shutdown 的超时时间，默认为
3s。

### How to Debug

由于现在采用了分支测试，导致的问题就是对 `EventListener` 的调试可能会变的比较麻烦，因为在调试的时候，可能会出现
Event 被其他分支消费了。

为了解决这个问题，提供了一个 `test-group` 的配置项，当 producer 和 consumer 的 `test-group`
配置项一致的时候，这个 Event 只会被这个分支的服务实例消费。

比如有人修改了 message 服务 `AddAppointmentEventListener` 的逻辑，可以在 grooming (producer) 和
message (consumer) 的配置文件中配置相同的 `test-group`（比如 feature-a），
这样在调试的时候，这个 Event 只会被 feature-a test group 的服务实例消费。

相关配置：

```yaml
moego:
    messaging:
        test-group: feature-a
```

### Configurations

```yaml
moego:
    messaging:
        pulsar:
            service-url: mq.t2.moego.pet:40150
            authentication: <Please Ask Frank>
        graceful-shutdown-timeout: 3s
        test-group: feature-a
        auto-ack: false
        consumers:
            - name: consumer-1
              topics: [grooming.appointment.AddAppointmentEvent]
              subscribe-type: once_each_service
              initial-position: latest
              thread-count: 3
            - name: consumer-2
              topics: [grooming.appointment.*]
              subscribe-type: once_each_service
              initial-position: latest
              thread-count: 5
```

### 参考示例

基本使用可以参考 [api-todo](https://github.com/MoeGolibrary/moego-api-todo) 分支 **feature-test-mq**。

### TODO

-   [ ] Support retry ?

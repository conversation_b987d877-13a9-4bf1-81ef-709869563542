package com.moego.server.task.job;

import com.moego.server.message.api.ISendService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SendRenewEndDateNotificationTask {

    private final ISendService sendApi;

    /**
     * 每小时执行一次
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void sendRenewEndDateNotification() {
        log.info("Start to send renew end date notification");
        sendApi.sendRenewEndDateNotification();
    }
}

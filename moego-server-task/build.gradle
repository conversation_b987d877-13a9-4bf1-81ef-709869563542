plugins {
    id 'jacoco'
    id 'net.razvan.jacoco-to-cobertura' version "1.2.0"
    id 'org.springframework.boot'
}

apply from: "https://moego.s3.us-west-2.amazonaws.com/ops/sbc/${System.getProperty('sbc.scriptBranch', '')}SourceBranchControl.gradle"
applySourceBranchControlDependencies()

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter'
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
    implementation("io.grpc:grpc-services:${grpcVersion}")
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    testCompileOnly 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok'
    testImplementation("org.springframework.boot:spring-boot-starter-test")
}

bootJar {
  archivesBaseName = 'moego-server'
  version = ''
}

jacocoTestReport {
  reports {
    xml {
      required = true
      destination file("${rootProject.buildDir}/reports/jacoco/test/jacocoTestReport.xml")
    }
  }
  afterEvaluate {
    classDirectories.setFrom(files(classDirectories.files.collect {
      fileTree(dir: it, exclude: [
        "**/*ConverterImpl*",
        "**/*MapperImpl*",
        "**/jooq/generated/**",
      ])
    }))
  }
}

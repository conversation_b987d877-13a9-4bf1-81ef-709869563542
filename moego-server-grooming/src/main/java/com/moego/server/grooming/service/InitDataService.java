package com.moego.server.grooming.service;

import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.enums.ServiceEnum;
import com.moego.common.utils.DateUtil;
import com.moego.server.grooming.dto.InitDataGroomingResultDto;
import com.moego.server.grooming.mapper.MoeGroomingServiceCategoryMapper;
import com.moego.server.grooming.mapper.MoeGroomingServiceMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingService;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceCategory;
import com.moego.server.grooming.params.AppointmentParams;
import com.moego.server.grooming.params.GroomingInitDataParam;
import com.moego.server.grooming.params.PetDetailParams;
import java.math.BigDecimal;
import java.util.ArrayList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class InitDataService {

    @Autowired
    private MoeGroomingServiceCategoryMapper serviceCategoryMapper;

    @Autowired
    private MoeGroomingServiceMapper moeGroomingServiceMapper;

    @Autowired
    private MoeGroomingAppointmentService moeGroomingAppointmentService;

    @Autowired
    private MoeGroomingReportService groomingReportService;

    /**
     * 新用户首次创建 company&&business 时，生成相关setting
     * @param initDataParam
     * @return
     */
    public InitDataGroomingResultDto initGroomingModule(GroomingInitDataParam initDataParam) {
        Integer businessId = initDataParam.getBusinessId();
        Long companyId = initDataParam.getCompanyId();
        // 初始化 Grooming report setting
        groomingReportService.initGroomingReportSetting(businessId, companyId, false);

        Integer taxId = initDataParam.getTaxId();
        initServiceCategory0(businessId, companyId, taxId);
        // retail模块初始化的时候，需要一个serviceId
        InitDataGroomingResultDto initResultData = new InitDataGroomingResultDto();
        initServiceCategory1(businessId, companyId, taxId, initResultData);
        initServiceCategory2(businessId, companyId, taxId);
        // todo
        initGroomingAppointment(initDataParam, initResultData);

        return initResultData;
    }

    private void initGroomingAppointment(
            GroomingInitDataParam initDataParam, InitDataGroomingResultDto initResultData) {
        AppointmentParams appointment = new AppointmentParams();
        appointment.setId(null);
        appointment.setBusinessId(initDataParam.getBusinessId());
        appointment.setCompanyId(initDataParam.getCompanyId());
        appointment.setCreatedById(initDataParam.getOwnerStaffId());
        appointment.setAlertNotes("");
        appointment.setAppointmentDateString(DateUtil.getNowDateString());
        appointment.setAppointmentStartTime(540);
        appointment.setColorCode("#9b9b9b");
        appointment.setCustomerAddressId(0);
        appointment.setCustomerId(initDataParam.getCustomerId());
        appointment.setSource(GroomingAppointmentEnum.SOURCE_WEB);
        appointment.setTicketComments("");
        PetDetailParams petDetailParams = new PetDetailParams();
        petDetailParams.setPetId(initDataParam.getMiniPetId());
        petDetailParams.setScopeTypePrice(1);
        petDetailParams.setScopeTypeTime(1);
        petDetailParams.setServiceId(initResultData.getFullLargeServiceId());
        petDetailParams.setServicePrice(initResultData.getFullLargeServicePrice());
        petDetailParams.setServiceTime(initResultData.getFullLargeServiceTime());
        petDetailParams.setServiceType(initResultData.getFullLargeServiceType());
        petDetailParams.setStaffId(initDataParam.getOwnerStaffId());
        petDetailParams.setStar(false);
        petDetailParams.setStartTime(540);
        appointment.setPetServices(new ArrayList<>());
        appointment.getPetServices().add(petDetailParams);
        moeGroomingAppointmentService.addGroomingAppointment(appointment);
        AppointmentParams appointment2 = new AppointmentParams();
        appointment2.setId(null);
        appointment2.setBusinessId(initDataParam.getBusinessId());
        appointment2.setCompanyId(initDataParam.getCompanyId());
        appointment2.setCreatedById(initDataParam.getOwnerStaffId());
        appointment2.setAlertNotes("");
        appointment2.setAppointmentDateString(DateUtil.getNowDateString());
        appointment2.setAppointmentStartTime(780);
        appointment2.setColorCode("#000");
        appointment2.setCustomerAddressId(0);
        appointment2.setCustomerId(initDataParam.getCustomerId());
        appointment2.setSource(GroomingAppointmentEnum.SOURCE_WEB);
        appointment2.setTicketComments("");
        PetDetailParams petDetailParams2 = new PetDetailParams();
        petDetailParams2.setPetId(initDataParam.getMiniPetId());
        petDetailParams2.setScopeTypePrice(1);
        petDetailParams2.setScopeTypeTime(1);
        petDetailParams2.setServiceId(initResultData.getFullMediumServiceId());
        petDetailParams2.setServicePrice(initResultData.getFullMediumServicePrice());
        petDetailParams2.setServiceTime(initResultData.getFullMediumServiceTime());
        petDetailParams2.setServiceType(initResultData.getFullMediumServiceType());
        petDetailParams2.setStaffId(initDataParam.getOwnerStaffId());
        petDetailParams2.setStar(false);
        petDetailParams2.setStartTime(780);
        appointment2.setPetServices(new ArrayList<>());
        appointment2.getPetServices().add(petDetailParams2);
        moeGroomingAppointmentService.addGroomingAppointment(appointment2);
        AppointmentParams appointment3 = new AppointmentParams();
        appointment3.setId(null);
        appointment3.setBusinessId(initDataParam.getBusinessId());
        appointment3.setCompanyId(initDataParam.getCompanyId());
        appointment3.setCreatedById(initDataParam.getOwnerStaffId());
        appointment3.setAlertNotes("");
        appointment3.setAppointmentDateString(DateUtil.getFormatStrForDate(DateUtil.getNextDateFew(1), "yyyy-MM-dd"));
        appointment3.setAppointmentStartTime(660);
        appointment3.setColorCode("#000");
        appointment3.setCustomerAddressId(0);
        appointment3.setCustomerId(initDataParam.getCustomerId());
        appointment3.setSource(GroomingAppointmentEnum.SOURCE_WEB);
        appointment3.setTicketComments("");
        PetDetailParams petDetailParams3 = new PetDetailParams();
        petDetailParams3.setPetId(initDataParam.getMiniPetId());
        petDetailParams3.setScopeTypePrice(2);
        petDetailParams3.setScopeTypeTime(2);
        petDetailParams3.setServiceId(initResultData.getFullSmallServiceId());
        petDetailParams3.setServicePrice(initResultData.getFullSmallServicePrice());
        petDetailParams3.setServiceTime(initResultData.getFullSmallServiceTime());
        petDetailParams3.setServiceType(initResultData.getFullSmallServiceType());
        petDetailParams3.setStaffId(initDataParam.getOwnerStaffId());
        petDetailParams3.setStar(false);
        petDetailParams3.setStartTime(660);
        appointment3.setPetServices(new ArrayList<>());
        appointment3.getPetServices().add(petDetailParams3);
        moeGroomingAppointmentService.addGroomingAppointment(appointment3);
    }

    private void initServiceCategory0(Integer businessId, Long companyId, Integer taxId) {
        // save service
        MoeGroomingService record1 = new MoeGroomingService();
        record1.setBusinessId(businessId);
        record1.setCompanyId(companyId);
        record1.setCategoryId(0);
        record1.setName("Extra care");
        record1.setDescription("");
        record1.setType(ServiceEnum.TYPE_ADD_ONS);
        record1.setTaxId(taxId);
        record1.setPrice(new BigDecimal("10.0000"));
        record1.setDuration(15);
        record1.setSort(45);
        record1.setColorCode("#7ed321");
        record1.setCreateTime(DateUtil.get10Timestamp());
        record1.setUpdateTime(DateUtil.get10Timestamp());
        moeGroomingServiceMapper.insertSelective(record1);
        MoeGroomingService record2 = new MoeGroomingService();
        record2.setBusinessId(businessId);
        record2.setCompanyId(companyId);
        record2.setCategoryId(0);
        record2.setName("Nail painting");
        record2.setDescription("");
        record2.setType(ServiceEnum.TYPE_ADD_ONS);
        record2.setTaxId(taxId);
        record2.setPrice(new BigDecimal("10.0000"));
        record2.setDuration(15);
        record2.setSort(46);
        record2.setColorCode("#7ed321");
        record2.setCreateTime(DateUtil.get10Timestamp());
        record2.setUpdateTime(DateUtil.get10Timestamp());
        moeGroomingServiceMapper.insertSelective(record2);
        MoeGroomingService record3 = new MoeGroomingService();
        record3.setBusinessId(businessId);
        record3.setCompanyId(companyId);
        record3.setCategoryId(0);
        record3.setName("Special Shampoo");
        record3.setDescription("Your pet will have a wonderful time here.");
        record3.setType(ServiceEnum.TYPE_ADD_ONS);
        record3.setTaxId(taxId);
        record3.setPrice(new BigDecimal("5.0000"));
        record3.setDuration(0);
        record3.setSort(47);
        record3.setColorCode("#7ed321");
        record3.setCreateTime(DateUtil.get10Timestamp());
        record3.setUpdateTime(DateUtil.get10Timestamp());
        moeGroomingServiceMapper.insertSelective(record3);
    }

    private void initServiceCategory1(
            Integer businessId, Long companyId, Integer taxId, InitDataGroomingResultDto initResultData) {
        // save category
        MoeGroomingServiceCategory groomingServiceCategory = new MoeGroomingServiceCategory();
        groomingServiceCategory.setBusinessId(businessId);
        groomingServiceCategory.setCompanyId(companyId);
        groomingServiceCategory.setName("Grooming service");
        groomingServiceCategory.setSort(2);
        groomingServiceCategory.setType(ServiceEnum.TYPE_SERVICE);
        groomingServiceCategory.setCreateTime(DateUtil.get10Timestamp());
        groomingServiceCategory.setUpdateTime(DateUtil.get10Timestamp());
        serviceCategoryMapper.insertSelective(groomingServiceCategory);
        // save service
        MoeGroomingService record1 = new MoeGroomingService();
        record1.setBusinessId(businessId);
        record1.setCompanyId(companyId);
        record1.setCategoryId(groomingServiceCategory.getId());
        record1.setName("Full Service - large");
        record1.setDescription("Your pet will have a wonderful time here.");
        record1.setType(ServiceEnum.TYPE_SERVICE);
        record1.setTaxId(taxId);
        record1.setPrice(new BigDecimal("120.0000"));
        record1.setDuration(90);
        record1.setSort(1);
        record1.setColorCode("#FF4382");
        record1.setCreateTime(DateUtil.get10Timestamp());
        record1.setUpdateTime(DateUtil.get10Timestamp());
        moeGroomingServiceMapper.insertSelective(record1);
        initResultData.setFullLargeServiceId(record1.getId());
        initResultData.setFullLargeServicePrice(record1.getPrice());
        initResultData.setFullLargeServiceTime(record1.getDuration());
        initResultData.setFullLargeServiceType(record1.getType().intValue());
        MoeGroomingService record2 = new MoeGroomingService();
        record2.setBusinessId(businessId);
        record2.setCompanyId(companyId);
        record2.setCategoryId(groomingServiceCategory.getId());
        record2.setName("Full service - medium");
        record2.setDescription("Your pet will have a wonderful time here.");
        record2.setType(ServiceEnum.TYPE_SERVICE);
        record2.setTaxId(taxId);
        record2.setPrice(new BigDecimal("100.0000"));
        record2.setDuration(75);
        record2.setSort(2);
        record2.setColorCode("#FF4382");
        record2.setCreateTime(DateUtil.get10Timestamp());
        record2.setUpdateTime(DateUtil.get10Timestamp());
        moeGroomingServiceMapper.insertSelective(record2);
        initResultData.setFullMediumServiceId(record2.getId());
        initResultData.setFullMediumServicePrice(record2.getPrice());
        initResultData.setFullMediumServiceTime(record2.getDuration());
        initResultData.setFullMediumServiceType(record2.getType().intValue());
        MoeGroomingService record3 = new MoeGroomingService();
        record3.setBusinessId(businessId);
        record3.setCompanyId(companyId);
        record3.setCategoryId(groomingServiceCategory.getId());
        record3.setName("Full service - small");
        record3.setDescription("Your pet will have a wonderful time here.");
        record3.setType(ServiceEnum.TYPE_SERVICE);
        record3.setTaxId(taxId);
        record3.setPrice(new BigDecimal("90.0000"));
        record3.setDuration(60);
        record3.setSort(3);
        record3.setColorCode("#FF4382");
        record3.setCreateTime(DateUtil.get10Timestamp());
        record3.setUpdateTime(DateUtil.get10Timestamp());
        moeGroomingServiceMapper.insertSelective(record3);
        initResultData.setFullSmallServiceId(record3.getId());
        initResultData.setFullSmallServicePrice(record3.getPrice());
        initResultData.setFullSmallServiceTime(record3.getDuration());
        initResultData.setFullSmallServiceType(record3.getType().intValue());
    }

    private void initServiceCategory2(Integer businessId, Long companyId, Integer taxId) {
        // save category
        MoeGroomingServiceCategory groomingServiceCategory = new MoeGroomingServiceCategory();
        groomingServiceCategory.setBusinessId(businessId);
        groomingServiceCategory.setCompanyId(companyId);
        groomingServiceCategory.setName("Bath only");
        groomingServiceCategory.setSort(1);
        groomingServiceCategory.setType(ServiceEnum.TYPE_SERVICE);
        groomingServiceCategory.setCreateTime(DateUtil.get10Timestamp());
        groomingServiceCategory.setUpdateTime(DateUtil.get10Timestamp());
        serviceCategoryMapper.insertSelective(groomingServiceCategory);
        // save service
        MoeGroomingService record1 = new MoeGroomingService();
        record1.setBusinessId(businessId);
        record1.setCompanyId(companyId);
        record1.setCategoryId(groomingServiceCategory.getId());
        record1.setName("Bath - large");
        record1.setDescription("Your pet will have a wonderful time here.");
        record1.setType(ServiceEnum.TYPE_SERVICE);
        record1.setTaxId(taxId);
        record1.setPrice(new BigDecimal("80.0000"));
        record1.setDuration(90);
        record1.setSort(42);
        record1.setColorCode("#FF4382");
        record1.setCreateTime(DateUtil.get10Timestamp());
        record1.setUpdateTime(DateUtil.get10Timestamp());
        moeGroomingServiceMapper.insertSelective(record1);
        MoeGroomingService record2 = new MoeGroomingService();
        record2.setBusinessId(businessId);
        record2.setCompanyId(companyId);
        record2.setCategoryId(groomingServiceCategory.getId());
        record2.setName("Bath - medium");
        record2.setDescription("Your pet will have a wonderful time here.");
        record2.setType(ServiceEnum.TYPE_SERVICE);
        record2.setTaxId(taxId);
        record2.setPrice(new BigDecimal("60.0000"));
        record2.setDuration(60);
        record2.setSort(82);
        record2.setColorCode("#FF4382");
        record2.setCreateTime(DateUtil.get10Timestamp());
        record2.setUpdateTime(DateUtil.get10Timestamp());
        moeGroomingServiceMapper.insertSelective(record2);
        MoeGroomingService record3 = new MoeGroomingService();
        record3.setBusinessId(businessId);
        record3.setCompanyId(companyId);
        record3.setCategoryId(groomingServiceCategory.getId());
        record3.setName("Bath - small");
        record3.setDescription("Your pet will have a wonderful time here.");
        record3.setType(ServiceEnum.TYPE_SERVICE);
        record3.setTaxId(taxId);
        record3.setPrice(new BigDecimal("40.0000"));
        record3.setDuration(30);
        record3.setSort(83);
        record3.setColorCode("#FF4382");
        record3.setCreateTime(DateUtil.get10Timestamp());
        record3.setUpdateTime(DateUtil.get10Timestamp());
        moeGroomingServiceMapper.insertSelective(record3);
    }
}

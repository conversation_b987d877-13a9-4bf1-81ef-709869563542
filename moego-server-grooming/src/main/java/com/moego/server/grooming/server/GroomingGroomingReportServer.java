package com.moego.server.grooming.server;

import com.moego.server.grooming.api.IGroomingGroomingReportServiceBase;
import com.moego.server.grooming.dto.groomingreport.GroomingReportDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportPreviewDataDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportSettingDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportSummaryInfoDTO;
import com.moego.server.grooming.params.groomingreport.GetGroomingReportSummaryInfoParams;
import com.moego.server.grooming.params.groomingreport.GroomingIdListParams;
import com.moego.server.grooming.params.groomingreport.GroomingReportPreviewParams;
import com.moego.server.grooming.params.groomingreport.UpdateGroomingReportStatusParams;
import com.moego.server.grooming.service.MoeGroomingReportService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
public class GroomingGroomingReportServer extends IGroomingGroomingReportServiceBase {

    @Autowired
    private MoeGroomingReportService groomingReportService;

    @Override
    public GroomingReportSettingDTO getGroomingReportSetting(@RequestParam("businessId") Integer businessId) {
        return groomingReportService.getGroomingReportSetting(businessId, false);
    }

    @Override
    public GroomingReportPreviewDataDTO getGroomingReportPreviewData(
            @RequestParam("businessId") Integer businessId, @RequestBody GroomingReportPreviewParams params) {
        return groomingReportService.getPreviewDataForSendingEmail(businessId, params);
    }

    @Override
    public List<GroomingReportSummaryInfoDTO> getGroomingReportSummaryInfoList(
            @RequestBody GetGroomingReportSummaryInfoParams params) {
        return groomingReportService.getGroomingReportSummaryInfoList(params);
    }

    @Override
    public Boolean updateGroomingReportSentStatus(@RequestBody UpdateGroomingReportStatusParams params) {
        return groomingReportService.updateGroomingReportSentStatus(params);
    }

    @Override
    public GroomingReportSummaryInfoDTO getGroomingReportSummaryInfoByUuid(
            @RequestParam("uuid") String uuid, @RequestParam(value = "reportId", required = false) Integer reportId) {
        return groomingReportService.getGroomingReportSummaryInfo(uuid, reportId);
    }

    @Override
    public List<GroomingReportDTO> getGroomingReportList(GroomingIdListParams idListParams) {
        return groomingReportService.getGroomingReportListByGroomingIdList(
                idListParams.businessId(), idListParams.groomingIdList());
    }

    @Override
    public List<GroomingReportDTO> getGroomingReportListForClientApp(Integer appointmentId) {
        return groomingReportService.getGroomingReportRecordsForClientApp(appointmentId);
    }
}
